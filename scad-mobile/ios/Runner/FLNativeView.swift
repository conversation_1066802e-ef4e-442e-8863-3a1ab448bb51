//
//  FLNativeView.swift
//  Runner
//
//  Created by <PERSON><PERSON> on 10/02/2024.
//

import Flutter
import SwiftUI
import UIKit
import ArcGIS
import Toaster
import Combine
import Flutter

@available(iOS 15.0, *)
class FLNativeViewFactory: NSObject, FlutterPlatformViewFactory {
    
    
    private var messenger: FlutterBinaryMessenger
    
    init(messenger: FlutterBinaryMessenger) {
        self.messenger = messenger
        super.init()
    }
    
    func create(
        withFrame frame: CGRect,
        viewIdentifier viewId: Int64,
        arguments arg: Any?
    ) -> FlutterPlatformView {
        return FLNativeView(
            frame: frame,
            viewIdentifier: viewId,
            arguments: arg,
            binaryMessenger: messenger
        )
    }
    
    public func createArgsCodec() -> FlutterMessageCodec & NSObjectProtocol {
        return FlutterStandardMessageCodec.sharedInstance()
    }
}

@available(iOS 15.0, *)
class FLNativeView: NSObject, FlutterPlatformView {
    
    // MARK: - variables
    private var messageKey = "message_key"
    private var outMessageKeyValueMapReady = "map_ready"
    private var outMessageKeyValueApiResponse = "api_response"
    private let outMessageKeyValueDistrictSelected = "district_selected"
    private let inMessageKeyValueInitMap = "init_map"
    private let inMessageKeyValueCallApi = "call_api"
    private let inMessageKeyValueModuleChanged = "module_changed"
    private let inMessageKeyValueUpdateLayer = "update_layer"
    private let inMessageKeyValuePOI = "point_of_interest"
    private let inMessageKeyValueMeasurement = "measurement"
    private let outMessageKeyValueMeasurementResult = "measurement_result"
    private let inMessageKeyValueSelection = "selection"
    private let inMessageKeyValueClearMapSelection = "clear_map_selection"
    private let inMessageKeyValueHasSomethingToClearOnMap = "has_something_to_clear_on_map"
    private let outMessageKeyValueSelectionDone = "selection_done"
    private let outMessageKeyValueToast = "toast_message"
    
    private let eventChannelName = "arcgisMapEvent"
    private let methodChannelName = "arcgisMapMethod"

    private let keyCensus = "3";
    private let keyHouseHold = "2";
    private let keyPopulationByRegion = "4";
    private let keyJobSeekers = "1";
    private let keyRealEstateFlatTransaction = "5";
    private let keyRealEstateCensus = "6";

    private var eventHandler: EventHandler = EventHandler()
    private var activeMapLayerList: [String] = []
    private var mapViewProxy: MapViewProxy?
    private var map = Map();
    private var mapView: MapView = MapView(map: Map());
    private var _view: UIView
    private var geometryEditor: GeometryEditor = GeometryEditor()
    
    private var graphicsOverlay: GraphicsOverlay = GraphicsOverlay()
    init(
        frame: CGRect,
        viewIdentifier viewId: Int64,
        arguments args: Any?,
        binaryMessenger messenger: FlutterBinaryMessenger?
    ) {
        _view = UIView()
        super.init()
        createNativeView(view: _view, arguments: args)
    }
    
    func view() -> UIView {
        return _view
    }
    
    func createNativeView(view _view: UIView, arguments args: Any?) {
        let keyWindows = UIApplication.shared.windows.first(where: { $0.isKeyWindow}) ?? UIApplication.shared.windows.first
        let topController = keyWindows?.rootViewController
        
        if let creationParams = args as? [String: Any] {
            let controller =  FlutterEventChannel(
                name: eventChannelName, binaryMessenger: topController as! FlutterBinaryMessenger
            )
            
            FlutterMethodChannel(name: methodChannelName, binaryMessenger: topController as! FlutterBinaryMessenger).setMethodCallHandler({
                (call: FlutterMethodCall, reslt: FlutterResult) -> Void in
                
                self.handleIncomingMessage(call: call,result: reslt)
            })
            
            controller.setStreamHandler(eventHandler)
            
            applyLicense(licenseString: creationParams["licenseString"] as? String ?? "")
            
            guard let baseMapPortalId = creationParams["baseMapPortalId"] as? String,
                  let accessToken = creationParams["token"] as? String else {
                return
            }
            
            let CustomArcGISChallengeHandler=CustomArcGISChallengeHandler(accessToken:accessToken)
            
            map = Map(
                item: PortalItem(
                    portal: Portal(
                        url: URL(string: "https://www.arcgis.com")!,
                        connection:  Portal.Connection.anonymous),
                    id: .init(baseMapPortalId)!
                )
                
            )
            
            
            //mapView.geometryEditor(geometryEditor)
            ArcGISEnvironment.authenticationManager.arcGISAuthenticationChallengeHandler = CustomArcGISChallengeHandler
            let vc = UIHostingController(
                rootView: SwiftUIView(
                    map: map,
                    districtFeatureLayer:districtFeatureLayer,
                    outMessageKeyValueMeasurementResult: outMessageKeyValueMeasurementResult,
                    handleIdentifyResults:handleIdentifyResultsMain,
                    geometryEditor:geometryEditor,
                    graphicsOverlay: graphicsOverlay
                    
                )
            )
            let swiftUiView = vc.view!
            swiftUiView.translatesAutoresizingMaskIntoConstraints = false
            
            topController!.addChild(vc)
            _view.addSubview(swiftUiView)
            
            NSLayoutConstraint.activate([
                swiftUiView.leadingAnchor.constraint(equalTo: _view.leadingAnchor),
                swiftUiView.trailingAnchor.constraint(equalTo: _view.trailingAnchor),
                swiftUiView.topAnchor.constraint(equalTo: _view.topAnchor),
                swiftUiView.bottomAnchor.constraint(equalTo: _view.bottomAnchor)
            ])
            
            vc.didMove(toParent: topController)
            
            
            eventHandler.callEventSink(
                messageType: outMessageKeyValueMapReady,
                dataHashMap: nil
            )
        }
    }
    
    // MARK: - function for applying license
    private func applyLicense(licenseString: String) {
        guard let licenseKey = LicenseKey(licenseString) else { return }
        do {
            try ArcGISEnvironment.setLicense(with: licenseKey)
        } catch {
            toast(message: "Unable to apply license", type: "error")
        }
    }
    
    
    // MARK: - function for showing toast messages
    private func toast(message: String, type: String = "success") {
        sendMessageToFlutter(
            messageType: outMessageKeyValueToast,
            dataHashMap: ["message": message,"type": type]
        )
    }
    
    // MARK: - function for adding map layer
    private  func addMapLayer(title: String, element: Layer) {
        do {
            print(title)
            if (title == "district") {
                activeMapLayerList.insert(title, at: 0)
                map.addOperationalLayers([element])
            } else {
                activeMapLayerList.append(title)
                map.addOperationalLayers([element])
            }
        } catch {
            toast(message:"Unable to add map layer", type: "error")
        }
    }
    
    // MARK: - function for removing map layer
    private func removeMapLayer(title: String) {
        guard var index = activeMapLayerList.firstIndex(of: title) else {
            return print("index is null \(String(describing: index))")
        }
        activeMapLayerList.remove(at: index)
        map.removeOperationalLayer(map.operationalLayers[index])
    }
    
    // MARK: - function for clearing map layers
    private func clearMapLayers() {
        
        do {
            if !map.operationalLayers.isEmpty {
                map.removeAllOperationalLayers()
                activeMapLayerList.removeAll()
                //                addMapLayer(title: "district", element: layer)
            }
        } catch {
            print("clearMapLayers error \(error)")
            toast(message: "Unable to clear Map Layers", type: "error")
        }
        
    }
    
    // MARK: - district layer load function
    private func loadLayerDistrict(url: String?) {
        
        if (url == nil){
            return
        }
        
        clearMapLayers();
        
        let stateOutlineSymbol = SimpleLineSymbol(
            style: .solid,
            color : UIColor(red: 0.50, green: 0.50, blue: 0.50, alpha: 1.00),
            width: 1
        )
        
        let layerRenderer = SimpleRenderer(
            symbol: SimpleFillSymbol(
                style: .solid,
                color: UIColor(red: 0.50, green: 0.50, blue: 0.50, alpha:0.33),
                outline: stateOutlineSymbol
            )
        )
        Task {
            do {
                let serviceFeatureTable = ServiceFeatureTable(url: URL(string: url ?? "")!)
                
                let queryParameters = QueryParameters()
                queryParameters.whereClause = "1=1"
                
                guard let featureQueryResult: FeatureQueryResult = try? await serviceFeatureTable.queryFeatures(
                    using: queryParameters, queryFeatureFields: .loadAll
                ) else {
                    toast(message: "Unable to load map data", type: "error")
                    print("loadDistricts: featureQueryResult == null")
                    return
                }
                
                let feature  = Array(featureQueryResult.features()).first
                
                let featureTable: FeatureTable? = feature?.table
                if (featureTable == nil) {
                    toast(message: "Unable to load map data", type: "error")
                    print("loadDistricts: featureQueryResult == null")
                } else {
                    
                    self.districtFeatureLayer = FeatureLayer(featureTable: featureTable!)
                    self.districtFeatureLayer?.renderer = layerRenderer
                    if(self.districtFeatureLayer != nil) {
                        addMapLayer(title: "District", element:self.districtFeatureLayer!)
                    }
                    
                    let attributesArray = Array(featureQueryResult.features()).map { $0.attributes }
                    let attributesJSONData = try? JSONSerialization.data(withJSONObject: attributesArray, options: [])
                    let attributesJSONString = String(data: attributesJSONData ?? Data(), encoding: .utf8) ?? ""
                    
                    let messageDict: [String: Any] = [
                        "key": "district_list",
                        "url": url ?? "",
                        "attributes": attributesJSONString
                    ]
                    
                    sendMessageToFlutter(
                        messageType: outMessageKeyValueApiResponse,
                        dataHashMap: messageDict
                    )
                    
                }
                
            } catch {
                toast(message: "Unable to load map data", type: "error")
                print("loadDistricts: featureQueryResult == null")
            }
        }
    }
    
    // MARK: - loadlayer function
    func loadLayer(title: String, url: String?, renderer: Renderer? = nil) async {
        guard let url = url else { return }
        
        do {
            if let featureTable = await query(url: url) {
                let featureLayer = FeatureLayer(featureTable: featureTable)
                featureLayer.renderer = renderer
                
                addMapLayer(title: title, element: featureLayer)
            } else {
                print("loadLayer: featureTable is nil")
                toast(message: "Unable to load map layer", type: "error")
            }
        } catch {
            print("Error loading map layer: \(error)")
            toast(message: "Unable to load map layer", type: "error")
        }
    }
    
    // MARK: - function for query
    func query(url: String) async -> FeatureTable? {
        do {
            var whereCondition = "1=1"
            let serviceFeatureTable = ServiceFeatureTable(url: URL(string: url)!)
            let queryParameters = QueryParameters()
            queryParameters.whereClause = whereCondition
            
            guard let featureQueryResult = try? await serviceFeatureTable.queryFeatures(
                using: queryParameters, queryFeatureFields: .loadAll
            ) else {
                print("query: featureQueryResult == nil")
                return nil
            }
            
            let feature = Array(featureQueryResult.features()).first
            
            let attributesArray = Array(featureQueryResult.features()).map { $0.attributes }
            
            let attributesJSONData = try? JSONSerialization.data(withJSONObject: attributesArray, options: [])
            
            let attributesJSONString = String(data: attributesJSONData ?? Data(), encoding: .utf8) ?? ""
            
            let messageDict: [String: Any] = [
                "url": url,
                "attributes": attributesJSONString
            ]
            
            sendMessageToFlutter(
                messageType: outMessageKeyValueApiResponse,
                dataHashMap: [
                    "url": url,
                    "attributes": attributesJSONString
                ]
            )
            
            return feature?.table
        } catch {
            print("Error in query \(error)")
            toast(message: "Unable to query",type: "error")
        }
        return nil
    }
    
    // MARK: - function for loading POI layer
    func loadPOILayer(title: String, url: String, filePath: String) {
        if !activeMapLayerList.contains(title) {
            let featureTable = ServiceFeatureTable(url: URL(string: url)!)
            let featureLayer = FeatureLayer(featureTable: featureTable)
            
            let image = UIImage(contentsOfFile: filePath)
            let pictureMarkerSymbol = PictureMarkerSymbol(image: image!)
            
            pictureMarkerSymbol.height = 18.0
            pictureMarkerSymbol.width = 18.0
            
            let renderer = SimpleRenderer(symbol: pictureMarkerSymbol)
            
            featureLayer.renderer = renderer
            
            addMapLayer(title: title, element: featureLayer)
        }
    }
    
    // MARK: - function to send messages to flutter
    private func sendMessageToFlutter(messageType: String, dataHashMap: [String: Any]?) {
        
        var hashMap: [String: Any] = [messageKey: messageType]
        
        if let dataHashMap = dataHashMap {
            hashMap.merge(dataHashMap, uniquingKeysWith: { (_, new) in new })
        }
        
        print("sendMessageToFlutter: \(String(describing: dataHashMap))")
        
        DispatchQueue.main.async {
            self.eventHandler.callEventSink(messageType: messageType, dataHashMap: dataHashMap)
        }
    }
    
    // MARK: - function to handle incoming messages from flutter
    private func handleIncomingMessage(call: FlutterMethodCall, result: FlutterResult) {
        
        print("handleIncomingMessage: method:\(call.method)\narguments:\(String(describing: call.arguments))")
        
        switch call.method {
            //        case inMessageKeyValueInitMap:
            //            if let arguments = call.arguments as? [String: Any],
            //               let districtLayer = arguments["district_layer"] as? String {
            //                loadLayerDistrict(url: districtLayer)
            //            }
            
        case inMessageKeyValueModuleChanged:
            districtFeatureLayer?.clearSelection()
            
            if let arguments = call.arguments as? [String: Any],
               let module = arguments["moduleKey"] as? String,
               let layerUrls = arguments["layerUrls"] as? [String: String],
               let districtLayerUrl = arguments["districtLayer"] as? String {
                
                switch module {
                case keyCensus:
                    if let populationLayerUrl = layerUrls["populationLayerUrl"] {
                        loadLayerDistrict(url: districtLayerUrl)
                        loadHouseholdPopulationLayer(populationLayerUrl: populationLayerUrl)
                    }
                    
                case keyHouseHold:
                    if let populationLayerUrl = layerUrls["populationLayerUrl"] {
                        loadLayerDistrict(url: districtLayerUrl)
                        loadHouseholdPopulationLayer(populationLayerUrl: populationLayerUrl)
                    }
                    
                case keyRealEstateCensus:
                     if let layerUrl = layerUrls["realEstateBuildingsOnlyForVisualization"] {

//                         var pointRenderer: SimpleRenderer = {
//                              return SimpleRenderer(
//                              SimpleMarkerSymbol(
//                                  style: .square,
//                                  color: .green,
//                                  size: 20
//                              ))
//                          }()

        let renderer = SimpleRenderer(symbol: SimpleMarkerSymbol(
                                                          style: .circle,
                                                          color: .black,
                                                          size: 4
                                                      ))

                         loadLayerDistrict(url: districtLayerUrl)
                         Task {
                             await loadLayer(title: "realEstateBuildingsOnlyForVisualization", url: layerUrl, renderer: renderer)
                         }
                     }

                case keyRealEstateFlatTransaction:
                    loadLayerDistrict(url: districtLayerUrl)
                    //                    if let realEstateDataLayerUrl = layerUrls["realEstateDataLayerUrl"] {
                    //                        loadRealEstateDataLayer(realEstateDataLayerUrl: realEstateDataLayerUrl)
                    //                    }
                    
                case keyJobSeekers:
                    if let greyShadeLayerUrl = layerUrls["greyShadeLayerUrl"] {
                        loadLayerDistrict(url: districtLayerUrl)
                        Task {
                            await loadLayer(title: "greyShadeLayerUrl", url: greyShadeLayerUrl)
                        }
                    }
                    
                default:
                    break
                }
            }
            
        case inMessageKeyValueUpdateLayer:
            if let arguments = call.arguments as? [String: Any],
               let module = arguments["moduleKey"] as? String,
               module == keyHouseHold,
               let data = arguments["data"] as? [String: Any],
               let showNonUae = data["showNonUae"] as? Bool,
               let showUae = data["showUae"] as? Bool {
                householdPopulationLayerUpdate(showNonUae: showNonUae, showUae: showUae)
            }
            
        case inMessageKeyValuePOI:
            guard let arguments = call.arguments as? [String: Any],
                  let data = arguments["poi"] as? [[String: Any]] else { return }
            
            do {
                try clearPoiLayers()
            } catch {
                print("Calling error in clearPoiLayers \(error)")
            }
            
            for poi in data {
                guard let title = poi["title"] as? String,
                      let url = poi["url"] as? String,
                      let filePath = poi["filePath"] as? String else {
                    continue
                }
                
                loadPOILayer(title: title, url: url, filePath: filePath)
            }
            
        case inMessageKeyValueMeasurement:
            guard let arguments = call.arguments as? [String: Any],
                  let event = arguments["event"] as? String,
                  let type = arguments["type"] as? String else {
                return
            }
            
            switch event {
            case "start":
                startMeasurement(type: type)
            case "stop":
                stopMeasurement(type: type.isEmpty ? "" : type)
            case "cancel":
                cancelSketch()
            case "clear":
                clearMeasurement()
            default:
                break
            }
            
        case inMessageKeyValueSelection:
            guard let arguments = call.arguments as? [String: Any],
                  let event = arguments["event"] as? String else {
                return
            }
            
            switch event {
            case "start":
                startSketch()
            case "done":
                stopSketch()
            case "cancel":
                cancelSketch()
            case "undo":
                undoSketch()
            case "redo":
                redoSketch()
            default:
                break
            }
            
        case inMessageKeyValueClearMapSelection:
            clearMapSelection()
            
        case inMessageKeyValueHasSomethingToClearOnMap:
            
            result(!graphicsOverlay.graphics.isEmpty)
            return
            
        default:
            print("invalid")
        }
    }
    
    // MARK: - clearMapSelection
    private func clearMapSelection(){
        districtFeatureLayer!.clearSelection()
        cancelSketch()
    }
    
    // MARK: - startSketch
    private func startSketch() {
        startGeometry(geometryType: Polygon.self)
    }
    
    // MARK: - stopSketch
    private func stopSketch() {
        var sketchGeometry: Geometry? = stopGeometry()
        if (sketchGeometry != nil) {
            let messageDict: [String: Any] = [
                "geometry": sketchGeometry!.toJSON(),
            ]
            
            sendMessageToFlutter(
                messageType: outMessageKeyValueSelectionDone,
                dataHashMap: messageDict
            )
        }
    }
    
    // MARK: - undoSketch
    private func undoSketch() {
        if(geometryEditor.canUndo) {
            geometryEditor.undo()
        } else {
            toast(message: "Cannot undo", type: "error")
        }
    }
    
    // MARK: - redoSketch
    private func redoSketch() {
        if(geometryEditor.canRedo) {
            geometryEditor.redo()
        } else {
            toast(message: "Cannot redo", type: "error")
        }
    }
    
    // MARK: - startMeasurement
    private func startMeasurement(type: String?) {
        if type == "area" {
            startGeometry(geometryType: Polygon.self)
        } else if type == "distance" {
            startGeometry(geometryType: Polyline.self)
        }
    }
    
    // MARK: - startGeometry
    private func startGeometry(geometryType: Geometry.Type) {
        
        geometryEditor.tool = VertexTool()
        geometryEditor.start(withType: geometryType)
    }
    
    // MARK: - stopMeasurement
    private func stopMeasurement(type: String) {
        var sketchGeometry: Geometry? = stopGeometry()
        
        if (sketchGeometry != nil) {
            measure(sketchGeometry: sketchGeometry!, type: type)
        }
    }
    
    // MARK: - stopGeometry
    func stopGeometry() -> Geometry? {
        
        var sketchGeometry = geometryEditor.geometry
        
        if(sketchGeometry == nil) {
            toast(message: "Error retrieving geometry", type: "error")
            return nil
        }
        
        if(sketchGeometry?.sketchIsValid == false) {
            toast(message: "Not a valid selection", type: "error")
            return nil
        }
        
        geometryEditor.stop()
        
        // create a graphic from the sketch editor geometry
        let graphic = Graphic(geometry: sketchGeometry)
        switch sketchGeometry {
        case is Polygon:
            graphic.symbol = fillSymbol
        case is Polyline:
            graphic.symbol = lineSymbol
        case is Point, is Multipoint:
            graphic.symbol = pointSymbol
        default:
            graphic.symbol = nil
        }
        
        // add the graphic to the graphics overlay
        graphicsOverlay.addGraphic(graphic)
        
        return sketchGeometry
    }
    
    // MARK: - measure
    private func measure(sketchGeometry: Geometry, type: String) {
        
        if type == "distance" {
            
            let jsonString = sketchGeometry.toJSON()
            if let jsonData = jsonString.data(using: .utf8),
               let jsonObject = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any],
               let pathsArray = jsonObject["paths"]  {
                let list = extractList(data: pathsArray)
                var distance = 0.0
                for i in 0..<(list.count - 1) {
                    let map1 = list[i]
                    let map2 = list[i + 1]
                    distance += measureDistance(x1: map1["x"]!, y1: map1["y"]!, x2: map2["x"]!, y2: map2["y"]!)
                }
                print("distance: \(distance)")
                
                let messageDict: [String: Any] = [
                    "type": type,
                    "distance": distance
                ]
                
                sendMessageToFlutter(
                    messageType: outMessageKeyValueMeasurementResult,
                    dataHashMap: messageDict
                )
                
            } else {
                print("Error: Failed to extract paths from sketchGeometry JSON")
            }
        } else if type == "area" {
            
            let area = GeometryEngine.geodeticArea(
                of: sketchGeometry,
                unit: AreaUnit.squareMeters,
                curveType: .geodesic
            )
            print("area: \(area)")
            
            let distance = GeometryEngine.length(of: sketchGeometry)
            print("distance: \(distance)")
            
            let messageDict: [String: Any] = [
                "type": type,
                "distance": distance,
                "area": area
            ]
            
            sendMessageToFlutter(
                messageType: outMessageKeyValueMeasurementResult,
                dataHashMap: messageDict
            )
        }
    }
    
    // MARK: - extractList
    private func extractList(data: Any) -> [[String: Double]] {
        if let dataArray = data as? [[Any]] {
            if dataArray.count == 1, let innerArray = dataArray.first {
                return extractList(data: innerArray)
            } else {
                var list: [[String: Double]] = []
                for item in dataArray {
                    if let innerArray = item as? [Any], innerArray.count >= 2 {
                        list.append([
                            "x": (innerArray[0] as? Double) ?? 0.0,
                            "y": (innerArray[1] as? Double) ?? 0.0
                        ])
                    }
                }
                return list
            }
        } else if let dataArray = data as? [Any] {
            return extractList(data: dataArray)
        } else {
            return []
        }
    }
    
    // MARK: - measureDistance
    private func measureDistance(x1: Double, y1: Double, x2: Double, y2: Double) -> Double {
        
        var point1 = Point(x: x1, y: y1, spatialReference: .wgs84)
        var point2 = Point(x: x2, y: y2, spatialReference: .wgs84)
        
        // Create a world equidistant cylindrical spatial reference for measuring planar distance.
        var equidistantSpatialRef = SpatialReference.wgs84
        
        // Project the points from geographic to the projected coordinate system.
        var edinburghProjected = GeometryEngine.project(point1, into: equidistantSpatialRef)
        var darEsSalaamProjected = GeometryEngine.project(point2, into: equidistantSpatialRef)
        
        if (edinburghProjected == nil || darEsSalaamProjected == nil) {
            return 0.0
        } else {
            
            // Get the planar distance between the points in the spatial reference unit (meters)
            var planarDistanceMeters = GeometryEngine.distance(from: edinburghProjected!, to: darEsSalaamProjected!)
            
            print("measureDistance: \(planarDistanceMeters)")
            
            return planarDistanceMeters;
        }
    }
    
    
    // MARK: - cancelSketch
    private func cancelSketch() {
        print("cancelSketch")
        graphicsOverlay.removeAllGraphics()
        geometryEditor.clearGeometry()
        geometryEditor.clearSelection()
        geometryEditor.stop()
    }
    
    // MARK: - clearMeasurement
    private func clearMeasurement() {
        print("clearMeasurement")
        graphicsOverlay.removeAllGraphics()
        geometryEditor.clearGeometry()
        geometryEditor.clearSelection()
    }
    
    // MARK: - create a symbol for a line graphic
    private lazy var lineSymbol: SimpleLineSymbol = {
        return SimpleLineSymbol(
            style: .solid,
            color: .red,
            width: 2
        )
    }()
    
    // MARK: - create a symbol for the fill graphic
    private lazy var fillSymbol: SimpleFillSymbol = {
        return SimpleFillSymbol(
            style: .solid,
            color: .clear,
            outline: lineSymbol
        )
    }()
    
    // MARK: - create a symbol for the point graphic
    private lazy var pointSymbol: SimpleMarkerSymbol = {
        return SimpleMarkerSymbol(
            style: .square,
            color: .green,
            size: 20
        )
    }()
    
    
    
    // MARK: - function to load population overview layer
    private func loadPopulationOverviewLayer(populationLayerUrl: String?) {
        DispatchQueue.global(qos: .background).async { [self] in
            Task {
                do {
                    clearMapLayers()
                    
                    guard let populationLayerUrl = populationLayerUrl,
                          let featureQueryResult = try? await getFeatureQueryResult(url: populationLayerUrl),
                          let feature = Array(featureQueryResult.features()).first,
                          let featureTable = feature.table else {
                        print("loadPopulationOverviewLayer: featureTable is null")
                        return
                    }
                    
                    let featureLayer = FeatureLayer(featureTable: featureTable)
                    populationOverviewLayerAdd(featureLayer: featureLayer, showNonUae: true, showUae: true)
                } catch {
                    toast(message: error.localizedDescription,  type: "error")
                    print("Unable to load layer: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - function to load household population layer
    private func loadHouseholdPopulationLayer(populationLayerUrl: String?) {
        Task {
            do {
                
                if(populationLayerUrl == nil) {
                    return
                }
                
                clearMapLayers()
                
                let featureQueryResult = try await getFeatureQueryResult(url:populationLayerUrl ?? "")
                
                let feature = Array(featureQueryResult.features())
                
                if(feature.isEmpty) {
                    print("loadHouseholdPopulationLayer: featureTable is empty")
                } else {
                    let serviceFeatureTable = ServiceFeatureTable(url: URL(string:populationLayerUrl ?? "")!)
                    let featureLayer = FeatureLayer(featureTable: serviceFeatureTable)
                    householdPopulationLayerAdd(
                        featureLayer: featureLayer,
                        showNonUae: true,
                        showUae: true
                    )
                }
            } catch {
                toast(message: error.localizedDescription, type: "error")
                print("Unable to load layer: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - function to load job seekers layer
    private func loadJobSeekersLayer(jobSeekersLayerUrl: String?) {
        DispatchQueue.global(qos: .background).async { [self] in
            Task {
                do {
                    clearMapLayers()
                    
                    guard let jobSeekersLayerUrl = jobSeekersLayerUrl,
                          let featureQueryResultJobSeekersLayer = try? await getFeatureQueryResult(url: jobSeekersLayerUrl) else {
                        print("loadJobSeekersLayer: featureQueryResultJobSeekersLayer is null")
                        return
                    }
                    
                    var a = Array(featureQueryResultJobSeekersLayer.features()).first?.table;
                    
                    if let featureTable = a {
                        addMapLayer(
                            title: "JobSeekers",
                            element: FeatureLayer(featureTable: featureTable)
                        )
                    }
                } catch {
                    toast(message: error.localizedDescription, type: "error")
                    print("Unable to load layer: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - function to load real estate layer
    private  func loadRealEstateDataLayer(realEstateDataLayerUrl: String?) {
        
        DispatchQueue.global().async { [self] in
            Task {
                do {
                    clearMapLayers()
                    
                    let realEstateDataLayerResult = try await getFeatureQueryResult(url: realEstateDataLayerUrl!)
                    
                    guard let feature = Array(realEstateDataLayerResult.features()).first,
                          let featureTable = feature.table else {
                        print("Real Estate Data layer feature table is nil")
                        return
                    }
                    
                    let featureLayer = FeatureLayer(featureTable: featureTable)
                    addMapLayer(title: "RealEstateData", element: featureLayer)
                    
                } catch {
                    toast(message: error.localizedDescription, type: "error")
                    print("Unable to load Real Estate Data layer: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - function to update household population layer
    private func householdPopulationLayerUpdate(showNonUae: Bool, showUae: Bool) {
        guard let index = activeMapLayerList.firstIndex(of: "householdPopulation"),
              let featureLayer = map.operationalLayers[index] as? FeatureLayer else {
            toast(message: "Unable to update layer",type: "error")
            return
        }
        
        removeMapLayer(title: activeMapLayerList[index])
        
        householdPopulationLayerAdd(featureLayer: featureLayer, showNonUae: showNonUae, showUae: showUae)
    }
    
    // MARK: - function getFeatureQueryResult
    private func getFeatureQueryResult(url: String, whereCondition: String = "1=1") async throws -> FeatureQueryResult {
        let serviceFeatureTable = ServiceFeatureTable(url: URL(string: url)!)
        let queryParameters = QueryParameters()
        queryParameters.whereClause = whereCondition
        
        do {
            let featureQueryResult = try await serviceFeatureTable.queryFeatures(
                using: queryParameters,
                queryFeatureFields: .loadAll
            )
            return featureQueryResult
        } catch {
            print("getFeatureQueryResult error \(error)")
            toast(message: "Unable to get Feature Query Result",type: "error")
            throw error
        }
    }
    
    // MARK: - function populationOverviewLayerAdd
    private func populationOverviewLayerAdd(featureLayer: FeatureLayer, showNonUae: Bool, showUae: Bool) {
        let symbolNonUae = SimpleMarkerSymbol(style: .circle, color: showNonUae ? .blue : .clear, size: 4)
        let symbolUae = SimpleMarkerSymbol(style: .circle, color: showUae ? .red : .clear, size: 4)
        
        let uniqueValue1 = UniqueValue(
            description: "total_non_citizen",
            label: "total_non_citizen",
            symbol: symbolNonUae,
            values: ["15260"]
        )
        let uniqueValue2 = UniqueValue(
            description: "total_citizen",
            label: "total_citizen",
            symbol: symbolUae,
            values: ["44016"]
        )
        
        let uniqueValueRenderer = UniqueValueRenderer(
            fieldNames: ["total_non_citizen", "total_citizen"],
            uniqueValues: [uniqueValue1, uniqueValue2]
        )
        
        featureLayer.renderer = uniqueValueRenderer
        
        addMapLayer(title: "populationOverview", element: featureLayer)
    }
    
    // MARK: - function householdPopulationLayerAdd
    private func householdPopulationLayerAdd(featureLayer: FeatureLayer, showNonUae: Bool, showUae: Bool) {
        
        do {
            let symbolNonUae = SimpleMarkerSymbol(
                style: .circle, color: showNonUae
                ? .init(red: 0.349, green: 0.306, blue: 0.870, alpha: 1.0)
                : .clear, size: 4.0
            )
            
            let symbolUae = SimpleMarkerSymbol(
                style: .circle, color: showUae
                ? .init(red: 0.843, green: 0.369, blue: 0.365, alpha: 1.0)
                : .clear, size: 4.0
            )
            
            let nonUaeUniqueValue = UniqueValue(
                description: "premise_citizenship",
                label: "premise_citizenship",
                symbol: symbolNonUae,
                values: ["NON_UAE"]
            )
            
            let uaeUniqueValue = UniqueValue(
                description: "premise_citizenship",
                label: "premise_citizenship",
                symbol: symbolUae,
                values: ["UAE"]
            )
            
            let uniqueValueRenderer = UniqueValueRenderer(
                fieldNames: ["premise_citizenship"],
                uniqueValues: [nonUaeUniqueValue, uaeUniqueValue]
            )
            
            featureLayer.renderer = uniqueValueRenderer
            
            addMapLayer(title: "householdPopulation", element: featureLayer)
        } catch {
            toast(message: "Unable to load layer: \(error.localizedDescription)", type: "error")
        }
    }
    
    private var districtFeatureLayer: FeatureLayer? = nil
    
    // The point indicating where to identify features.
    private var identifyPoint: CGPoint?
    
    
    func handleIdentifyResultsMain(_ results: [IdentifyLayerResult]) {
        // Get layer names and geoelement counts from the results.
        districtFeatureLayer?.clearSelection()
        let identifiedFeatures = results.flatMap { identifyLayerResult in
            identifyLayerResult.geoElements.compactMap { $0 as? Feature }
        }
        
        print("identifiedFeatures -> $\(identifiedFeatures)")
        // Select features on the provided districtFeatureLayer
        districtFeatureLayer?.selectFeatures(identifiedFeatures)
        
        // Extract attributes from the first feature and send message to Flutter
        if let firstFeature = identifiedFeatures.first {
            do {
                print("inside do")
                let jsonData = try JSONSerialization.data(withJSONObject: firstFeature.attributes, options: [])
                let jsonString = String(data: jsonData, encoding: .utf8)
                
                if let jsonString = jsonString {
                    let messageDict: [String: Any] = [
                        "attributes": jsonString
                    ]
                    print(messageDict)
                    sendMessageToFlutter(
                        messageType: outMessageKeyValueDistrictSelected,
                        dataHashMap: messageDict
                    )
                }
            } catch {
                print("Error converting attributes to JSON: \(error)")
            }
        }
    }
    // MARK: - clearPOILayers
    private func clearPoiLayers() {
        do {
            let tempList: [String] = activeMapLayerList.filter { $0.starts(with: "poi-") }
            
            for i in stride(from: tempList.count - 1, through: 0, by: -1) {
                if let index = activeMapLayerList.firstIndex(of: tempList[i]) {
                    map.removeOperationalLayer(map.operationalLayers[index])
                }
            }
            
            activeMapLayerList.removeAll { $0.starts(with: "poi-") }
        } catch {
            print("clearPoiLayers error \(error)")
            toast(message: "Unable to clear Poi Layers",type: "error")
        }
    }
}

// MARK: - MapView
@available(iOS 15.0, *)
struct SwiftUIView: View {
    
    private var districtFeatureLayer: FeatureLayer? = nil
    private var outMessageKeyValueMeasurementResult: String
    private var handleIdentifyResults: ([IdentifyLayerResult]) -> Void
    private var geometryEditor: GeometryEditor
    private var viewpoint = Viewpoint(
        latitude : 24.43 ,
        longitude : 54.37,
        scale : 179757.5
    )
    private var map: Map;
    private var graphicsOverlay: GraphicsOverlay
    init(
        map: Map,
        districtFeatureLayer: FeatureLayer?,
        outMessageKeyValueMeasurementResult: String,
        handleIdentifyResults: @escaping ([IdentifyLayerResult]) -> Void,
        geometryEditor: GeometryEditor,
        graphicsOverlay: GraphicsOverlay
    ) {
        self.map = map
        self.graphicsOverlay = graphicsOverlay
        self.districtFeatureLayer = districtFeatureLayer
        self.outMessageKeyValueMeasurementResult = outMessageKeyValueMeasurementResult
        self.handleIdentifyResults = handleIdentifyResults
        self.geometryEditor = geometryEditor
        
    }
    
    @State var screenCoordinate: CGPoint?
    
    var body: some View {
        
        MapViewReader { proxy in
            MapView(map:map,viewpoint: viewpoint,   graphicsOverlays:[graphicsOverlay]).onSingleTapGesture { screenPoint, _ in
                screenCoordinate = screenPoint
            }
            .geometryEditor(geometryEditor)
            .selectionColor(.green)
            .task(id: screenCoordinate){
                if let screenPoint = screenCoordinate,
                   let results = try? await proxy.identifyLayers(
                    screenPoint: screenPoint,
                    tolerance: 0.0,
                    returnPopupsOnly: false,
                    maximumResultsPerLayer: -1
                   ) {
                    handleIdentifyResults(results)
                }
            }
        }
    }
}

// MARK: - EventHandler class
class EventHandler: NSObject, FlutterStreamHandler {
    
    var eventSink: FlutterEventSink? = nil
    
    var timer: Timer? = Timer()
    
    func callEventSink(messageType: String, dataHashMap: [String: Any]?) {
        var hashMap: [String: Any] = ["message_key": messageType]
        
        if let dataHashMap = dataHashMap {
            hashMap.merge(dataHashMap) { (_, new) in new }
        }
        
        if timer == nil || !timer!.isValid {
            self.timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true, block: { _ in
                if(self.eventSink != nil) {
                    self.eventSink?(hashMap)
                    self.timer?.invalidate()
                    self.timer = nil
                }
            })
        }
    }
    
    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        eventSink = events
        return nil
    }
    
    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        eventSink = nil
        timer?.invalidate()
        timer = nil
        return nil
    }
}

// MARK: - arcGISChallengeHandler
final class CustomArcGISChallengeHandler: ArcGISAuthenticationChallengeHandler {
    var accessToken: String = ""
    public init(accessToken:  String = "") {
        self.accessToken = accessToken
    }
    
    func handleArcGISAuthenticationChallenge(
        _ challenge: ArcGISAuthenticationChallenge
    ) async throws -> ArcGISAuthenticationChallenge.Disposition {
        let tokenInfo = TokenInfo(
            accessToken: accessToken,
            expirationDate: Date().addingTimeInterval(8 * 60 * 60),
            isSSLRequired: true
        )
        print("validaiton \(accessToken) validation")
        let preGeneratedTokenCredential = PregeneratedTokenCredential(
            url: challenge.requestURL,
            tokenInfo: tokenInfo!
        )
        return .continueWithCredential(preGeneratedTokenCredential)
        
    }
}
