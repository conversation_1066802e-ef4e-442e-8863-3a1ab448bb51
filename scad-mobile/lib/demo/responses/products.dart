part of '../demo_api_responses.dart';

final productAccessResponse = {
  'analyticalAppsCatogories': [
    {'name': 'Scenario Drivers', 'key': 'scenario_drivers'},
    {'name': 'Forecasts', 'key': 'forecasts'},
    {'name': 'Dashboard', 'key': 'dashboard'},
    {'name': 'Insights Discovery', 'key': 'insights_discovery'},
  ],
  'spatialAnalytics': <JSONObject>[],
};

final demoProductsResponse = [
  {
    'id': '441',
    'name': 'Scenario Drivers',
    'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-05/Scenario%20Drivers_0.svg',
    'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-05/Scenario%20Drivers.svg',
    'node_count': 5,
    'isSelected': false,
    'domains': [
      {
        'id': '575',
        'name': 'Economy',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/economy-white_1.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/economy-black_0.svg',
        'items': [
          {
            'id': '4857',
            'type': 'What If',
            'content_type': 'analytical-apps',
            'title': 'What If Analysis: GDP, Inflation and Unemployment',
            'subTitle': 'What If Analysis: GDP, Inflation and Unemployment',
            'domain': 'Economy',
            'category': 'Scenario Drivers',
          },
          {
            'id': '2143',
            'type': 'Internal',
            'content_type': 'analytical-apps',
            'title': 'What if analysis: quarterly non oil economic activity indicator(constant)',
            'subTitle': 'WHAT IF ANALYSIS: QUARTERLY NON OIL ECONOMIC ACTIVITY INDICATOR(CONSTANT)',
            'domain': 'Economy',
            'category': 'Scenario Drivers',
          },
          {
            'id': '617',
            'type': 'Internal',
            'content_type': 'analytical-apps',
            'title': 'Economic Activity Index Forecasting Model by Sector (Constant GDP)',
            'subTitle': 'Index based on 2014 as a reference year (2014=100)',
            'domain': 'Economy',
            'category': 'Scenario Drivers',
          },
          {
            'id': '610',
            'type': 'Internal',
            'content_type': 'analytical-apps',
            'title': 'Quarterly Forecast for Non-Oil Current GDP',
            'subTitle':
                'The line chart displays the current GDP historical figure sand one subsequent quarter forecast as per the inputs provided by change drivers and selected time period. Base year 2014=100',
            'domain': 'Economy',
            'category': 'Scenario Drivers',
          }
        ],
      },
      {
        'id': '579',
        'name': 'Labour Force',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/labour-force-svgrepo-white.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/labour-force.svg',
        'items': [
          {
            'id': '17',
            'type': 'Internal',
            'content_type': 'analytical-apps',
            'title': 'What If Analysis: Abu Dhabi Population',
            'subTitle':
                'Population projection estimates projections and scenarios for the Emirate of Abu Dhabi based on select socio-economic drivers',
            'domain': 'Labour Force',
            'category': 'Scenario Drivers',
          }
        ],
      }
    ],
  },
  {
    'id': '438',
    'name': 'Dashboard',
    'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-05/Dashboard_1.svg',
    'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-05/Dashboard_0.svg',
    'node_count': 19,
    'isSelected': false,
    'domains': [
      {
        'id': '3198',
        'name': 'Social Statistics',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/people-community-svgrepo-com-white_0.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/people-community-svgrepo-com_0.svg',
        'items': [
          {
            'id': '4797',
            'type': 'Tableau-Internal',
            'content_type': 'analytical-apps',
            'title': 'Education Dashboard',
            'subTitle': 'Education Dashboard',
            'domain': 'Social Statistics',
            'category': 'Dashboard',
          }
        ],
      },
      {
        'id': '579',
        'name': 'Labour Force',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/labour-force-svgrepo-white.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/labour-force.svg',
        'items': [
          {
            'id': '4791',
            'type': 'Tableau-Internal',
            'content_type': 'analytical-apps',
            'title': 'Job Seeker',
            'subTitle': 'Job Seeker',
            'domain': 'Labour Force',
            'category': 'Dashboard',
          },
          {
            'id': '3187',
            'type': 'Tableau-Internal',
            'content_type': 'analytical-apps',
            'title': 'Labor Force Dashboard',
            'subTitle': 'Labor Force Dashboard',
            'domain': 'Labour Force',
            'category': 'Dashboard',
          }
        ],
      },
      {
        'id': '575',
        'name': 'Economy',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/economy-white_1.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/economy-black_0.svg',
        'items': [
          {
            'id': '4790',
            'type': 'Tableau-Internal',
            'content_type': 'analytical-apps',
            'title': 'Foreign Trade',
            'subTitle': 'Foreign Trade',
            'domain': 'Economy',
            'category': 'Dashboard',
          },
          {
            'id': '4789',
            'type': 'Tableau-Internal',
            'content_type': 'analytical-apps',
            'title': 'Annual Economic Survey',
            'subTitle': 'Annual Economic Survey',
            'domain': 'Economy',
            'category': 'Dashboard',
          },
          {
            'id': '4788',
            'type': 'Tableau-Internal',
            'content_type': 'analytical-apps',
            'title': 'PMI Dashboard',
            'subTitle': 'PMI Dashboard',
            'domain': 'Economy',
            'category': 'Dashboard',
          },
          {
            'id': '1515',
            'type': 'Tableau-Internal',
            'content_type': 'analytical-apps',
            'title': 'ABU DHABI PURCHASING MANAGERS INDEX (PMI)',
            'subTitle':
                'This analytical app is based on IHS Markit data for Abu Dhabi, Dubai and UAE. This tool is intended as an insight discovery app to analyze the market conditions and economic trends.',
            'domain': 'Economy',
            'category': 'Dashboard',
          }
        ],
      },
      {
        'id': '574',
        'name': 'Population & Demographic',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/population-white_0.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/population-black_0.svg',
        'items': [
          {
            'id': '4787',
            'type': 'Tableau-Internal',
            'content_type': 'analytical-apps',
            'title': 'Population Dashboard',
            'subTitle': 'Population Dashboard',
            'domain': 'Population & Demographic',
            'category': 'Dashboard',
          }
        ],
      }
    ],
  },
  {
    'id': '440',
    'name': 'Insights Discovery',
    'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-05/Insights_0.svg',
    'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-05/Insights.svg',
    'node_count': 17,
    'isSelected': false,
    'domains': [
      {
        'id': '3198',
        'name': 'Social Statistics',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/people-community-svgrepo-com-white_0.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/people-community-svgrepo-com_0.svg',
        'items': [
          {
            'id': '4796',
            'type': 'Basket-Insights',
            'content_type': 'analytical-apps',
            'title': 'Family Basket of Goods',
            'subTitle': 'Family Basket of Goods',
            'domain': 'Social Statistics',
            'category': 'Insights Discovery',
          }
        ],
      },
      {
        'id': '575',
        'name': 'Economy',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/economy-white_1.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/economy-black_0.svg',
        'items': [
          {
            'id': '4660',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'Visa consumer spending 2.0',
            'subTitle':
                'The analytical app is based on Visa data. It is intended to work as an insights discovery tool to analyse consumer spending behavior and trends across various aspects.',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          },
          {
            'id': '4655',
            'type': 'ECI-Insights',
            'content_type': 'analytical-apps',
            'title': 'Economic Complexity Index',
            'subTitle': 'Economic Complexity Index',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          },
          {
            'id': '624',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'VISA CONSUMER SPENDING',
            'subTitle':
                'The analytical app is based on Visa data. It is intended to work as an insights discovery tool to analyse consumer spending behavior and trends across various aspects.',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          },
          {
            'id': '449',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'Consumer Spending (NI)',
            'subTitle':
                'The analytical app is based on Network International data for consumer spending. The tool is intended as an insight discovery app to analyze the consumer habits and better representation of the broader economy.',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          },
          {
            'id': '441',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'Basket of Goods (CPI)',
            'subTitle':
                'The analytical app is based on CPI data for Abu Dhabi’s Economy. The tool is intended as an insight discovery app to analyze the consumer habits and better representation of the broader economy.',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          },
          {
            'id': '409',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'Abu Dhabi Purchasing Managers Index',
            'subTitle':
                'The analytical app is based on IHS Markit data for Abu Dhabi’s Economy adjusted PMI index. The tool is intended as an insight discovery app to analyze the market conditions and economic trends in the manufacturing and service sectors.',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          },
          {
            'id': '384',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'Abu Dhabi Awarded Projects- Insights Discovery',
            'subTitle':
                'The analytical app is based on MEED data for awarded projects. The app considers awarded year as the map time variable. The tool is intended as a insight discovery app to analyze the number and net value of projects awarded over the years',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          },
          {
            'id': '373',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'Real Estate Analysis (Sale) - Insights Discovery',
            'subTitle':
                'The analytical app is based on Real Estate Market data. The data has been gathered from Dubizzle, PropertyFinder and Bayut. It is intended to work as an insights discovery tool to analyze real estate price/sqft and trends across various aspects.',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          },
          {
            'id': '364',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'Real Estate Market Analyis (Rent) - Insights Discovery ',
            'subTitle':
                'The analytical app is based on Real Estate Market data. The data has been gathered from Dubizzle, PropertyFinder and Bayut. It is intended to work as an insights discovery tool to analyze real estate price/sqft and trends across various aspects.',
            'domain': 'Economy',
            'category': 'Insights Discovery',
          }
        ],
      },
      {
        'id': '3199',
        'name': 'Industry & Business',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/industry-svgrepo-white_1.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/industry-svgrepo-com_1.svg',
        'items': [
          {
            'id': '598',
            'type': 'insights-discovery',
            'content_type': 'analytical-apps',
            'title': 'STR - Insights Discovery',
            'subTitle':
                "The analytical app is based on Hotel Industry data. It provides an important performance indicators and metrics on hotels across selected cities.STR stands for Smith Travel Research, a hospitality analytics firm founded in 1985. They collect the data from hotels who subscribed to STR service to observe the performance metrics of their competitors. The genius part of STR's model is that these hotel customers also contribute their own performance metrics with STR which then packages and resells them to other competitors and investors. So readers of this dashboard should bear in mind that the following data doesn't cover the whole scope of the hotels in the listed cities.  Please visit for more information: https://hoteltechreport.com/news/str-report",
            'domain': 'Industry & Business',
            'category': 'Insights Discovery',
          }
        ],
      }
    ],
  },
  {
    'id': '439',
    'name': 'Forecast',
    'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-05/Forecast_0.svg',
    'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-05/Forecast.svg',
    'node_count': 12,
    'isSelected': false,
    'domains': [
      {
        'id': '575',
        'name': 'Economy',
        'dark_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/economy-white_1.svg',
        'light_icon': 'http://35.154.226.231/web/sites/default/files/2023-07/economy-black_0.svg',
        'items': [
          {
            'id': '212',
            'type': 'Internal',
            'content_type': 'analytical-apps',
            'title': 'Economic Activity Indicator by Sector (GDP - Constant Price)',
            'subTitle': 'Economic Activity Indicator by Sector (GDP - Constant Price)',
            'domain': 'Economy',
            'category': 'Forecast',
          }
        ],
      }
    ],
  }
];

final tbDashboards = [
  {
    'uuid': '158176b3-8238-4511-8e00-118079b3daf9',
    'domain_id': '575',
    'domain_name': 'Economy',
    'domain_name_ar': 'الإحصاءات الاقتصادية',
    'name': 'National Accounts',
    'name_ar': 'National Accounts',
    'product_type': 'td',
  },
  {
    'uuid': 'cdcdf7b4-fe88-41ad-8bbe-b1e6823a230f',
    'domain_id': '575',
    'domain_name': 'Economy',
    'domain_name_ar': 'الإحصاءات الاقتصادية',
    'name': 'Private Sector',
    'name_ar': 'Private Sector',
    'product_type': 'td',
  }
];
