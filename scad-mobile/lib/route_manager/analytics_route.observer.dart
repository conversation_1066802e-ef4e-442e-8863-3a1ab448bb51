import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/config/firebase_config/firebase_config.dart';
import 'package:scad_mobile/src/features/home/<USER>/pages/home_navigation.dart';
import 'package:scad_mobile/src/services/kpi_log/kpi_log_service.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';

class AnalyticsRouteObserver extends AutoRouteObserver {
  factory AnalyticsRouteObserver() => _mInstance;

  AnalyticsRouteObserver._();

  static final _mInstance = AnalyticsRouteObserver._();

  static const _authenticationRoutes = {
    ForgotPasswordScreenRoute.name: 'Forgot Password',
    LoginPageRoute.name: 'Login',
    ResetPasswordScreenRoute.name: 'Reset Password',
  };

  static const _analyticsMap = <String, String>{
    //  Authentication routes
    ..._authenticationRoutes,

    //  Home routes
    HomePageRoute.name: 'Home',
    ThemesPageRoute.name: 'Domains',
    MyAppsLandingPageRoute.name: 'My Apps',
    ProductsRoute.name: 'Products',
    AskUsLandingPageRoute.name: 'Ask Us',
    CollectionIndicatorsRoute.name: 'My Apps Details',

    //  Details Page Routes
    OfficialExperimentalDetailsScreenRoute.name: 'Details - Official Experimental',
    InsightsDiscoveryDetailsScreenRoute.name: 'Details - Insights Discovery',
    ScenarioForecastDetailsScreenRoute.name: 'Details - Scenario Forecast',
    SelectComparableScreenRoute.name: 'Details - Select Comparable Indicators',
    CompareDetailsScreenRoute.name: 'Details - Compare',
    SelectComputableScreenRoute.name: 'Details - Select Computable Indicators',
    ComputeDetailsScreenRoute.name: 'Details - Compute',

    //  Drawer Item Routes
    GlossaryScreenRoute.name: 'Glossary',
    UserGuideScreenRoute.name: 'User Guide',
    DownloadHistoryScreenRoute.name: 'Download History',
    AboutThisAppScreenRoute.name: 'About This App',
    ContactUsScreenRoute.name: 'Contact Us',
    FeedbackScreenRoute.name: 'Feedback',
    TermsAndConditionsScreenRoute.name: 'Terms and Conditions',
    SettingsScreenRoute.name: 'Settings',

    //  Misc Routes
    DashboardWebViewPageRoute.name: 'Tableau Dashboard',
    NotificationListRoute.name: 'Notifications',
    SearchScreenRoute.name: 'Search',
    GenAiChatPageRoute.name: 'GenAI Chat',
    SpatialAnalyticsScreenRoute.name: 'Spatial Analytics',
    ChatWithSmeInboxPageRoute.name: 'Chat with SME',
    AppUpdateScreenRoute.name: 'App Update',
  };

  static void homeNavigationListener(int index) => _mInstance._onHomeNavigationChanged(index);

  void _logRoute(Route<dynamic>? route) {
    final screenClass = route?.settings.name;
    final screenName = _analyticsMap[screenClass];
    if (screenName == null) return;

    final extra = <String, Object>{};
    final args = route?.settings.arguments;
    String? nodeId;

    if (args is DashboardWebViewPageRouteArgs) {
      nodeId = args.uuid;
      extra.addAll({
        'title': args.title,
        'uuid': args.uuid,
      });
    }

    _setLog(screenName, screenClass, extra, nodeId);
  }

  Future<void> _setLog(
    String screenName,
    String? screenClass, [
    Map<String, Object>? extra,
    String? nodeId,
  ]) =>
      Future.wait(
        [
          FirebaseAnalyticsConfig.instance.logScreen(
            screenName,
            screenClass ?? screenName,
            extra: extra,
          ),
          if (!_authenticationRoutes.containsKey(screenClass))
            KpiLogService.instance.newSession(
              screenName,
              nodeId,
            ),
        ],
      );

  void _onHomeNavigationChanged(int index) {
    const homePages = [
      HomePageRoute.name,
      ThemesPageRoute.name,
      MyAppsLandingPageRoute.name,
      ProductsRoute.name,
      AskUsLandingPageRoute.name,
    ];

    final screenClass = homePages.elementAt(index);
    final screenName = _analyticsMap[screenClass]!;
    AppLog.info('AnalyticsRouteObserver: BOTTOM NAV CHANGE: $screenName');
    _setLog(screenName, screenClass);
  }

  //  - if [previousRoute] route is null, then likely the app is started
  //  - if [route] is null, then a new dialog or bottom sheet was opened, dialogs and bottom sheets are not
  //    handled via auto_router. this is why the issue
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    AppLog.info('AnalyticsRouteObserver: ROUTE PUSHED: '
        '${previousRoute?.settings.name} -> ${route.settings.name}');
    _logRoute(route);
  }

  //  - if [route] was null then a dialog or bottom sheet was popped. the right to handle is by configuring the
  //    the bottom sheet or dialogs in auto_router
  //  - if [previousRoute] is null then probably the route popped to another dialog, i.e., 2 dialogs were open, now 1 is open
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    AppLog.info('AnalyticsRouteObserver: ROUTE POPPED: '
        '${route.settings.name} -> ${previousRoute?.settings.name}');

    if (route.settings.name == null) {
      //  the route was popped from a bottom sheet or dialog box
      return;
    }

    final screenClass = previousRoute?.settings.name;
    if (screenClass == HomeNavigationRoute.name) {
      return _onHomeNavigationChanged(HomeNavigation.currentIndex);
    }

    final screenName = _analyticsMap[screenClass];
    if (screenName == null || _authenticationRoutes.containsKey(screenClass)) {
      return;
    }

    //  Unlike KPI-Logs, Firebase Analytics automatically handles this event.
    //  Only logging into KPI for route pop.
    KpiLogService.instance.newSession(screenName);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    AppLog.info('AnalyticsRouteObserver: ROUTE REPLACED: '
        '${oldRoute?.settings.name} -> ${newRoute?.settings.name}');
    _logRoute(newRoute);
  }
}
