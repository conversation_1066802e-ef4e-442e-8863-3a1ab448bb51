part of 'route_imports.dart';

class MaintenanceGuard extends AutoRouteGuard {
  @override
  Future<void> onNavigation(NavigationResolver resolver, StackRouter router) async {
    if (kDebugMode) {
      final disableSlaCheck = HiveUtilsPersistent.box.get(
        'disableSlaCheck',
        defaultValue: false,
      ) as bool;

      if (disableSlaCheck) {
        return resolver.next();
      }
    }

    unawaited(_checkMaintenance(resolver, router));
    resolver.next();
  }

  Future<void> _checkMaintenance(NavigationResolver resolver, StackRouter router) async {
    final response = await HomeRepositoryImpl().accessCheck();
    final data = response.response;
    if (!response.isSuccess || data == null) {
      return _safeResolve(resolver);
    }

    final isUnderMaintenance = data.isFullMaintenance;
    if (isUnderMaintenance) {
      unawaited(
        router.replaceAll([
          MaintenanceScreenRoute(data: data),
        ]),
      );
      return;
    }

    _safeResolve(resolver);
  }

  void _safeResolve(NavigationResolver resolver) {
    if (resolver.isResolved) return;
    resolver.next();
  }
}
