import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/notification/data/models/request/subscription_request.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/clear_notifications_response.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/notification_model.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/read_notification_response.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/remove_subscription_response.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/subscription_response.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/hive_utils/api_cache/api_cache.dart';

part 'notification_repository.dart';
