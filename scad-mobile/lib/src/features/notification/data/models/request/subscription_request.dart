class SubscriptionRequest {
  SubscriptionRequest({
    this.nodeId,
    this.appType,
    this.contentType,
  });
  SubscriptionRequest.fromJson(Map<String, dynamic> json)
      : nodeId = json['node_id'] as String?,
        appType = json['app_type'] as String?,
        contentType = json['content_type'] as String?;
  final String? nodeId;
  final String? appType;
  final String? contentType;

  Map<String, dynamic> toJson() => {
        'node_id': nodeId,
        'app_type': appType,
        'content_type': contentType,
      };
}
