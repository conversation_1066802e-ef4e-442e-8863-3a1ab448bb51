class SubscriptionResponse {
  SubscriptionResponse({
    this.status,
    this.message,
    this.uuid,
  });

  SubscriptionResponse.fromJson(Map<String, dynamic> json)
      : status = json['status'] as String?,
        message = json['message'] as String?,
        uuid = json['uuid'] as String?;
  final String? status;
  final String? message;
  final String? uuid;

  Map<String, dynamic> toJson() =>
      {'status': status, 'message': message, 'uuid': uuid};
}
