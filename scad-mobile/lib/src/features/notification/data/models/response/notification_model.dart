class NotificationListResponse {
  NotificationListResponse({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  NotificationListResponse.fromJson(Map<String, dynamic> json)
      : count = json['count'] as int?,
        next = json['next'] as String?,
        previous = json['previous'] as String?,
        results = (json['results'] as List?)
            ?.map(
              (dynamic e) =>
                  NotificationModel.fromJson(e as Map<String, dynamic>),
            )
            .toList();
  final int? count;
  final String? next;
  final String? previous;
  final List<NotificationModel>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results?.map((e) => e.toJson()).toList(),
      };
}

class NotificationModel {
  NotificationModel({
    this.contentName,
    this.contentDescription,
    this.contentNameAr,
    this.contentDescriptionAr,
    this.createdTime,
    this.isRead,
    this.mySubscription,
    this.uuid,
  });

  NotificationModel.fromJson(Map<String, dynamic> json)
      : contentName = json['content_name'] as String?,
        contentDescription = json['content_description'] as String?,
        contentNameAr = json['content_name_ar'] as String?,
        contentDescriptionAr = json['content_description_ar'] as String?,
        createdTime = json['created_time'] as String?,
        isRead = json['is_read'] as bool?,
        mySubscription =
            (json['my_subscription'] as Map<String, dynamic>?) != null
                ? MySubscription.fromJson(
                    json['my_subscription'] as Map<String, dynamic>,
                  )
                : null,
        uuid = json['uuid'] as String?;
  final String? contentName;
  final String? contentDescription;
  final String? contentNameAr;
  final String? contentDescriptionAr;
  final String? createdTime;
  bool? isRead;
  final MySubscription? mySubscription;
  final String? uuid;

  Map<String, dynamic> toJson() => {
        'content_name': contentName,
        'content_description': contentDescription,
        'content_name_ar': contentNameAr,
        'content_description_ar': contentDescriptionAr,
        'created_time': createdTime,
        'is_read': isRead,
        'my_subscription': mySubscription?.toJson(),
        'uuid': uuid,
      };
}

class MySubscription {
  MySubscription({
    this.nodeId,
    this.appType,
    this.contentType,
    this.uuid,
  });

  MySubscription.fromJson(Map<String, dynamic> json)
      : nodeId = json['node_id'] as String?,
        appType = json['app_type'] as String?,
        contentType = json['content_type'] as String?,
        uuid = json['uuid'] as String?;
  final String? nodeId;
  final String? appType;
  final String? contentType;
  final String? uuid;

  Map<String, dynamic> toJson() => {
        'node_id': nodeId,
        'app_type': appType,
        'content_type': contentType,
        'uuid': uuid,
      };
}
