class ReadNotificationResponse {
  ReadNotificationResponse({
    this.status,
    this.message,
    this.notificationUnread,
  });
  ReadNotificationResponse.fromJson(Map<String, dynamic> json)
      : status = json['status'] as String?,
        message = json['message'] as String?,
        notificationUnread = json['notification_unread'] as bool?;
  final String? status;
  final String? message;
  final bool? notificationUnread;

  Map<String, dynamic> toJson() => {
        'status': status,
        'message': message,
        'notification_unread': notificationUnread,
      };
}
