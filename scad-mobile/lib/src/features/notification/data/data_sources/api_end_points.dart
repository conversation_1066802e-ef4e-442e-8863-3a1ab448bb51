import 'package:scad_mobile/src/config/app_config/api_config.dart';

class NotificationsEndPoints {
  static String appPath = ApiConfig.appApiPath;
  static String ifpPath = ApiConfig.ifpApiPath;

  static String getMyNotificationsList =
      '$appPath/my-notification/list/?offset=0&limit=20&notification_filter={{filter}}';
  static String createMySubscription = '$appPath/my-subscription/create/';
  static String removeMySubscription = '$appPath/my-subscription/delete/';
  static String readNotification = '$appPath/my-notification/';
  static String clearAllNotification = '$appPath/my-notification/clear-all/';
}
