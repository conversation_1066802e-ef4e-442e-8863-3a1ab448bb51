import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/models/response_models/api_response.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/notification/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/notification/data/models/request/subscription_request.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/clear_notifications_response.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/notification_model.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/read_notification_response.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/remove_subscription_response.dart';
import 'package:scad_mobile/src/features/notification/data/models/response/subscription_response.dart';
import 'package:scad_mobile/src/features/notification/domain/repositories/notification_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/services/http_services.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class NotificationRepositoryImpl extends NotificationRepository {
  final _httpService = HttpServiceRequests();

  Future<void> _deleteIndicatorStatusCache(String indicatorId) {
    final cacheKey = getStaticCacheKey(
      HiveKeys.keyIndicatorStatus(indicatorId),
    );
    return deleteCache(cacheKey);
  }

  @override
  Future<RepoResponse<SubscriptionResponse>> createSubscription({
    required String indicatorId,
    required SubscriptionRequest payload,
  }) async {
    try {
      final String endpoint = NotificationsEndPoints.createMySubscription;
      final ApiResponse response = await _httpService.postJson(
        endpoint,
        jsonPayloadMap: payload.toJson(),
      );

      if (response.isSuccess) {
        await _deleteIndicatorStatusCache(indicatorId);
        return RepoResponse<SubscriptionResponse>.success(
          response: SubscriptionResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<SubscriptionResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<SubscriptionResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<NotificationListResponse>> myNotificationsList({
    String? nextPage,
    RequestParamsMap? requestParams,
  }) async {
    try {
      final String endpoint = NotificationsEndPoints.getMyNotificationsList;
      final ApiResponse response = await _httpService.get(
        nextPage ?? endpoint.setUrlParams(requestParams!),
      );

      if (response.isSuccess) {
        return RepoResponse<NotificationListResponse>.success(
          response: NotificationListResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<NotificationListResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<NotificationListResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<RemoveSubscriptionResponse>> removeSubscription({
    required String indicatorId,
    required String nodeId,
  }) async {
    try {
      final String endpoint = '${NotificationsEndPoints.removeMySubscription}$nodeId/';
      final ApiResponse response = await _httpService.delete(endpoint);

      if (response.isSuccess) {
        await _deleteIndicatorStatusCache(indicatorId);
        return RepoResponse<RemoveSubscriptionResponse>.success(
          response: RemoveSubscriptionResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<RemoveSubscriptionResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<RemoveSubscriptionResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<ReadNotificationResponse>> markAsReadNotification(
    bool isRead,
    String id,
  ) async {
    try {
      final String endpoint = '${NotificationsEndPoints.readNotification}$id/';
      final ApiResponse response = await _httpService.putJson(
        endpoint,
        jsonPayload: {'is_read': isRead},
      );

      if (response.isSuccess) {
        return RepoResponse<ReadNotificationResponse>.success(
          response: ReadNotificationResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<ReadNotificationResponse>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ReadNotificationResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<ClearNotificationResponse>> clearAllNotification() async {
    try {
      final String endpoint = NotificationsEndPoints.clearAllNotification;
      final ApiResponse response = await _httpService.postJson(
        endpoint,
      );

      if (response.isSuccess) {
        return RepoResponse<ClearNotificationResponse>.success(
          response: ClearNotificationResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<ClearNotificationResponse>.error(
          errorMessage: response.message,
        );
      }
    }catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<ClearNotificationResponse>.error(
        errorMessage:  LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
