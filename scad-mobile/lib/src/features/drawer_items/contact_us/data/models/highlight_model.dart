class HighlightResponseModel {
  HighlightResponseModel({this.highlightList});

  factory HighlightResponseModel.fromJson(Map<String, dynamic> json) {
    return HighlightResponseModel(
      highlightList: (json['data'] as List<dynamic>?)
          ?.map(
            (e) => HighlightModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
    );
  }

  List<HighlightModel>? highlightList;
}

class HighlightModel {
  HighlightModel({
    this.uuid,
    this.addedBy,
    this.editedBy,
    this.text,
    this.textAr,
    this.order,
    this.active,
    this.createdTime,
    this.updatedTime,
  });

  factory HighlightModel.fromJson(Map<String, dynamic> json) => HighlightModel(
        uuid: json['uuid'] as String?,
        addedBy: json['added_by'] == null
            ? null
            : AddedBy.fromJson(json['added_by'] as Map<String, dynamic>),
        editedBy: json['edited_by'] == null
            ? null
            : AddedBy.fromJson(json['edited_by'] as Map<String, dynamic>),
        text: json['text'] as String?,
        textAr: json['text_ar'] as String?,
        order: json['order'],
        active: json['active'] as bool?,
        createdTime: json['created_time'] as String?,
        updatedTime: json['updated_time'] as String,
      );

  String? uuid;
  AddedBy? addedBy;
  AddedBy? editedBy;
  String? text;
  String? textAr;
  dynamic order;
  bool? active;
  String? createdTime;
  String? updatedTime;

  Map<String, dynamic> toJson() => {
        'uuid': uuid,
        'added_by': addedBy?.toJson(),
        'edited_by': editedBy,
        'text': text,
        'text_ar': textAr,
        'order': order,
        'active': active,
        'created_time': createdTime,
        'updated_time': updatedTime,
      };
}

class AddedBy {
  AddedBy({
    this.uuid,
    this.name,
  });

  factory AddedBy.fromJson(Map<String, dynamic> json) => AddedBy(
        uuid: json['uuid'] as String?,
        name: json['name'] as String?,
      );

  String? uuid;
  String? name;

  Map<String, dynamic> toJson() => {
        'uuid': uuid,
        'name': name,
      };
}
