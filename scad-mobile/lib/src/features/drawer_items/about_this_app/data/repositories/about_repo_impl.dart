import 'dart:async';

import 'package:scad_mobile/demo/demo_api_responses.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/data_sources/api_end_points.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/models/about_app_model/response/about_app_list_response.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/domain/repositories/about_app_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';

class AboutAppRepositoryImpl extends AboutAppRepository {
  final _httpService = HttpServiceRequests();

  @override
  Future<RepoResponse<AboutAppListResponseModel>> aboutAppData() async {
    final endpoint = AboutAppEndPoints.aboutUs;
    final cacheKey = getCacheKey(endpoint);

    return fetchWithCache(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(
        AboutAppEndPoints.aboutUs,
      ),
      demoApiResponse: () => {'data': demoAboutUsResponse},
      parseResult: (json) => AboutAppListResponseModel.fromJson(json),
    );
  }
}
