import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/models/about_app_model/response/about_app_response.dart';

class AboutAppListResponseModel {
  AboutAppListResponseModel({
    this.aboutAppListResponse,
  });

  AboutAppListResponseModel.fromJson(JSONObject json)
      : aboutAppListResponse = (json['data'] as List<dynamic>?)
            ?.map(
              (e) => AboutAppResponseModel.fromJson(e as JSONObject),
            )
            .toList();

  List<AboutAppResponseModel>? aboutAppListResponse;

  JSONObject toJson() => {
        'data': aboutAppListResponse?.map((e) => e.toJson()).toList(),
      };
}
