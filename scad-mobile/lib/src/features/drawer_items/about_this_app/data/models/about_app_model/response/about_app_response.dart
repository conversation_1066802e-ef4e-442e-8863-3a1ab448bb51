import 'package:scad_mobile/src/common/types.dart';

class AboutAppResponseModel {
  AboutAppResponseModel({
    this.uuid,
    this.title,
    this.aboutContent,
    this.version,
    this.active,
    this.aboutContentAr,
    this.titleAr,
  });

  AboutAppResponseModel.fromJson(JSONObject json)
      : uuid = json['uuid'] as String?,
        title = json['title'] as String?,
        aboutContent = json['about_content'] as String?,
        version = json['version'] as String?,
        active = json['active'] as bool?,
        aboutContentAr = json['about_content_ar'] as String?,
        titleAr = json['title_ar'] as String?;

  String? uuid;
  String? title;
  String? titleAr;
  String? aboutContent;
  String? aboutContentAr;
  String? version;
  bool? active;

  JSONObject toJson() => {
        'uuid': uuid,
        'title': title,
        'title_ar': titleAr,
        'about_content': aboutContent,
        'about_content_ar': aboutContentAr,
        'version': version,
        'active': active,
      };
}
