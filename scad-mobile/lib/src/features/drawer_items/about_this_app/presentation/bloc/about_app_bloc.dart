import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/models/about_app_model/response/about_app_list_response.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/data/models/about_app_model/response/about_app_response.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/domain/repositories/about_app_repository_imports.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'about_app_event.dart';
part 'about_app_state.dart';

class AboutAppBloc extends Bloc<AboutAppEvent, AboutAppState> {
  AboutAppBloc(this.aboutAppRepository) : super(AboutDataLoadingState()) {
    on<AboutAppContentLoadingEvent>(_onLoadAboutContent);
  }

  final AboutAppRepository aboutAppRepository;

  Future<void> _onLoadAboutContent(
    AboutAppContentLoadingEvent event,
    Emitter<AboutAppState> emit,
  ) async {
    emit(AboutDataLoadingState());
    try {
      final RepoResponse<AboutAppListResponseModel> response =
          await servicelocator<AboutAppRepository>().aboutAppData();
      if (response.isSuccess) {
        emit(
          AboutDataSuccessState(
            aboutDataList: response.response?.aboutAppListResponse ?? [],
          ),
        );
      } else {
        emit(
          AboutDataFailureState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        AboutDataFailureState(error:  LocaleKeys.somethingWentWrong.tr()),
      );
    }
  }
}
