part of 'about_app_bloc.dart';

abstract class AboutAppState extends Equatable {
  const AboutAppState();

  @override
  List<Object> get props => [];
}

class AboutDataLoadingState extends AboutAppState {}

class AboutDataSuccessState extends AboutAppState {
  const AboutDataSuccessState({
    required this.aboutDataList,
  });

  final List<AboutAppResponseModel> aboutDataList;

  @override
  List<Object> get props => [aboutDataList];
}

class AboutDataFailureState extends AboutAppState {
  const AboutDataFailureState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}
