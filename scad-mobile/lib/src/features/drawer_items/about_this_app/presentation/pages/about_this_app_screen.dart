import 'package:auto_route/annotations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/drawer_items/about_this_app/presentation/bloc/about_app_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class AboutThisAppScreen extends StatefulWidget {
  const AboutThisAppScreen({super.key});

  @override
  State<AboutThisAppScreen> createState() => _AboutThisAppScreenState();
}

class _AboutThisAppScreenState extends State<AboutThisAppScreen> {
  ValueNotifier<String> title = ValueNotifier('');
  ValueNotifier<PackageInfo?> packageInfo = ValueNotifier(null);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _loadData();
    });
  }

  void _loadData() {
    context.read<AboutAppBloc>().add(const AboutAppContentLoadingEvent());
    PackageInfo.fromPlatform().then((value) {
      packageInfo.value = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            ValueListenableBuilder(
              valueListenable: title,
              builder: (context, value, _) {
                return FlatAppBar(
                  title: title.value,
                  bottomPadding: 0,
                );
              },
            ),
            Expanded(
              child: BlocConsumer<AboutAppBloc, AboutAppState>(
                listener: (context, state) {
                  if (state is AboutDataSuccessState) {
                    title.value = (HiveUtilsSettings.isLanguageArabic
                            ? state.aboutDataList.first.titleAr
                            : state.aboutDataList.first.title) ??
                        LocaleKeys.aboutThisApp.tr();
                  }
                },
                builder: (context, state) {
                  return Expanded(child: _body(state));
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _body(AboutAppState state) {
    if (state is AboutDataLoadingState) {
      return const Center(child: CircularProgressIndicator());
    } else if (state is AboutDataFailureState) {
      return Center(
        child: ErrorReloadPlaceholder(
          error: state.error,
          onReload: _loadData,
        ),
      );
    } else if (state is AboutDataSuccessState) {
      return state.aboutDataList.isEmpty
          ? const Center(child: NoDataPlaceholder())
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 24,
                      ),
                      child: HtmlWidget(
                        HiveUtilsSettings.isLanguageArabic
                            ? state.aboutDataList.firstOrNull?.aboutContentAr ?? ''
                            : state.aboutDataList.firstOrNull?.aboutContent ?? '',
                        textStyle: AppTextStyles.s14w4cblackShade4.copyWith(
                          color: HiveUtilsSettings.isLightMode ? AppColors.grey : AppColors.greyShade4,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                ValueListenableBuilder(
                  valueListenable: packageInfo,
                  builder: (context, i, w) {
                    return Text(
                      '${packageInfo.value?.appName ?? ''} | V${packageInfo.value?.version ?? ''}',
                      style: AppTextStyles.s14w4cblackShade4.copyWith(
                        color: HiveUtilsSettings.isLightMode ? AppColors.grey : AppColors.white,
                        fontSize: 12,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 30),
              ],
            );
    } else {
      return const SizedBox();
    }
  }
}
