import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/landing_top_button.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_list_response.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/ask_us_filter_button.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/chat_thread_list.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/empty_chat_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/landing/new_chat_bottom_sheet.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class AskUsThreadListPage extends StatefulWidget {
  const AskUsThreadListPage({
    required this.scrollController,
    required this.submitQuestionKey,
    required this.steps,
    super.key,
  });

  final ScrollController scrollController;

  final GlobalKey submitQuestionKey;
  final List<GlobalKey> steps;

  @override
  State<AskUsThreadListPage> createState() => _AskUsThreadListPageState();
}

class _AskUsThreadListPageState extends State<AskUsThreadListPage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final isLightMode = HiveUtilsSettings.isLightMode;

  List<ChatThread> _chatThreadList = [];
  int? _count;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      _loadChatThreads();
      widget.scrollController.addListener(_scrollListener);
    });
  }

  @override
  void dispose() {
    widget.scrollController.removeListener(_scrollListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
      listener: _chatWithSmeBlocListener,
      builder: (context, state) {
        if (state is GetChatThreadListLoadingState && (_chatThreadList.isEmpty || state.reset)) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (_errorMessage != null) {
          return ErrorReloadPlaceholder(
            error: _errorMessage ?? '',
            onReload: _loadChatThreads,
          );
        }

        return Column(
          children: [
            SizedBox(
              height: 2,
              child: state is NewChatThreadLoadingState ? const LinearProgressIndicator() : null,
            ),
            Flexible(
              child: SingleChildScrollView(
                controller: widget.scrollController,
                padding: const EdgeInsets.fromLTRB(24, 14, 24, 150),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocaleKeys.welcomeToAskUs.tr(),
                      style: AppTextStyles.s14w4cGrey.copyWith(
                        color: isLightMode ? AppColors.black : AppColors.greyShade4,
                        // fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _bulletPointText(LocaleKeys.welcomeToAskUsLine1.tr()),
                    const SizedBox(height: 2),
                    _bulletPointText(LocaleKeys.welcomeToAskUsLine2.tr()),
                    const SizedBox(height: 24),
                    if (_chatThreadList.isEmpty) ...[
                      submitNewQuestionButton(context),
                      const SizedBox(height: 24),
                      if (context.read<ChatWithSmeBloc>().selectedOption != AskUsFilterOptions.all)
                        const AskUsFilterButton(),
                      const SizedBox(height: 55),
                      const Center(
                        child: EmptyChatWidget(),
                      ),
                      const SizedBox(height: 65),
                    ],
                    if (_chatThreadList.isNotEmpty) ...[
                      submitNewQuestionButton(
                        context,
                        isSmallWidget: true,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        LocaleKeys.questionThreads.tr(),
                        style: TextStyle(
                          color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const AskUsFilterButton(),
                      const SizedBox(height: 16),
                      Stack(
                        children: [
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            padding: EdgeInsets.zero,
                            itemCount: _chatThreadList.length,
                            itemBuilder: (context, index) {
                              final chatData = _chatThreadList[index];
                              return ChatThreadListItem(
                                chatThread: chatData,
                                reloadChatThreadsFn: () {
                                  _resetList();
                                  _loadChatThreads();
                                },
                              );
                            },
                          ),
                          if (_chatThreadList.isNotEmpty && _isLoading)
                            Positioned(
                              bottom: 8,
                              child: SizedBox(
                                width: MediaQuery.sizeOf(context).width,
                                child: const Center(child: CircularProgressIndicator()),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _bulletPointText(String str) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '•',
          style: AppTextStyles.s14w4cGrey.copyWith(
            color: isLightMode ? AppColors.grey : AppColors.greyShade4,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            str,
            style: AppTextStyles.s14w4cGrey.copyWith(
              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
            ),
          ),
        ),
      ],
    );
  }

  void _chatWithSmeBlocListener(BuildContext context, ChatWithSmeState state) {
    if (state is GetChatThreadListSuccessState) {
      _count = state.data?.count ?? 0;
      _isLoading = false;

      if (state.reset) {
        _chatThreadList.clear();
      }

      _chatThreadList.addAll(
        (state.data?.results ?? [])
          ..removeWhere(
            (element) => _chatThreadList.any((element1) => element.uuid == element1.uuid),
          ),
      );
    }

    if (state is GetChatThreadListErrorState) {
      _errorMessage = state.error;
    }

    if (state is NewChatThreadSuccessState) {
      context.read<ChatWithSmeBloc>().add(
            ChatWithSmeLoadInboxEvent(
              chatThreadId: state.chatThreadId ?? '',
            ),
          );
      context
          .pushRoute(
        ChatWithSmeInboxPageRoute(
          chatThreadId: state.chatThreadId,
          title: state.subject,
          domain: state.domain,
          domainId: state.domainId,
          theme: state.theme,
          subTheme: state.subTheme,
          ticketId: state.ticketId,
          indicatorName: '',
          chatDisabled: state.chatDisabled,
          chatThreadClosed: state.chatThreadClosed,
        ),
      )
          .whenComplete(() {
        _resetList();
        _loadChatThreads();
      });
    }

    if (state is NewChatThreadErrorState) {
      AppMessage.showOverlayNotificationError(message: state.error);
    }
  }

  Widget submitNewQuestionButton(BuildContext context, {bool isSmallWidget = false}) {
    return IntroWidget(
      stepKey: widget.submitQuestionKey,
      stepIndex: 3,
      totalSteps: widget.steps.length,
      title: LocaleKeys.submitANewQuestion.tr(),
      description: LocaleKeys.startChatGuideDesc.tr(),
      arrowAlignment: Alignment.topRight,
      crossAxisAlignment: CrossAxisAlignment.center,
      targetBorderRadius: 20,
      arrowPadding: EdgeInsets.only(
        right: MediaQuery.sizeOf(context).width * 0.2,
        left: MediaQuery.sizeOf(context).width * 0.2,
        bottom: 10,
      ),
      targetPadding: const EdgeInsets.all(10),
      child: LandingTopButton(
        isSmallWidget: isSmallWidget,
        animatedIcon: isLightMode ? AnimationAsset.animationChatBubble : AnimationAssetDark.animationChatBubble,
        label: LocaleKeys.submitANewQuestion.tr(),
        onTap: () {
          showModalBottomSheet<dynamic>(
            backgroundColor: Colors.transparent,
            isScrollControlled: true,
            useRootNavigator: true,
            context: context,
            builder: (BuildContext context) {
              return const NewChatBottomSheet();
            },
          );
        },
      ),
    );
  }

  void _scrollListener() {
    if (widget.scrollController.position.pixels >= widget.scrollController.position.maxScrollExtent) {
      if ((_count != null && _chatThreadList.length >= _count!) || _isLoading) {
        return;
      }
      _loadChatThreads();
    }
  }

  void _loadChatThreads() {
    _errorMessage = null;
    _isLoading = true;
    context.read<ChatWithSmeBloc>().add(
          GetChatThreadListEvent(
            offset: _chatThreadList.length,
          ),
        );
  }

  void _resetList() {
    _chatThreadList = [];
    _count = null;
  }
}
