import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/appbar/slim_appbar.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ImageViewerScreen extends StatefulWidget {
  const ImageViewerScreen({this.url, this.filePath, super.key});

  final String? url;
  final String? filePath;

  @override
  State<ImageViewerScreen> createState() => _ImageViewerScreenState();
}

class _ImageViewerScreenState extends State<ImageViewerScreen> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, __) {
        if (!didPop) {
          context.back();
        }
      },
      child: Scaffold(
        body: Column(
          children: [
            const SlimAppBar(),
            Expanded(
              child: widget.filePath != null
                  ? Image.memory(File(widget.filePath!).readAsBytesSync())
                  : widget.url != null
                      ? Image.network(
                          widget.url!,
                          loadingBuilder: (_, __, ___) => const Center(
                            child: CircularProgressIndicator(),
                          ),
                          errorBuilder: (_, __, ___) => Center(
                            child: ErrorReloadPlaceholder(error: LocaleKeys.somethingWentWrong.tr()),
                          ),
                        )
                      : const SizedBox(),
            ),
          ],
        ),
      ),
    );
  }
}
