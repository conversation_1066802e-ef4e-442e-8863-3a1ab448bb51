import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/app_shimmer.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/dialogs/response_guide_dialog.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/inbox/end_of_chat_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/inbox/inbox_list.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/ticket_id_chip.dart';
import 'package:scad_mobile/src/features/details_page/base/helper/route_helper.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class ChatWithSmeInboxPage extends StatefulWidget {
  const ChatWithSmeInboxPage({
    this.title,
    this.domain,
    this.domainId,
    this.theme,
    this.subTheme,
    this.ticketId,
    this.indicatorNodeId,
    this.indicatorAppType,
    this.indicatorContentType,
    this.indicatorKey,
    this.indicatorName,
    this.chatThreadId,
    this.chatDisabled,
    this.chatThreadClosed,
    this.attachFile,
    this.isRead,
    super.key,
  });

  final String? title;
  final bool? isRead;
  final String? domain;
  final int? domainId;
  final String? theme;
  final String? subTheme;
  final String? ticketId;
  final String? indicatorNodeId;
  final String? indicatorAppType;
  final String? indicatorContentType;
  final String? indicatorKey;
  final String? indicatorName;
  final String? chatThreadId;
  final bool? chatDisabled;
  final bool? chatThreadClosed;
  final File? attachFile;

  @override
  State<ChatWithSmeInboxPage> createState() => _ChatWithSmeInboxPageState();
}

class _ChatWithSmeInboxPageState extends State<ChatWithSmeInboxPage> {
  final ScrollController scrollController = ScrollController();
  final TextEditingController chatController = TextEditingController();
  ValueNotifier<bool> dataQueryLinkPopupVisibility = ValueNotifier<bool>(true);

  List<MessageModel>? messageList = [];

  bool isSendingMessage = false;
  bool isLoading = false;
  bool isChatClosed = false;
  bool isChatDisabled = false;

  File? file;
  File? attachFile;

  static const String statisticalDataRequestUrl = 'https://www.scad.gov.ae/web/guest/w/statistical-data-request';

  @override
  void initState() {
    super.initState();

    // final isUnread = !(widget.isRead ?? true);
    // if (isUnread) {
    //   SchedulerBinding.instance.addPostFrameCallback(
    //     (duration) => ResponseGuideDialog.show(context),
    //   );
    // }

    attachFile = widget.attachFile;
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (appDrawerController.value.visible) {
          appDrawerController.hideDrawer();
        } else if (!didPop) {
          context.back();
        }
      },
      child: AppDrawer(
        child: Scaffold(
          body: GestureDetector(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Column(
              children: [
                FlatAppBar(
                  title: widget.title ?? '',
                  bottomPadding: 0,
                ),
                Expanded(
                  child: BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
                    listener: (context, state) {
                      if (state is ChatWithSmeSendFeedbackState) {
                        if (state.isSuccess) {
                          showSimpleNotification(
                            Container(
                              padding: const EdgeInsets.only(
                                top: 15,
                                left: 20,
                                right: 14,
                                bottom: 15,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFEAF7D9),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    LocaleKeys.feedbackSubmittedSuccessfully.tr(),
                                    style: const TextStyle(
                                      color: Color(0xFF4B7044),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SvgPicture.asset(
                                    'assets/images/green-circle-tick.svg',
                                  ),
                                ],
                              ),
                            ),
                            elevation: 0,
                            position: NotificationPosition.bottom,
                            background: Colors.transparent,
                          );
                          context.read<ChatWithSmeBloc>().add(
                                ChatWithSmeLoadInboxEvent(
                                  chatThreadId: widget.chatThreadId ?? '',
                                ),
                              );
                        }
                        isChatClosed = state.chatThreadClosed;
                      } else if (state is ChatWithSmeSendLoadingMessageState) {
                        isSendingMessage = state.isLoading ?? false;
                      } else if (state is ChatWithSmeSendMessageState) {
                        messageList = state.messageList;
                        isChatDisabled = state.chatDisabled ?? false;

                        file = null;
                        attachFile = null;
                        chatController.clear();
                        FocusManager.instance.primaryFocus?.unfocus();
                      } else if (state is ChatWithSmeAttachmentAddState) {
                        file = state.file;
                        attachFile = null;
                      } else if (state is ChatWithSmeInboxDataState) {
                        messageList = (state.messageList ?? []).isEmpty
                            ? [
                                MessageModel(
                                  message: 'Hi ${HiveUtilsAuth.getUserName}',
                                  uuid: 'admin',
                                  attachment: '',
                                ),
                                MessageModel(
                                  message: 'How can we help you today?',
                                  uuid: 'admin',
                                  attachment: '',
                                ),
                              ]
                            : state.messageList?.reversed.toList() ?? [];

                        if ((state.messageList ?? []).isEmpty) {
                          file = attachFile;
                        }
                        attachFile = null;
                        chatController.clear();

                        // messageList?.sort((a, b) {
                        //   if (a.createdTime == null || b.createdTime == null) return 0;
                        //   final aTime = DateTime.parse(a.createdTime!);
                        //   final bTime = DateTime.parse(b.createdTime ?? '');
                        //   return aTime.compareTo(bTime);
                        // });

                        // messageList?.forEach((e) {
                        //   print('_ChatWithSmeInboxPageState.build: ${e.createdTime}:'
                        //       ' ${e.sender?.uuid} -> ${e.recipient?.uuid} ${e.message}');
                        // });
                        // print('_ChatWithSmeInboxPageState.build: my uuid: ${HiveUtilsAuth.getUserId()}');

                        if (!(widget.chatThreadClosed ?? true)) {
                          final lastMessage = messageList?.lastOrNull;
                          if (lastMessage == null) return;

                          final isMyResponseMessage =
                              lastMessage.sender?.uuid == HiveUtilsAuth.getUserId() || lastMessage.feedback != null;
                          if (lastMessage.sender != null && !isMyResponseMessage && (widget.chatDisabled ?? false)) {
                            ResponseGuideDialog.show(context);
                          }
                        }
                      }
                    },
                    builder: (context, state) {
                      if (state is ChatWithSmeInboxLoadingState) {
                        return SizedBox(
                          height: MediaQuery.of(context).size.height * 0.9,
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      } else if (state is ChatWithSmeChatInboxErrorState) {
                        return SizedBox(
                          height: MediaQuery.of(context).size.height * 0.9,
                          child: Center(
                            child: NoDataPlaceholder(
                              msg: state.errorText,
                            ),
                          ),
                        );
                      }

                      return Stack(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 24),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: [
                                    if (widget.indicatorName == '') ...[
                                      Wrap(
                                        children: [
                                          Text(
                                            '${LocaleKeys.reference.tr()}:',
                                            style: const TextStyle(
                                              color: AppColors.greyShade4,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          _referenceItem(
                                            isLightMode,
                                            widget.domain,
                                            (widget.theme ?? '').isNotEmpty || (widget.subTheme ?? '').isNotEmpty,
                                          ),
                                          if ((widget.theme ?? '').isNotEmpty)
                                            _referenceItem(
                                              isLightMode,
                                              widget.theme,
                                              true,
                                            ),
                                          if ((widget.subTheme ?? '').isNotEmpty)
                                            _referenceItem(
                                              isLightMode,
                                              widget.subTheme,
                                              false,
                                            ),
                                        ],
                                      ),
                                    ],
                                    if (widget.indicatorName != '') ...[
                                      Row(
                                        children: [
                                          Text(
                                            '${LocaleKeys.reference.tr()}:',
                                            style: const TextStyle(
                                              color: AppColors.greyShade4,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          BlocConsumer<IndicatorCardBloc, IndicatorCardState>(
                                            listener: (context, state) {
                                              if (state is IndicatorDetailsSuccessState) {
                                                DetailsPageRouteHelper.indicatorDetailsPageRoute(context,
                                                    id: widget.indicatorNodeId ?? '',
                                                    contentType: widget.indicatorContentType ?? '',
                                                    title: state.indicatorDetails.componentTitle ?? '',
                                                    indicatorType: state.indicatorDetails.getIndicatorType());
                                              }
                                            },
                                            builder: (context, state) {
                                              return Flexible(
                                                child: Material(
                                                  color: Colors.transparent,
                                                  borderRadius: BorderRadius.circular(8),
                                                  child: InkWell(
                                                    borderRadius: BorderRadius.circular(8),
                                                    onTap: () {
                                                      if (context.router.stack.any(
                                                        (e) => [
                                                          OfficialExperimentalDetailsScreenRoute.name,
                                                          InsightsDiscoveryDetailsScreenRoute.name,
                                                          ScenarioForecastDetailsScreenRoute.name,
                                                        ].contains(e.name),
                                                      )) {
                                                        Navigator.pop(context);
                                                      } else {
                                                        context.read<IndicatorCardBloc>().add(
                                                              GetIndicatorDetailsEvent(
                                                                id: widget.indicatorNodeId ?? '',
                                                                contentType: widget.indicatorContentType ?? '',
                                                              ),
                                                            );
                                                      }
                                                    },
                                                    child: Stack(
                                                      children: [
                                                        if (state is IndicatorDetailsLoadingState)
                                                          const Positioned.fill(
                                                            child: AppShimmer(
                                                              radius: 8,
                                                            ),
                                                          ),
                                                        Container(
                                                          padding: const EdgeInsets.symmetric(
                                                            horizontal: 8,
                                                            vertical: 8,
                                                          ),
                                                          child: Text(
                                                            widget.indicatorName ?? '',
                                                            maxLines: 1,
                                                            overflow: TextOverflow.ellipsis,
                                                            style: TextStyle(
                                                              color:
                                                                  isLightMode ? AppColors.blueLight : AppColors.white,
                                                              fontSize: 14,
                                                              fontWeight: FontWeight.w500,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ],
                                    const SizedBox(height: 15),
                                    TicketIdChip(
                                      ticketId: widget.ticketId,
                                    ),
                                    const SizedBox(height: 15),
                                    Container(
                                      width: double.infinity,
                                      height: 2,
                                      color: isLightMode ? AppColors.white : AppColors.blackShade4,
                                    ),
                                  ],
                                ),
                              ),
                              Expanded(child: _body(state, isLightMode)),
                            ],
                          ),
                          if (isSendingMessage && (messageList ?? []).length > 2) ...[
                            Center(
                              child: Container(
                                width: 50,
                                height: 50,
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: Colors.transparent.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const CircularProgressIndicator(
                                  color: AppColors.selectedChipBlue,
                                ),
                              ),
                            ),
                          ],
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: BlocBuilder<ChatWithSmeBloc, ChatWithSmeState>(
            builder: (context, state) {
              return widget.chatDisabled == true || widget.chatThreadClosed == true || isChatDisabled
                  ? const SizedBox.shrink()
                  : Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ValueListenableBuilder(
                            valueListenable: dataQueryLinkPopupVisibility,
                            builder: (c, box, child) {
                              if (!dataQueryLinkPopupVisibility.value) {
                                return const SizedBox();
                              }
                              return Stack(
                                children: [
                                  Container(
                                    margin: const EdgeInsets.fromLTRB(14, 10, 14, 0),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 14,
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          isLightMode ? AppColors.blueShade36.withValues(alpha: 0.15) : AppColors.blueShade32,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text.rich(
                                      TextSpan(
                                        text: LocaleKeys.dataQueryLinkPopupMessage.tr(),
                                        children: [
                                          WidgetSpan(
                                            child: InkWell(
                                              onTap: () {
                                                final Uri uri = Uri.parse(statisticalDataRequestUrl);
                                                canLaunchUrl(uri).then((bool result) async {
                                                  if (result) {
                                                    await launchUrl(uri);
                                                  }
                                                });
                                              },
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                  top: 2,
                                                ),
                                                child: Text(
                                                  LocaleKeys.clickHere.tr(),
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                    color: AppColors.selectedChipBlue,
                                                    decoration: TextDecoration.underline,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 0,
                                    right: 8,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(50),
                                      onTap: () {
                                        dataQueryLinkPopupVisibility.value = false;
                                      },
                                      child: SvgPicture.asset(AppImages.icCancel),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                          IgnorePointer(
                            ignoring: isSendingMessage,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 15,
                                vertical: 22,
                              ),
                              child: SizedBox(
                                height: 49,
                                child: TextField(
                                  controller: chatController,
                                  textCapitalization: TextCapitalization.sentences,
                                  style: TextStyle(
                                    color: isLightMode ? AppColors.black : AppColors.white,
                                  ),
                                  decoration: InputDecoration(
                                    fillColor: isLightMode ? AppColors.white : AppColors.blueShade36,
                                    contentPadding: const EdgeInsets.symmetric(
                                      vertical: 14,
                                      horizontal: 12,
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(50),
                                      borderSide: const BorderSide(
                                        color: AppColors.greyShade1,
                                      ),
                                    ),
                                    disabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(50),
                                      borderSide: const BorderSide(
                                        color: AppColors.greyShade1,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(50),
                                      borderSide: const BorderSide(
                                        color: AppColors.greyShade1,
                                      ),
                                    ),
                                    prefixIcon: _attachmentButton(isLightMode),
                                    suffixIcon: Material(
                                      color: Colors.transparent,
                                      borderRadius: BorderRadius.circular(100),
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(100),
                                        onTap: () {
                                          FocusManager.instance.primaryFocus?.unfocus();
                                          if (chatController.text.trim().isEmpty && file == null) {
                                            AppMessage.showOverlayNotificationError(
                                              message: LocaleKeys.pleaseEnterAMessageOrAttachAFile.tr(),
                                            );
                                          } else {
                                            context.read<ChatWithSmeBloc>().add(
                                                  ChatWithSmeSendMessageEvent(
                                                    chatThreadId: widget.chatThreadId ?? '',
                                                    message: chatController.text,
                                                    attachment: file,
                                                  ),
                                                );
                                          }
                                        },
                                        child: Container(
                                          width: 33,
                                          height: 33,
                                          margin: const EdgeInsets.all(9),
                                          padding: const EdgeInsets.all(7),
                                          decoration: ShapeDecoration(
                                            color: isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                                            shape: const OvalBorder(),
                                          ),
                                          child: isSendingMessage
                                              ? const CircularProgressIndicator(
                                                  color: AppColors.white,
                                                  strokeWidth: 2,
                                                )
                                              : Transform.flip(
                                                  flipX: HiveUtilsSettings.isLanguageArabic,
                                                  child: SvgPicture.asset(
                                                    AppImages.icSend,
                                                  ),
                                                ),
                                        ),
                                      ),
                                    ),
                                    filled: true,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
            },
          ),
        ),
      ),
    );
  }

  Widget _referenceItem(bool isLightMode, String? label, bool showArrow) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$label ',
          style: TextStyle(
            color: isLightMode ? AppColors.grey : AppColors.white,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
        if (showArrow)
          Padding(
            padding: const EdgeInsets.only(top: 2),
            child: SvgPicture.asset(
              'assets/images/reference_arrow.svg',
              colorFilter: ColorFilter.mode(
                isLightMode ? AppColors.grey : AppColors.white,
                BlendMode.srcIn,
              ),
            ),
          ),
      ],
    );
  }

  Widget _body(ChatWithSmeState state, bool isLightMode) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          curve: Curves.easeOut,
          duration: const Duration(milliseconds: 500),
        );
      }
    });

    return SingleChildScrollView(
      controller: scrollController,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          children: [
            InboxList(
              messageList: messageList ?? [],
            ),
            if (_showAwaitingMessage() || isChatDisabled) ...[
              EndOfChatWidget(
                text: LocaleKeys.awaitingResponseFromSme.tr(),
              ),
              const SizedBox(height: 26),
            ],
            if (widget.chatThreadClosed == true || isChatClosed) ...[
              EndOfChatWidget(
                text: LocaleKeys.chatEnded.tr(),
              ),
              const SizedBox(height: 26),
            ],
          ],
        ),
      ),
    );
  }

  bool _showAwaitingMessage() {
    if ((widget.chatDisabled == true) && (widget.chatThreadClosed == false) && !isChatClosed) {
      if (messageList?.last.sender == null) {
        return true;
      } else if (messageList?.last.sender?.uuid != HiveUtilsAuth.getUserId()) {
        if (messageList?.last.feedback == null) {
          return false;
        } else if (messageList?.last.feedback?.satisfied == true) {
          return false;
        } else if (messageList?.last.feedback?.satisfied == false) {
          return true;
        }
      }
      return true;
    } else {
      return false;
    }
  }

  Widget _attachmentButton(bool isLightMode) {
    String fileType = '';
    if (file != null) {
      fileType = (file?.path.substring((file?.path.lastIndexOf('.') ?? -1) + 1) ?? '').toLowerCase();
      if (['jpg', 'jpeg', 'png'].contains(fileType)) {
        fileType = 'IMG';
      } else if (fileType == 'pdf') {
        fileType = 'PDF';
      } else {
        fileType = '';
      }
    }
    return Padding(
      padding: const EdgeInsets.all(8),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: file != null
            ? SizedBox(
                height: 49,
                child: Material(
                  color: isLightMode ? AppColors.greyShade5 : AppColors.grey,
                  borderRadius: BorderRadius.circular(30),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(30),
                          bottomLeft: Radius.circular(30),
                        ),
                        onTap: () {
                          context.pushRoute(ImageViewerScreenRoute(filePath: file?.path));
                        },
                        child: Container(
                          padding: const EdgeInsets.fromLTRB(8, 4, 12, 4),
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(30),
                              bottomLeft: Radius.circular(30),
                            ),
                            // borderRadius: BorderRadius.circular(42),
                          ),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                AppImages.icAttach,
                                colorFilter: ColorFilter.mode(
                                  isLightMode ? AppColors.grey.withValues(alpha: 0.8) : AppColors.greyShade1,
                                  BlendMode.srcIn,
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  // color: AppColors.grey,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Center(
                                  child: Text(
                                    fileType,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: isLightMode ? AppColors.grey : AppColors.whiteShade3,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const VerticalDivider(
                        width: 1,
                        indent: 2,
                        endIndent: 2,
                      ),
                      InkWell(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(30),
                          bottomRight: Radius.circular(30),
                        ),
                        onTap: () {
                          context.read<ChatWithSmeBloc>().add(
                                const ChatWithSmeAttachmentAddEvent(
                                  isRemove: true,
                                ),
                              );
                        },
                        child: Container(
                          padding: const EdgeInsets.fromLTRB(12, 6, 12, 6),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(42),
                          ),
                          child: Icon(
                            Icons.delete_rounded,
                            color: isLightMode ? AppColors.redButton : AppColors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(50),
                  onTap: () {
                    context.read<ChatWithSmeBloc>().add(
                          const ChatWithSmeAttachmentAddEvent(),
                        );
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(3),
                    child: SvgPicture.asset(
                      AppImages.icAttach,
                      colorFilter: ColorFilter.mode(
                        file != null
                            ? AppColors.selectedChipBlue
                            : isLightMode
                                ? AppColors.grey
                                : AppColors.greyShade1,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ),
      ),
    );
  }
}
