import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

enum AskUsFilterOptions {
  all(LocaleKeys.all),
  open(LocaleKeys.open),
  closed(LocaleKeys.closed);

  const AskUsFilterOptions(this.key);

  final String key;
}

class AskUsFilterButton extends StatelessWidget {
  const AskUsFilterButton({super.key});

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return PopupMenuButton(
      position: PopupMenuPosition.under,
      color: isLightMode ? AppColors.white : AppColors.scaffoldBackground,
      padding: const EdgeInsets.only(top: 12),
      menuPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      offset: const Offset(0, 8),
      onSelected: (option) => context.read<ChatWithSmeBloc>().add(
            OnChatFilterSelectedEvent(option),
          ),
      itemBuilder: (BuildContext context) {
        return AskUsFilterOptions.values.map((option) {
          return PopupMenuItem<AskUsFilterOptions>(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            height: 40,
            value: option,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 6),
              decoration: option != AskUsFilterOptions.values.last
                  ? BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: isLightMode ? AppColors.greyShade8 : AppColors.grey,
                          width: 2,
                        ),
                      ),
                    )
                  : null,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Text(option.key.tr()),
                ),
              ),
            ),
          );
        }).toList();
      },
      child: BlocBuilder<ChatWithSmeBloc, ChatWithSmeState>(
        buildWhen: (_, state) => state is OnChatFilterSelectedState,
        builder: (context, state) {
          final isAllSelected = context.read<ChatWithSmeBloc>().selectedOption == AskUsFilterOptions.all;

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                AppImages.icFilters,
                colorFilter: const ColorFilter.mode(
                  AppColors.blueLightOld,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                LocaleKeys.filters.tr(),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (!isAllSelected)
                Container(
                  width: 6,
                  height: 6,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    color: AppColors.red,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
