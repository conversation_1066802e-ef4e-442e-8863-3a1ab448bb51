import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class EndOfChatWidget extends StatelessWidget {
  const EndOfChatWidget({required this.text, super.key});

  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Expanded(
          child: Divider(
            color: AppColors.greyShade4,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: AppColors.greyShade4,
              fontSize: 14,
              fontWeight: FontWeight.w400,
              height: 0,
            ),
          ),
        ),
        const Expanded(
          child: Divider(
            color: AppColors.greyShade4,
          ),
        ),
      ],
    );
  }
}
