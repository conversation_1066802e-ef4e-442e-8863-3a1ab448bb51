import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class FeedbackPopUp extends StatelessWidget {
  FeedbackPopUp({required this.message, required this.isSatisfied, super.key});

  final MessageModel? message;
  final bool isSatisfied;

  final TextEditingController commentController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.blueShade28,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: commentController,
              maxLines: 5,
              autofocus: true,
              textCapitalization: TextCapitalization.sentences,
              style: AppTextStyles.s16w5cBlackShade1,
              decoration: InputDecoration(
                hintText: LocaleKeys.helpUsHint.tr(),
                hintStyle: const TextStyle(
                  color: AppColors.greyShade1,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  height: 0,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
            const SizedBox(height: 15),
            Align(
              alignment: Alignment.centerLeft,
              child: InkWell(
                onTap: () {
                  Navigator.pop(context);

                  context.read<ChatWithSmeBloc>().add(
                        ChatWithSmeSendFeedbackEvent(
                          messageId: message?.uuid ?? '',
                          isSatisfied: isSatisfied,
                          comment: commentController.text,
                        ),
                      );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 5,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.blueLight,
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: Text(
                    LocaleKeys.send.tr(),
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
