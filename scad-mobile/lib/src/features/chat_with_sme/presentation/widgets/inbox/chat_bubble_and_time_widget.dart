import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_bubble/chat_bubble.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/app_box_shadow.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/message/message_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/dialogs/dissatisfied_response_dialog.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/dialogs/satisfied_response_dialog.dart';
import 'package:scad_mobile/src/utils/app_utils/downloadHelper.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/gen_ai_assets.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChatBubbleAndTimeWidget extends StatelessWidget {
  const ChatBubbleAndTimeWidget({
    required this.message,
    required this.nextMessage,
    required this.isSender,
    this.isGrouped,
    this.isLastMessage = false,
    super.key,
  });

  final MessageModel? message;
  final MessageModel? nextMessage;
  final bool isSender;
  final bool? isGrouped;
  final bool? isLastMessage;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    final bool isArabic = HiveUtilsSettings.isLanguageArabic;

    return Column(
      crossAxisAlignment: isSender
          ? isArabic
              ? CrossAxisAlignment.start
              : CrossAxisAlignment.end
          : isArabic
              ? CrossAxisAlignment.end
              : CrossAxisAlignment.start,
      children: [
        if (isGrouped == true)
          Container(
            padding: _padding(
              message?.attachment != '' ? true : false,
              message?.message != '' ? true : false,
            ),
            margin: EdgeInsets.only(
              right: isSender ? 5 : 100,
              left: isSender ? 100 : 7,
            ),
            decoration: BoxDecoration(
              color: isSender
                  ? isLightMode
                      ? AppColors.blueLight
                      : AppColors.blueLightOld
                  : isLightMode
                      ? AppColors.white
                      : AppColors.blueShade32,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (message?.attachment != '') ...[
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Image.network(message?.attachment ?? ''),
                  ),
                  const SizedBox(height: 5),
                ],
                Text(
                  message?.message ?? '',
                  style: TextStyle(
                    color: isSender
                        ? AppColors.white
                        : isLightMode
                            ? AppColors.blackChatText
                            : AppColors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          )
        else
          Stack(
            children: [
              ChatBubble(
                alignment: isSender ? Alignment.centerRight : Alignment.centerLeft,
                clipper: ChatBubbleClipper6(
                  type: isSender ? BubbleType.sendBubble : BubbleType.receiverBubble,
                ),
                elevation: 0,
                backGroundColor: isSender
                    ? isLightMode
                        ? AppColors.blueLight
                        : AppColors.blueLightOld
                    : isLightMode
                        ? AppColors.white
                        : AppColors.blueShade32,
                padding: _padding(
                  message?.attachment != '' ? true : false,
                  message?.message != '' ? true : false,
                ),
                margin: EdgeInsets.only(
                  right: isSender ? 0 : 100,
                  left: isSender ? 100 : 0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (message?.attachment != '') ...[
                      _attachmentPreview(
                        context,
                        message?.attachment ?? '',
                        isLightMode,
                      ),
                      const SizedBox(height: 5),
                    ],
                    if (message?.message != '') ...[
                      Directionality(
                        textDirection: TextDirection.ltr,
                        child: Text(
                          message?.message ?? '',
                          style: TextStyle(
                            color: isSender
                                ? AppColors.white
                                : isLightMode
                                    ? AppColors.blackChatText
                                    : AppColors.white,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (!isSender && isLastMessage == true) ...[
                Positioned(
                  right: 0,
                  child: _buildLikeDislikeButton(context),
                ),
              ],
            ],
          ),
        Visibility(
          visible: isGrouped == false && message?.feedback?.comments != null,
          child: Directionality(
            textDirection: TextDirection.ltr,
            child: Padding(
              padding: const EdgeInsets.only(top: 10, bottom: 10),
              child: Text(
                isGrouped == true
                    ? (nextMessage?.createdTime ?? '').formatDateTimeForChat()
                    : (message?.createdTime ?? '').formatDateTimeForChat(),
                style: const TextStyle(
                  color: AppColors.greyShade4,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
        ),
        if (message?.feedback?.comments != null && message?.feedback?.comments != '') ...[
          Column(
            crossAxisAlignment: !isArabic ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              ChatBubble(
                alignment: Alignment.centerRight,
                clipper: ChatBubbleClipper6(
                  type: BubbleType.sendBubble,
                ),
                elevation: 0,
                backGroundColor: isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                margin: const EdgeInsets.only(
                  left: 100,
                ),
                padding: const EdgeInsets.only(
                  left: 22,
                  top: 12,
                  right: 22,
                  bottom: 20,
                ),
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: Text(
                    message?.feedback?.comments ?? '',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Directionality(
                textDirection: TextDirection.ltr,
                child: Text(
                  (message?.feedback?.createdTime ?? '').formatDateTimeForChat(),
                  style: const TextStyle(
                    color: AppColors.greyShade4,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ],
        const SizedBox(height: 10),
        Visibility(
          visible: isGrouped == false && message?.feedback?.comments == null,
          child: Directionality(
            textDirection: TextDirection.ltr,
            child: Text(
              isGrouped == true
                  ? (nextMessage?.createdTime ?? '').formatDateTimeForChat()
                  : (message?.createdTime ?? '').formatDateTimeForChat(),
              style: const TextStyle(
                color: AppColors.greyShade4,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLikeDislikeButton(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    bool isDisabled = message?.feedback != null;

    final child = BlocBuilder<ChatWithSmeBloc, ChatWithSmeState>(
      builder: (context, state) {
        if (state is ChatWithSmeSendFeedbackState) {
          isDisabled = state.chatThreadClosed;
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              message?.feedback?.satisfied == true
                  ? 'assets/images/thumbs-up-blue.svg'
                  : 'assets/images/thumbs-up-grey.svg',
              colorFilter: isLightMode
                  ? null
                  : ColorFilter.mode(
                      message?.feedback?.satisfied == true ? AppColors.white : AppColors.greyShade4,
                      BlendMode.srcIn,
                    ),
            ),
            const SizedBox(height: 2),
            SvgPicture.asset(
              message?.feedback?.satisfied == false
                  ? 'assets/images/thumbs-down-blue.svg'
                  : 'assets/images/thumbs-down-grey.svg',
              colorFilter: isLightMode
                  ? null
                  : ColorFilter.mode(
                      message?.feedback?.satisfied == false ? AppColors.white : AppColors.greyShade4,
                      BlendMode.srcIn,
                    ),
            ),
          ],
        );
      },
    );

    if (isDisabled) return child;

    return PopupMenuButton(
      surfaceTintColor: Colors.transparent,
      color: isLightMode ? AppColors.white : AppColors.scaffoldBackground,
      offset: const Offset(0, -20),
      position: PopupMenuPosition.over,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      onSelected: (option) => _onFeedbackSelected(context, option),
      itemBuilder: (context) {
        const options = [
          (LocaleKeys.satisfied, GenAIAssets.icThumbsUp),
          (LocaleKeys.dissatisfied, GenAIAssets.icThumbsDown),
        ];

        return options.map((option) {
          return PopupMenuItem<String>(
            value: option.$1,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: option.$1 == LocaleKeys.dissatisfied
                  ? null
                  : BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: isLightMode ? AppColors.greyShade8 : AppColors.grey,
                          width: 2,
                        ),
                      ),
                    ),
              child: Row(
                children: [
                  SvgPicture.asset(
                    option.$2,
                    width: 20,
                    height: 20,
                    colorFilter: const ColorFilter.mode(
                      AppColors.cyanDark,
                      BlendMode.srcIn,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Text(
                      option.$1.tr(),
                      style: isLightMode ? AppTextStyles.s14w4cGrey : AppTextStyles.s14w4cWhite,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList();
      },
      child: child,
    );
  }

  void _onFeedbackSelected(BuildContext context, String option) {
    final isSatisfied = option == LocaleKeys.satisfied;
    if (isSatisfied) {
      SatisfiedResponseDialog.show(
        context,
        messageUuid: message?.uuid ?? '',
      );
    } else {
      DissatisfiedResponseDialog.show(
        context,
        messageUuid: message?.uuid ?? '',
      );
    }
  }

  EdgeInsetsGeometry _padding(bool isAttachment, bool isMessageText) {
    if (isAttachment) {
      return EdgeInsets.only(
        left: isGrouped == true ? 5 : (isSender ? 5 : 13),
        top: 5,
        right: isGrouped == true ? 5 : (isSender ? 13 : 5),
        bottom: isMessageText ? 20 : 9,
      );
    } else {
      return EdgeInsets.only(
        left: isSender ? 22 : 27,
        top: isGrouped == true ? (isSender ? 0 : 12) : 12,
        right: isSender ? 27 : 22,
        bottom: isGrouped == true ? 13 : 20,
      );
    }
  }

  Widget _attachmentPreview(BuildContext context,
      String url,
      bool isLightMode,) {
    final ValueNotifier<DownloadProgress?> progress = ValueNotifier(null);

    final double screenWidth = MediaQuery.of(context).size.width;
    final String ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
    late Widget child;
    if (['jpg', 'jpeg', 'png'].contains(ext)) {
      child = Container(
        height: screenWidth * .5,
        width: screenWidth * .5,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: AppColors.whiteShade4,
        ),
        child: Image.network(
          message?.attachment ?? '',
          fit: BoxFit.cover,
        ),
      );
    } else {
      String fileType = '';
      if (ext.isEmpty) {
        fileType = 'Unknown File Type';
      } else if (ext == 'pdf') {
        fileType = 'PDF Document';
      }

      child = Container(
        width: screenWidth * .5,
        decoration: const BoxDecoration(
          color: AppColors.whiteShade2,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.whiteShade4,
              ),
              child: Icon(
                Icons.insert_drive_file_outlined,
                size: 50,
                color: AppColors.blue,
              ),
            ),
            const SizedBox(
              width: 12,
            ),
            Text(
              fileType,
            ),
          ],
        ),
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: Material(
        color: Colors.transparent,
        child: ValueListenableBuilder(
          valueListenable: progress,
          builder: (context, _, __) {
            return InkWell(
              onTap: progress.value?.progressValue != null
                  ? null
                  : () {
                      progress.value = DownloadProgress(downloaded: 0, total: 1);
                      DownloadHelper.downloadFileToCacheDir(
                        url: url,
                        folder: 'chat/chat_files',
                        fileName: 'file_${message?.uuid}.${ext.toLowerCase()}',
                        // checkFileExists: true,
                        progress: progress,
                        onComplete: (String filePath) {
                          context.pushRoute(ImageViewerScreenRoute(filePath: filePath));
                          progress.value = null;
                        },
                        onError: (String error) {
                          progress.value = null;
                        },
                      );
                    },
              child: Stack(
                children: [
                  child,
                  Positioned(
                    bottom: 6,
                    right: 6,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 150),
                      curve: Curves.ease,
                      height: (progress.value?.progressValue == null) ? 0 : 24,
                      decoration: BoxDecoration(
                        boxShadow: AppBox.shadow(),
                      ),
                      child: Row(
                        children: [
                          Text(
                            '${((progress.value?.progressValue ?? 0) * 100).toInt()}%',
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(6),
                            child: SizedBox(
                              height: 4,
                              width: screenWidth * .45 - 90,
                              child: LinearProgressIndicator(
                                value: progress.value?.progressValue,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
