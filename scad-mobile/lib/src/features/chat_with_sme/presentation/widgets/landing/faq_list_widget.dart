import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/faq/faq_model.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class FaqListWidget extends StatelessWidget {
   FaqListWidget({
    required this.faqList,
    super.key,
  });

  final List<FaqModel> faqList;

  final List<int> selectedIndexes = [];
  final ValueNotifier<String> selectedIndexesString = ValueNotifier('');

  @override
  Widget build(BuildContext context) {
    final bool isLightMode = HiveUtilsSettings.isLightMode;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: List.generate(faqList.length, (index) {
          return Column(
            children: [
              Theme(
                data: ThemeData(
                  dividerColor: Colors.transparent,
                  listTileTheme: ListTileTheme.of(context).copyWith(
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child:  ValueListenableBuilder(
                    valueListenable: selectedIndexesString,
                    builder: (context, _, __) {
                      return ExpansionTile(
                        key: Key('faq_tile_$index'),
                    title: Text(
                      (HiveUtilsSettings.isLanguageEnglish
                          ? (faqList[index].title ?? '')
                          : (faqList[index].titleAr == ''
                              ? (faqList[index].title ?? '')
                              : (faqList[index].titleAr ?? ''))),
                      style: TextStyle(
                        color: isLightMode
                            ? AppColors.blackTextTile
                            : AppColors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    tilePadding: EdgeInsets.zero,
                    trailing: selectedIndexes.any((e) => e == index)
                            ? SvgPicture.asset(
                                'assets/images/${isLightMode ? 'minus' : 'minus_dark'}.svg',
                              )
                            : SvgPicture.asset(
                                'assets/images/${isLightMode ? 'plus' : 'plus_dark'}.svg',
                              ),
                    onExpansionChanged: (isOpened) {
                      if(isOpened) {
                        selectedIndexes.add(index);
                      } else {
                        selectedIndexes.remove(index);
                      }
                      selectedIndexesString.value = selectedIndexes.join();
                    },
                    expandedAlignment: HiveUtilsSettings.isLanguageEnglish
                        ? Alignment.centerLeft
                        : Alignment.centerRight,
                    children: [
                      Text(
                        (HiveUtilsSettings.isLanguageEnglish
                            ? (faqList[index].description ?? '')
                            : faqList[index].descriptionAr == ''
                                ? (faqList[index].description ?? '')
                                : (faqList[index].descriptionAr ?? '')),
                        style: const TextStyle(
                          color: AppColors.greyShade4,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
      );
    },
                  ),
                ),
              ),
              if (index == faqList.length - 1)
                const SizedBox()
              else
                Divider(
                  color: isLightMode
                      ? AppColors.greyF3F4F6
                      : AppColors.blackShade4,
                  thickness: 1,
                ),
            ],
          );
        }),
      ),
    );
  }
}
