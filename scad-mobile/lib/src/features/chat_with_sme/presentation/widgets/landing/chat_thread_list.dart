import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/chat_thread/chat_thread_list_response.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/widgets/ticket_id_chip.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/hive_utils/api_cache/api_cache.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChatThreadListItem extends StatelessWidget {
  const ChatThreadListItem({
    required this.chatThread,
    required this.reloadChatThreadsFn,
    super.key,
  });

  final ChatThread chatThread;
  final VoidCallback reloadChatThreadsFn;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: InkWell(
        onTap: () {
          context.read<ChatWithSmeBloc>().add(
                ChatWithSmeLoadInboxEvent(
                  chatThreadId: chatThread.uuid ?? '',
                ),
              );
          context
              .pushRoute(
            ChatWithSmeInboxPageRoute(
              title: chatThread.subject,
              domain: chatThread.domain,
              domainId: int.parse(chatThread.domainId ?? '0'),
              theme: chatThread.theme,
              subTheme: chatThread.subTheme,
              indicatorNodeId: chatThread.indicatorNodeId,
              indicatorAppType: chatThread.indicatorAppType,
              indicatorContentType: chatThread.indicatorContentType,
              indicatorKey: chatThread.indicatorKey,
              indicatorName: chatThread.indicatorName,
              ticketId: chatThread.ticketId,
              chatThreadId: chatThread.uuid,
              chatDisabled: chatThread.chatDisabled,
              chatThreadClosed: chatThread.chatThreadClosed,
              isRead: chatThread.latestMessage?.isRead,
            ),
          )
              .whenComplete(() {
            reloadChatThreadsFn.call();
          });
        },
        borderRadius: BorderRadius.circular(15),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color:
                isLightMode ? AppColors.greyShade7 : AppColors.blueShade32,
            borderRadius: BorderRadius.circular(15),
            // boxShadow: [
            //   BoxShadow(
            //     color: AppColors.shadow1,
            //     blurRadius: 5,
            //     offset: const Offset(1, 4),
            //   ),
            // ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        SvgPicture.network(
                          HiveApiCacheBox.instance.getDomainImageById(chatThread.domainId),
                          width:20,
                          height:20,
                          colorFilter: ColorFilter.mode(
                            isLightMode
                                ? AppColors.blueLight
                                : AppColors.blueLightOld,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            chatThread.subject ?? '',
                            style: TextStyle(
                              color: isLightMode
                                  ? AppColors.blackTextTile
                                  : AppColors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 5),
                  Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color: (chatThread.latestMessage?.isRead == true) ||
                              (chatThread.latestMessage == null)
                          ? isLightMode
                              ? AppColors.readMessageDot
                              : AppColors.readMessageDot.withValues(alpha: 0.5)
                          : isLightMode
                              ? AppColors.blueLight
                              : AppColors.blueLightOld,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: chatThread.latestMessage?.sender?.uuid ==
                              HiveUtilsAuth.getUserId()
                          ? '${LocaleKeys.you.tr()}: '
                          : '${LocaleKeys.SME.tr()}: ',
                      style: TextStyle(
                        color: isLightMode
                            ? AppColors.blackChatText
                            : AppColors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    TextSpan(
                      text: chatThread.latestMessage == null
                          ? LocaleKeys.askForHelp.tr()
                          : chatThread.latestMessage?.message,
                      style: const TextStyle(
                        color: AppColors.greyShade4,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TicketIdChip(
                    ticketId: chatThread.ticketId,
                  ),
                  const SizedBox(width: 10),
                  Visibility(
                    visible: chatThread.latestMessage != null,
                    child: Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Directionality(
                              textDirection: TextDirection.ltr,
                              child: Text(
                                chatThread.latestMessage?.createdTime
                                        ?.formatDateTimeForChat(
                                      showDateOnly: true,
                                    ) ??
                                    '',
                                style: const TextStyle(
                                  color: AppColors.greyShade4,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          const SizedBox(width: 6),
                          SvgPicture.asset(
                            'assets/images/pending.svg',
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              _buildChatStatusTag(chatThread.chatThreadClosed ?? false),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChatStatusTag(bool isClosed) {
    final String asset;
    final Color color;
    final String text;

    if (isClosed) {
      asset = AppImages.icChatThreadClosed;
      color = AppColors.redE5634A;
      text = LocaleKeys.closed.tr();
    } else {
      asset = AppImages.icChatThreadOpen;
      color = AppColors.green3BD6AD;
      text = LocaleKeys.open.tr();
    }

    return Material(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(32),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(asset),
            const SizedBox(width: 4),
            Text(
              text,
              style: TextStyle(color: color, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
