import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/rounded_dropdown_widget.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_auth.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class NewChatBottomSheet extends StatefulWidget {
  const NewChatBottomSheet({super.key});

  static Future<bool> createChatThreadWarning(BuildContext context, String? domainId) async {
    final List<String> multiDomainId = HiveUtilsAuth.getJWTDetails().multiDomainId ?? [];
    bool allow = false;

    if (multiDomainId.isNotEmpty && multiDomainId.contains(domainId)) {
      await showDialog<void>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            contentPadding: const EdgeInsets.all(20),
            insetPadding: const EdgeInsets.all(20),
            backgroundColor: AppColors.white,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(20)),
            ),
            elevation: 0,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Lottie.asset(AnimationAsset.animationAlert),
                const SizedBox(height: 14),
                Text(
                  LocaleKeys.smeCreateChatWarningMessage.tr(),
                  style: AppTextStyles.s14w4cBlueTitleText,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        allow = true;
                        Navigator.pop(context);
                      },
                      child: Text(
                        LocaleKeys.proceed.tr(),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 14),
                    TextButton(
                      style: TextButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          side: BorderSide(color: AppColors.blue),
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                        allow = false;
                      },
                      child: Text(
                        LocaleKeys.cancel.tr(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.blue,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      );
    }
    else{
      allow = true;
    }
    return allow;
  }

  @override
  State<NewChatBottomSheet> createState() => _NewChatBottomSheetState();
}

class _NewChatBottomSheetState extends State<NewChatBottomSheet> {
  bool? isLoading;
  List<DomainModel>? domainList;
  DomainModel? selectedDomain;
  List<Subdomain>? subDomainList;
  Subdomain? selectedSubDomain;
  List<Subtheme>? subThemeList;
  Subtheme? selectedSubTheme;
  String? charLength = '0';
  TextEditingController subjectController = TextEditingController();
  final bool isLightMode = HiveUtilsSettings.isLightMode;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<ChatWithSmeBloc>().add(
        const GetDomainListEvent(),
      );
    });
  }

  @override
  Widget build(BuildContext context) {

    return BlocConsumer<ChatWithSmeBloc, ChatWithSmeState>(
      listener: (context, state) {
        if (state is GetDomainListLoadingState) {
          isLoading = true;
        } else if (state is ChatWithSmeDomainDataState) {
          isLoading = false;
          domainList = state.domainList;
        } else if (state is ChatWithSmeDomainDropdownState) {
          selectedDomain = state.selectedValue;
          subDomainList = state.subDomainList;
          subThemeList = null;
          selectedSubDomain = null;
          selectedSubTheme = null;
        } else if (state is ChatWithSmeThemeDropdownState) {
          selectedSubDomain = state.selectedValue;
          subThemeList = state.subThemeList;
          selectedSubTheme = null;
        } else if (state is ChatWithSmeSubThemeDropdownState) {
          selectedSubTheme = state.selectedValue;
        } else if (state is Max60CharacterState) {
          charLength = state.count;
          subjectController.text = state.actualText ?? '';
        }
      },
      builder: (context, state) {
        if (state is FaqDomainListErrorState) {
          return Center(
            child: Text(
              state.error,
            ),
          );
        }
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
            decoration: BoxDecoration(
              color: isLightMode ? AppColors.white : AppColors.blueShade32,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: isLoading == true
                ? ConstrainedBox(
                    constraints: const BoxConstraints(
                      maxHeight: 200,
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 10),
                      const BottomSheetTopNotch(),
                      const SizedBox(height: 16),
                      Text(
                        LocaleKeys.selectCategoryYouWantToTalkAbout.tr(),
                        style: TextStyle(
                          color: isLightMode
                              ? AppColors.blackShade1
                              : AppColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Expanded(
                            child: RoundedDropDownWidget<DomainModel>(
                              constraintWidth:
                                  (MediaQuery.sizeOf(context).width - 64) / 2,
                              width: (MediaQuery.sizeOf(context).width - 64) / 2,
                              title: LocaleKeys.changeDomain.tr(),
                              items: domainList,
                              value: selectedDomain,
                              showAsRequired:true,
                              onChanged: (val) {
                                context.read<ChatWithSmeBloc>().add(
                                      ChatWithSmeDomainDropdownEvent(
                                        domainList: domainList ?? [],
                                        selectedValue: val ?? DomainModel(),
                                      ),
                                    );
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: RoundedDropDownWidget<Subdomain>(
                              constraintWidth:
                                  (MediaQuery.sizeOf(context).width - 64) / 2,
                              width: (MediaQuery.sizeOf(context).width - 64) / 2,
                              title: LocaleKeys.changeTheme.tr(),
                              items: subDomainList,
                              value: selectedSubDomain,
                              onChanged: (val) {
                                context.read<ChatWithSmeBloc>().add(
                                      ChatWithSmeThemeDropdownEvent(
                                        subDomainList: subDomainList ?? [],
                                        selectedValue: val ?? Subdomain(),
                                      ),
                                    );
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      RoundedDropDownWidget<Subtheme>(
                        constraintWidth: (MediaQuery.sizeOf(context).width - 64) / 2,
                        width: MediaQuery.sizeOf(context).width,
                        title: LocaleKeys.changeSubTheme.tr(),
                        items: subThemeList,
                        value: selectedSubTheme,
                        onChanged: (val) {
                          context.read<ChatWithSmeBloc>().add(
                                ChatWithSmeSubThemeDropdownEvent(
                                  selectedValue: val!,
                                ),
                              );
                        },
                      ),
                      const SizedBox(height: 20),
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: LocaleKeys.addSubjectLine.tr(),
                              style: const TextStyle(
                                color: AppColors.greyShade4,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const WidgetSpan(
                              child: SizedBox(width: 5),
                            ),
                            const TextSpan(
                              text: '*',
                              style: TextStyle(
                                color: AppColors.redWarning,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 5),
                      TextField(
                        controller: subjectController,
                        textCapitalization: TextCapitalization.sentences,
                        maxLength: 60,
                        decoration: InputDecoration(
                          counterText: '',
                          contentPadding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: const BorderSide(color: AppColors.greyShade1),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: const BorderSide(color: AppColors.greyShade1),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: const BorderSide(color: AppColors.greyShade1),
                          ),
                          suffix: Text(
                            '$charLength/60',
                            style: TextStyle(color: isLightMode ? AppColors.black : AppColors.white),
                          ),
                          fillColor: isLightMode ? AppColors.white : AppColors.blueShade36,
                          filled: true,
                        ),
                        style: TextStyle(
                          color: isLightMode ? AppColors.black : AppColors.white,
                          fontSize: 16,
                        ),
                        onChanged: (val) {
                          context.read<ChatWithSmeBloc>().add(Max60CharacterEvent(value: val));
                        },
                        onSubmitted: (val) {
                          context.read<ChatWithSmeBloc>().add(Max60CharacterEvent(value: val));
                        },
                      ),
                      const SizedBox(height: 30),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size.fromHeight(43),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onPressed: () async {
                          if (selectedDomain == null) {
                            AppMessage.showOverlayNotificationError(message: LocaleKeys.domainRequired.tr());
                          } else if (subjectController.text.trim().isEmpty) {
                            AppMessage.showOverlayNotificationError(message: LocaleKeys.subjectRequired.tr());
                          } else {
                            final bool allowCreate = await NewChatBottomSheet.createChatThreadWarning(context, selectedDomain!.domainId);
                            if(allowCreate && mounted){
                              Navigator.pop(context);
                              context.read<ChatWithSmeBloc>().add(
                                NewChatThreadEvent(
                                  domain: selectedDomain!.domainName!,
                                  domainId: int.parse(selectedDomain!.domainId!),
                                  theme: selectedSubDomain?.name ?? '',
                                  subTheme: selectedSubTheme?.name ?? '',
                                  subject: subjectController.text,
                                ),
                              );
                            }
                          }
                        },
                        child: Text(
                          LocaleKeys.submitYourQuery.tr(),
                          style: const TextStyle(
                            color: AppColors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }
}
