import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/details_page/compute/data/enums/compute_operations.dart';

class ComputeDataParams extends ChangeNotifier {
  ComputeDataParams({
    required ComputeOperations operation,
    required this.indicatorId,
    required this.properties,
  }) : _operation = operation;

  final String indicatorId;
  final List<Properties> properties;

  ComputeOperations _operation;

  ComputeOperations get operation => _operation;

  set operation(ComputeOperations operation) {
    _operation = operation;
    notifyListeners();
  }

  int? selectedPropertyIndex;

  int? _firstSelectedOptionIndex;

  int? get firstSelectedOptionIndex => _firstSelectedOptionIndex;

  set firstSelectedOptionIndex(int? value) {
    _firstSelectedOptionIndex = value;
    _secondSelectedOptionIndex = null;
    notifyListeners();
  }

  int? _secondSelectedOptionIndex;

  int? get secondSelectedOptionIndex => _secondSelectedOptionIndex;

  set secondSelectedOptionIndex(int? value) {
    _secondSelectedOptionIndex = value;
    notifyListeners();
  }

  Properties? get selectedProperty =>
      selectedPropertyIndex != null ? properties.elementAt(selectedPropertyIndex!) : null;

  List<String>? get firstPropertyOptions => selectedPropertyIndex != null
      ? List.from(
          properties.elementAt(selectedPropertyIndex!).options ?? [],
        )
      : null;

  void _resetOptionIndices() {
    _firstSelectedOptionIndex = null;
    _secondSelectedOptionIndex = null;
  }

  void onSelectedPropertyChanged(Properties? property) {
    if (property == null) {
      selectedPropertyIndex = null;
      _resetOptionIndices();
      notifyListeners();
      return;
    }

    selectedPropertyIndex = properties.indexWhere(
      (e) => e.path == property.path,
    );
    _resetOptionIndices();
    notifyListeners();
  }

  JSONObject getPayload() {
    final selectedProperty = this.selectedProperty;
    if (selectedProperty == null) {
      throw Exception('No property selected');
    }

    final unselectedPropertiesEntries = properties
        .where(
          (e) => e.path != selectedProperty.path,
        )
        .map(
          (e) => MapEntry(e.path, e.defaultVal),
        );
    final unselectedProperties = Map.fromEntries(unselectedPropertiesEntries);

    final selectedPropertyOptions = selectedProperty.options ?? [];

    return {
      'id': indicatorId,
      'dimensions': {
        ...unselectedProperties,
        selectedProperty.path: [
          selectedPropertyOptions.elementAt(firstSelectedOptionIndex!),
          selectedPropertyOptions.elementAt(secondSelectedOptionIndex!),
        ],
      },
      'operation': operation.symbol,
    };
  }
}
