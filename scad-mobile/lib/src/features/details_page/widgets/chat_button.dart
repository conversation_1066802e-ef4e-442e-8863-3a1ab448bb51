import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/features/chat_with_sme/presentation/bloc/chat_with_sme_bloc.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/src/features/details_page/base/helper/screenshot_helper.dart';
import 'package:scad_mobile/src/features/details_page/base/mixin/create_sme_thread_mixin.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:screenshot/screenshot.dart';

/// T is required and should extend [DetailsBaseBloc] and mixin [CreateSMEThreadMixin]
class ChatButton<T extends CreateSMEThreadMixin> extends StatefulWidget {
  const ChatButton({
    required this.screenshotController,
    required this.title,
    required this.contentType,
    required this.indicatorDetails,
    super.key,
  });

  final String title;
  final String contentType;
  final ScreenshotController screenshotController;
  final IndicatorDetailsResponse indicatorDetails;

  @override
  State<ChatButton<T>> createState() => _ChatButtonState<T>();
}

class _ChatButtonState<T extends CreateSMEThreadMixin> extends State<ChatButton<T>> {
  bool _isLoading = false;

  Future<void> _createSMEThreadListener(BuildContext context, DetailsBaseState state) async {
    if (state is CreateSMEThreadErrorState) {
      AppMessage.showOverlayNotificationError(
        message: state.error ?? LocaleKeys.somethingWentWrong.tr(),
      );
    }

    if (state is CreateSMEThreadSuccessState) {
      context.read<ChatWithSmeBloc>().add(
            ChatWithSmeLoadInboxEvent(
              chatThreadId: state.chatThread?.uuid ?? '',
            ),
          );

      final file = File(await ScreenshotHelper.chatScreenshotFilePath());
      if (context.mounted) {
        unawaited(
          context.pushRoute(
            ChatWithSmeInboxPageRoute(
              title: state.domain,
              domain: state.domain,
              domainId: state.domainId,
              theme: state.theme,
              subTheme: state.subTheme,
              ticketId: state.chatThread?.ticketId,
              indicatorNodeId: state.indicatorNodeId,
              indicatorAppType: state.indicatorAppType,
              indicatorContentType: state.indicatorContentType,
              indicatorKey: state.indicatorKey,
              indicatorName: state.indicatorName,
              chatThreadId: state.chatThread?.uuid ?? '',
              chatDisabled: state.chatDisabled,
              chatThreadClosed: state.chatThreadClosed,
              attachFile: file,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<T, DetailsBaseState>(
      listenWhen: (_, state) => state is CreateSMEThreadEventBaseState,
      listener: _createSMEThreadListener,
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 350),
        child: _isLoading
            ? const SizedBox(
                height: 37,
                width: 37,
                child: Padding(
                  padding: EdgeInsets.all(6),
                  child: CircularProgressIndicator(),
                ),
              )
            : InkWell(
                onTap: _onChatButtonClick,
                child: SvgPicture.asset(
                  HiveUtilsSettings.isLightMode ? AppImages.icChatLight : AppImages.icChatDark,
                ),
              ),
      ),
    );
  }

  Future<void> _onChatButtonClick() async {
    try {
      setState(() => _isLoading = true);
      await ScreenshotHelper.capture(
        context: context,
        screenshotController: widget.screenshotController,
        title: widget.title,
      );

      final indicatorDetails = widget.indicatorDetails;
      final contentType = indicatorDetails.type == 'Internal' && indicatorDetails.multiDrivers == true
          ? 'analytical-apps'
          : widget.contentType;

      if (mounted) {
        context.read<T>().add(
              CreateSMEThreadEvent(
                indicatorDetails: indicatorDetails,
                contentType: contentType,
              ),
            );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
