import 'package:easy_localization/easy_localization.dart' as locale;
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/string_extentions.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class CustomDataTable extends StatefulWidget {
  const CustomDataTable({
    required this.headerCells,
    required this.rowsCells,
    required this.isLightMode,
    required this.isLandscape,
    super.key,
  });

  final List<String?> headerCells;
  final List<DataGridRow> rowsCells;
  final bool isLightMode;
  final bool isLandscape;

  @override
  State<CustomDataTable> createState() => _CustomDataTableState();
}

class _CustomDataTableState extends State<CustomDataTable> {
  final _rowController = ScrollController();
  final _subTableXController = ScrollController();

  List<double> cellWidthList = [];

  double get width =>
      widget.isLandscape ? (MediaQuery.of(context).size.height - 40) : (MediaQuery.of(context).size.width - 48);

  @override
  void initState() {
    setCellWidth();

    _subTableXController.addListener(() {
      _rowController.jumpTo(_subTableXController.position.pixels);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    setCellWidth();
    if (widget.headerCells.isEmpty) {
      return Center(child: Text(LocaleKeys.noDataAvailable.tr()));
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SingleChildScrollView(
          controller: _rowController,
          scrollDirection: Axis.horizontal,
          physics: const NeverScrollableScrollPhysics(),
          child: _buildHeaderRow(),
        ),
        Expanded(
          child: Align(
            alignment: Alignment.topLeft,
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              controller: _subTableXController,
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: _buildDataRows(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void setCellWidth() {
    cellWidthList = List.generate(widget.headerCells.length, (index) => 0);
    for (int i = 0; i < widget.headerCells.length; i++) {
      cellWidthList[i] = _textSize(widget.headerCells[i] ?? '').width;

      for (final DataGridRow row in widget.rowsCells) {
        if (_textSize(row.getCells()[i].value.toString()).width > cellWidthList[i]) {
          cellWidthList[i] = _textSize(row.getCells()[i].value.toString()).width;
        }
      }
    }

    for (int i = 0; i < cellWidthList.length; i++) {
      cellWidthList[i] += 20;
    }
  }

  // finds text widget size
  Size _textSize(String text) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: AppTextStyles.s12w5cWhite),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout();
    return textPainter.size;
  }

  Widget _buildHeaderRow() {
    return ConstrainedBox(
      constraints: BoxConstraints(minWidth: width),
      child: DataTable(
        border: TableBorder(
          horizontalInside: const BorderSide(color: AppColors.greyShade8),
          verticalInside: const BorderSide(color: AppColors.greyShade8),
          borderRadius: BorderRadius.circular(10),
        ),
        headingRowHeight: 30,
        dataRowMinHeight: 30,
        dataRowMaxHeight: 30,
        headingRowColor: WidgetStateColor.resolveWith(
          (states) => AppColors.blue,
        ),
        columns: List.generate(widget.headerCells.length, (i) {
          return DataColumn(
            label: SizedBox(
              width: cellWidthList[i],
              child: Text(
                widget.headerCells[i] ?? '-',
                maxLines: 1,
                style: AppTextStyles.s12w5cWhite,
              ),
            ),
          );
        }),
        rows: const [],
      ),
    );
  }

  Widget _buildDataRows() {
    return ConstrainedBox(
      constraints: BoxConstraints(minWidth: width),
      child: IntrinsicWidth(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(minWidth: width),
              child: DataTable(
                border: TableBorder(
                  horizontalInside: const BorderSide(color: AppColors.greyShade8),
                  verticalInside: const BorderSide(color: AppColors.greyShade3),
                  borderRadius: BorderRadius.circular(10),
                ),
                headingRowHeight: 30,
                dataRowMinHeight: 30,
                dataRowMaxHeight: 30,
                columns: List.generate(widget.rowsCells.first.getCells().length, (i) {
                  return DataColumn(
                    label: SizedBox(
                      width: cellWidthList[i],
                      child: _text(
                        widget.rowsCells.first.getCells()[i].value.toString().isNotEmpty
                            ? widget.rowsCells.first.getCells()[i].value.toString()
                            : '-',
                      ),
                    ),
                  );
                }).toList(),
                rows: widget.rowsCells.sublist(1).map((row) {
                  return DataRow(
                    cells: List.generate(row.getCells().length, (i) {
                      return DataCell(
                        SizedBox(
                          width: cellWidthList[i],
                          child: _text(
                            row.getCells()[i].value.toString().isNotEmpty ? row.getCells()[i].value.toString() : '-',
                          ),
                        ),
                      );
                    }).toList(),
                  );
                }).toList(),
              ),
            ),
            ConstrainedBox(
              constraints: BoxConstraints(minWidth: width),
              child: const Divider(
                height: 1,
                color: AppColors.greyShade3,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Text _text(String str) {
    return Text(
      str,
      maxLines: 1,
      style: widget.isLightMode ? AppTextStyles.s12w5cBlack : AppTextStyles.s12w5cWhite,
      textDirection: str.isNumber() ? TextDirection.ltr : null,
    );
  }
}
