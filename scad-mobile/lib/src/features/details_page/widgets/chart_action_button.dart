import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChartActionButton extends StatelessWidget {
  const ChartActionButton.frequency({
    this.onPressed,
    this.enabled = true,
    super.key,
  })  : icon = AppImages.icDataFrequency,
        i18lLabelKey = LocaleKeys.changeDataFrequency;

  const ChartActionButton.presentation({
    this.onPressed,
    this.enabled = true,
    super.key,
  })  : icon = AppImages.icDataPresentation,
        i18lLabelKey = LocaleKeys.changeDataPresentation;

  const ChartActionButton.compare({
    this.onPressed,
    this.enabled = true,
    super.key,
  })  : icon = AppImages.icCompareIndicators,
        i18lLabelKey = LocaleKeys.compareIndicators;

  const ChartActionButton.compute({
    this.onPressed,
    this.enabled = true,
    super.key,
  })  : icon = AppImages.icComputeData,
        i18lLabelKey = LocaleKeys.computeData;

  final String icon;
  final String i18lLabelKey;
  final VoidCallback? onPressed;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return IgnorePointer(
      ignoring: !enabled,
      child: Opacity(
        opacity: enabled ? 1 : 0.5,
        child: InkWell(
          onTap: onPressed,
          child: Column(
            children: [
              Container(
                width: 50,
                height: 70,
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 20),
                decoration: BoxDecoration(
                  color: isLightMode
                      ? AppColors.blueShade24.withValues(alpha: 0.1)
                      : AppColors.blueLightOld.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(80),
                ),
                child: SvgPicture.asset(
                  icon,
                  colorFilter: ColorFilter.mode(
                    isLightMode ? AppColors.blueLight : AppColors.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              SizedBox(
                width: (MediaQuery.sizeOf(context).width - 48) / 4,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 1),
                  child: Text(
                    i18lLabelKey.tr(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isLightMode ? AppColors.grey : AppColors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
