import 'package:flutter/cupertino.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class ToggleSwitch extends StatefulWidget {
  const ToggleSwitch({
    required this.label,
    required this.initialValue,
    required this.onChanged,
    super.key,
  });

  final String label;
  final bool initialValue;
  final ValueChanged<bool> onChanged;

  @override
  State<ToggleSwitch> createState() => _ToggleSwitchState();
}

class _ToggleSwitchState extends State<ToggleSwitch> {
  late bool _value = widget.initialValue;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          widget.label,
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: AppColors.greyShade4,
            fontSize: 10,
            fontWeight: FontWeight.w400,
          ),
        ),
        SizedBox(
          height: 17,
          width: 41,
          child: FittedBox(
            fit: BoxFit.fitHeight,
            child: CupertinoSwitch(
              activeTrackColor: AppColors.green,
              inactiveTrackColor: AppColors.greyShade6,
              value: _value,
              onChanged: (value) {
                setState(
                  () => _value = value,
                );
                widget.onChanged(value);
              },
            ),
          ),
        ),
      ],
    );
  }
}
