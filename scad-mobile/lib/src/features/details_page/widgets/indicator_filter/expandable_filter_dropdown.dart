import 'package:flutter/material.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class ExpandableFilterDropdown extends StatelessWidget {
  const ExpandableFilterDropdown({
    required this.label,
    required this.displayText,
    this.isSelected = false,
    this.width,
    this.children,
    this.onTap,
    this.spacing = 10,
    super.key,
  });

  final bool isSelected;
  final double? width;
  final String label;
  final String displayText;
  final List<Widget>? children;
  final VoidCallback? onTap;
  final double spacing;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppColors.greyShade4,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
        const SizedBox(height: 5),
        InkWell(
          onTap: onTap,
          child: Container(
            width: width ?? MediaQuery.sizeOf(context).width,
            height: 40,
            decoration: BoxDecoration(
              color: isLightMode ? AppColors.white : AppColors.blueShade36,
              borderRadius: BorderRadius.circular(70),
              border: Border.all(
                color: isSelected
                    ? isLightMode
                        ? AppColors.blueLight
                        : AppColors.selectedChipBlue
                    : isLightMode
                        ? AppColors.greyShade1
                        : AppColors.blackShade8,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      displayText,
                      style: TextStyle(
                        color: isLightMode ? AppColors.blackTextTile : AppColors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  AnimatedRotation(
                    turns: isSelected ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.fastLinearToSlowEaseIn,
                    child: const Icon(
                      Icons.arrow_drop_down,
                    ),
                  ),
                ],
              ),
            ),
            // ),
          ),
        ),
        AnimatedSize(
          duration: const Duration(milliseconds: 500),
          curve: Curves.fastLinearToSlowEaseIn,
          child: isSelected
              ? Container(
                  margin: const EdgeInsets.only(top: 8, bottom: 4),
                  padding: const EdgeInsets.all(14),
                  width: MediaQuery.sizeOf(context).width,
                  decoration: BoxDecoration(
                    color: isLightMode ? Colors.transparent : AppColors.blueShade32,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: isLightMode ? AppColors.greyShade1 : AppColors.selectedChipBlue,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    spacing: spacing,
                    children: children ?? [],
                  ),
                )
              : const SizedBox.shrink(),
        ),
      ],
    );
  }
}
