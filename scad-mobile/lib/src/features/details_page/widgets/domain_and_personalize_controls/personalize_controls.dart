part of 'domain_and_personalize_controls.dart';

class PersonalizeControls extends StatefulWidget {
  const PersonalizeControls({
    required this.indicatorDetails,
    required this.contentType,
    this.hideNotification = false,
    super.key,
  });

  final IndicatorDetailsResponse indicatorDetails;
  final String contentType;
  final bool hideNotification;

  @override
  State<PersonalizeControls> createState() => _PersonalizeControlsState();
}

class _PersonalizeControlsState extends State<PersonalizeControls> {
  NodeIdUuid? subscription;

  NodeIdUuid? myApps;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      try {
        if (mounted) {
          context.read<IndicatorCardBloc>().add(
                GetIndicatorStatusEvent(
                  id: widget.indicatorDetails.id!,
                ),
              );
        }
      } catch (e, s) {
        Completer<dynamic>().completeError(e, s);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isDemoMode) {
      return const SizedBox();
    }

    final isLightMode = HiveUtilsSettings.isLightMode;
    return BlocConsumer<IndicatorCardBloc, IndicatorCardState>(
      listener: (context, state) {
        if (state is IndicatorStatusErrorState) {
          subscription = NodeIdUuid();
          myApps = NodeIdUuid();
        } else if (state is IndicatorStatusSuccessState) {
          subscription = state.subscription;
          myApps = state.myApps;
        }
      },
      builder: (context, state) {
        if (widget.indicatorDetails.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.id == 'compute-data') {
          return const SizedBox();
        }

        return Row(
          children: [
            if (!widget.hideNotification)
              BlocConsumer<NotificationBloc, NotificationState>(
                listener: (context, state) {
                  if (state is SubscriptionSuccessState) {
                    if (state.id == widget.indicatorDetails.id!) {
                      if (mounted) {
                        context.read<IndicatorCardBloc>().add(
                              GetIndicatorStatusEvent(
                                id: widget.indicatorDetails.id!,
                              ),
                            );
                      }
                    }
                  }
                },
                builder: (context, state) {
                  return Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(20),
                      onTap: _onNotificationIconTapped,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: subscription?.nodeId != null
                            ? SvgPicture.asset(isLightMode ? AppImages.icBellSolidLight : AppImages.icBellSolid)
                            : SvgPicture.asset(
                                AppImages.icBell,
                                colorFilter: ColorFilter.mode(
                                  isLightMode ? AppColors.greyShade4 : AppColors.white,
                                  BlendMode.srcIn,
                                ),
                              ),
                      ),
                    ),
                  );
                },
              ),
            BlocConsumer<MyAppsBloc, MyAppsState>(
              listener: (context, state) {
                if (state is MyAppsStatusSuccessResponseState) {
                  if (state.id == widget.indicatorDetails.id!) {
                    if (mounted) {
                      context.read<IndicatorCardBloc>().add(
                            GetIndicatorStatusEvent(
                              id: widget.indicatorDetails.id!,
                            ),
                          );
                    }
                  }
                }
              },
              builder: (context, state) {
                return Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(
                      20,
                    ),
                    onTap: _onAddToMyAppsTapped,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: myApps?.nodeId != null
                          ? isLightMode
                              ? SvgPicture.asset(
                                  AppImages.icAddToMyAppsOnLight,
                                )
                              : SvgPicture.asset(
                                  AppImages.icAddToMyAppsOn,
                                )
                          : SvgPicture.asset(
                              AppImages.icAddToMyAppsOff,
                              colorFilter: ColorFilter.mode(
                                isLightMode ? AppColors.greyShade4 : AppColors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                    ),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  void _onNotificationIconTapped() {
    final indicatorId = widget.indicatorDetails.id ?? '';
    if (subscription?.nodeId == null) {
      return context.read<NotificationBloc>().add(
            CreateSubscriptionEvent(
              indicatorId: indicatorId,
              title: widget.indicatorDetails.componentTitle ?? '',
              request: SubscriptionRequest(
                nodeId: widget.indicatorDetails.id,
                appType: widget.indicatorDetails.type,
                contentType: widget.contentType,
              ),
            ),
          );
    }

    context.read<NotificationBloc>().add(
          RemoveSubscriptionEvent(
            indicatorId: indicatorId,
            title: widget.indicatorDetails.componentTitle ?? '',
            uuid: subscription!.uuid!,
            id: subscription!.nodeId!,
          ),
        );
  }

  Future<void> _onAddToMyAppsTapped() async {
    final indicatorId = widget.indicatorDetails.id ?? '';

    if (myApps?.nodeId != null) {
      return context.read<MyAppsBloc>().add(
            RemoveFromMyAppsEvent(
              indicatorId: indicatorId,
              title: widget.indicatorDetails.componentTitle ?? '',
              id: widget.indicatorDetails.id!,
              uuid: myApps!.uuid!,
            ),
          );
    }

    await showModalBottomSheet<void>(
      isScrollControlled: true,
      useRootNavigator: true,
      constraints: BoxConstraints(
        minHeight: 100,
        maxHeight: MediaQuery.sizeOf(context).height * .90,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(
            20,
          ),
        ),
      ),
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return AddToMyApps(
          indicatorDetails: IndicatorDetailsResponseHelper(widget.indicatorDetails),
          nodeId: widget.indicatorDetails.id!,
          contentType: widget.contentType,
          isComparisonActive: widget.hideNotification,
        );
      },
    );
  }
}
