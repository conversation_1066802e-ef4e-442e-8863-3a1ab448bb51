import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/domain_icon.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_status_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/bloc/indicator_card_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/pages/add_to_myapps.dart';
import 'package:scad_mobile/src/features/notification/data/models/request/subscription_request.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

part 'personalize_controls.dart';

class DomainAndPersonalizeControls extends StatelessWidget {
  const DomainAndPersonalizeControls({
    required this.indicatorDetails,
    required this.contentType,
    this.hideNotification = false,
    super.key,
  });

  final IndicatorDetailsResponse indicatorDetails;
  final String contentType;
  final bool hideNotification;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Row(
      children: [
        DomainIcon(
          domainIdOrName: indicatorDetails.domainId,
          color: isLightMode ? AppColors.black : AppColors.white,
          padding: const EdgeInsets.all(6),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            indicatorDetails.domain ?? '',
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: TextStyle(
              color: isLightMode ? AppColors.grey : AppColors.greyShade4,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        PersonalizeControls(
          indicatorDetails: indicatorDetails,
          contentType: contentType,
          hideNotification: hideNotification,
        ),
      ],
    );
  }
}
