part of '../download_as.dart';

Future<void> _saveXls(
  BuildContext context, {
  required String title,
  required SeriesDataList seriesList,
  List<TableFields>? tableFieldList,
  ChartDataFrequency? frequency,
}) async {
  final bool hasPermission = await AppPermissions.checkPermissions(
    context,
    [AppPermission.storageAndroid],
  );

  if (!hasPermission) return;

  final notification = AppMessage.showOverlayNotificationSuccess(
    message: LocaleKeys.downloading.tr(),
  );

  final SeriesData seriesData = [];
  for (final collection in seriesList) {
    for (final element in collection) {
      final temp = JSONObject.from(element);
      if (frequency != null) {
        temp['OBS_DT'] = IndicatorDateSetting.setupNameAll(frequency.label, element['OBS_DT'] as String? ?? '');
      }
      seriesData.add(temp);
    }
  }

  final List<TableFields> tableFields = [];
  for (final element in tableFieldList ?? []) {
    if (seriesData.firstOrNull?.containsKey(element.path) ?? false) {
      tableFields.add(element as TableFields);
    }
  }

  final Workbook workbook = Workbook();
  final Worksheet sheet = workbook.worksheets[0]..showGridlines = true;

  //  Add Bayaan logo
  final bayaanLogo = await _getLogoAsBytes(AppImages.logoBayaanHorizontalPng, 50, 124);
  final bayaanLogoAsBytes = bayaanLogo.buffer.asUint8List();
  sheet.pictures.addStream(1, 1, bayaanLogoAsBytes);
  sheet.getRangeByName('A1:C3').merge();

  //  Add SCAD logo
  final scadLogo = await _getLogoAsBytes(AppImages.scadLogoHorz, 50, 140);
  final scadLogoAsBytes = scadLogo.buffer.asUint8List();
  sheet.pictures.addStream(1, 4, scadLogoAsBytes);
  sheet.getRangeByName('D1:F3').merge();

  //  Empty Row
  sheet.getRangeByName('A4:F4').merge();

  //  Title Row
  sheet.getRangeByName('A5:F5').merge();

  final Style globalStyle1 = workbook.styles.add('style1')
    ..backColor = '#D09A57'
    ..fontName = 'Arial'
    ..fontSize = 10
    ..fontColor = '#FFFFFF'
    ..bold = true
    ..hAlign = HAlignType.center
    ..vAlign = VAlignType.center;
  globalStyle1.borders.all.lineStyle = LineStyle.thin;
  globalStyle1.borders.all.color = '#000000';

  final Style globalStyle2 = workbook.styles.add('style2')
    ..backColor = '#D6FEE7'
    ..fontName = 'Calibri (Body)'
    ..fontSize = 11
    ..fontColor = '#000000'
    ..hAlign = HAlignType.left
    ..vAlign = VAlignType.center;
  globalStyle2.borders.all.lineStyle = LineStyle.thin;
  globalStyle2.borders.all.color = '#000000';

  final Style globalStyle3 = workbook.styles.add('style3')
    ..fontName = 'Calibri (Body)'
    ..fontSize = 11
    ..fontColor = '#000000'
    ..hAlign = HAlignType.left
    ..vAlign = VAlignType.center;

  final Style globalStyle4 = workbook.styles.add('style4')
    ..fontName = 'Arial'
    ..fontSize = 14
    ..fontColor = '#000000'
    ..bold = true
    ..hAlign = HAlignType.left
    ..vAlign = VAlignType.center;

  sheet.getRangeByName('A5')
    ..setText(title)
    ..cellStyle = globalStyle4
    ..cellStyle.hAlign = HAlignType.center;

  const int startRow = 7;
  if (tableFields.isNotEmpty) {
    sheet.setRowHeightInPixels(11, 60);
    int columnIndex = 0;
    for (final TableFields field in tableFields) {
      sheet.setColumnWidthInPixels(columnIndex + 1, 100);
      sheet.getRangeByName('${String.fromCharCode(65 + columnIndex)}${startRow - 1}')
        ..setText(field.label)
        ..cellStyle = globalStyle1;

      for (int i = 0; i < seriesData.length; i++) {
        if (seriesData[i][field.path].runtimeType == double) {
          sheet.getRangeByName('${String.fromCharCode(65 + columnIndex)}${startRow + i}')
            ..setText(double.parse(seriesData[i][field.path].toString()).toStringAsFixed(2))
            ..cellStyle = globalStyle3;
        } else {
          sheet.getRangeByName('${String.fromCharCode(65 + columnIndex)}${startRow + i}')
            ..setText(seriesData[i][field.path] != null ? seriesData[i][field.path].toString() : '')
            ..cellStyle = globalStyle3;
        }
      }

      columnIndex++;
    }
    sheet.getRangeByName(
      '''${String.fromCharCode(65)}${startRow + seriesData.length + 1}:${String.fromCharCode(65 + tableFields.length - 1)}${startRow + seriesData.length + 1}''',
    ).merge();

    sheet.getRangeByName('${String.fromCharCode(65)}${startRow + seriesData.length + 1}')
      ..setText(LocaleKeys.sensitiveInformation.tr())
      ..cellStyle = globalStyle2;
  } else {
    final SeriesData seriesPoints = [];
    for (final element in seriesData) {
      seriesPoints.add(element);
    }

    List<String> keys = [];
    SeriesData tempData = [];

    for (final SeriesData seriesData in seriesList) {
      if (keys.isEmpty) {
        sheet.setRowHeightInPixels(11, 60);
        int columnIndex = 0;
        if (seriesData.isNotEmpty) {
          keys = seriesData.first.keys.where((e) => tableHeaderTranslations.containsKey(e)).toList();
          for (final String key in keys) {
            if (key != 'legend_name' && key != 'legend_title') {
              final title = tableHeaderTranslations.containsKey(key) ? tableHeaderTranslations[key].toString() : key;

              sheet.setColumnWidthInPixels(columnIndex + 1, 130);
              sheet.getRangeByName(
                '${String.fromCharCode(65 + columnIndex)}${startRow - 1}',
              )
                ..setText(title)
                ..cellStyle = globalStyle1;
              columnIndex++;
            }
          }
        }
      }

      int columnIndex = 0;
      tempData = seriesPoints;
      for (int i = 0; i < tempData.toSet().toList().length; i++) {
        for (final String key in keys) {
          if (key != 'legend_name' && key != 'legend_title') {
            columnIndex = keys.indexOf(key);
            final value = tempData[i][key];
            if (value == null) {
              sheet.getRangeByName('${String.fromCharCode(65 + columnIndex)}${startRow + i}')
                ..setText('-')
                ..cellStyle = globalStyle3;
            } else {
              if (value is double) {
                sheet.getRangeByName('${String.fromCharCode(65 + columnIndex)}${startRow + i}')
                  ..setText(value.toStringAsFixed(3))
                  ..cellStyle = globalStyle3;
              } else {
                sheet.getRangeByName('${String.fromCharCode(65 + columnIndex)}${startRow + i}')
                  ..setText(value.toString())
                  ..cellStyle = globalStyle3;
              }
            }
          }
        }
      }
    }

    final endColumn = startRow + tempData.length + 1;
    final sensitiveRowRange = '''A$endColumn:${String.fromCharCode(65 + keys.length - 1)}$endColumn''';
    sheet.getRangeByName(sensitiveRowRange).merge();

    sheet.getRangeByName('${String.fromCharCode(65)}${startRow + tempData.length + 1}')
      ..setText(LocaleKeys.sensitiveInformation.tr())
      ..cellStyle = globalStyle2;
  }

  final List<int> bytes = workbook.saveSync();
  workbook.dispose();

  await FileUtils.saveFileAndNotify(
    bytes: bytes,
    filePath: FileUtils.joinPathParts(
      [FileUtils.dirPathIndicator, 'Indicator $title.xlsx'],
    ),
  );
  notification?.dismiss(animate: false);
}

Future<ByteData> _getLogoAsBytes(String imageAssetPath, int height, int width) async {
  final ByteData assetImageByteData = await rootBundle.load(imageAssetPath);
  final img.Image? baseSizeImage = img.decodeImage(assetImageByteData.buffer.asUint8List());
  final img.Image resizeImage = img.copyResize(baseSizeImage!, height: height, width: width);
  final ui.Codec codec = await ui.instantiateImageCodec(img.encodePng(resizeImage));
  final ui.FrameInfo frameInfo = await codec.getNextFrame();
  final ByteData? byteData = await frameInfo.image.toByteData(format: ui.ImageByteFormat.png);
  return byteData!;
}
