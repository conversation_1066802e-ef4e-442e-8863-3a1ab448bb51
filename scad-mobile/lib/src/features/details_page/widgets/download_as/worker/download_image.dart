part of '../download_as.dart';

Future<void> _saveImage(String title, Uint8List image) async {
  final notification = AppMessage.showOverlayNotificationSuccess(
    message: LocaleKeys.downloading.tr(),
  );

  final filePath = FileUtils.joinPathParts(
    [FileUtils.dirPathIndicator, 'Indicator $title.png'],
  );

  await FileUtils.saveFileAndNotify(
    bytes: image.toList(),
    filePath: filePath,
  );

  notification?.dismiss(animate: false);
}
