part of 'download_as.dart';

// TODO(Jerin): Handle rendering for longer description
// TODO(Jerin): Handle chart and legends separately for better rendering
class _DownloadAsPreviewDialog extends StatefulWidget {
  const _DownloadAsPreviewDialog._({
    required this.title,
    required this.description,
    required this.isPDF,
    required this.chart,
  });

  final String title;
  final String description;
  final bool isPDF;
  final Widget chart;

  static Future<void> show(
    BuildContext context, {
    required String title,
    required String description,
    required bool isPDF,
    required Widget chart,
  }) async {
    final bool hasPermission = await AppPermissions.checkPermissions(
      context,
      [AppPermission.storageAndroid],
    );

    if (!hasPermission || !context.mounted) return;

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: EdgeInsets.zero,
          insetPadding: const EdgeInsets.all(4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: AppColors.white,
          elevation: 0,
          content: _DownloadAsPreviewDialog._(
            title: title,
            description: description,
            chart: chart,
            isPDF: isPDF,
          ),
        );
      },
    );
  }

  @override
  State<_DownloadAsPreviewDialog> createState() => _DownloadAsPreviewDialogState();
}

class _DownloadAsPreviewDialogState extends State<_DownloadAsPreviewDialog> {
  final _screenshotController = ScreenshotController();
  final _chartScreenshotController = ScreenshotController();

  final _logoKey = GlobalKey(debugLabel: 'logoKey');
  final _titleKey = GlobalKey(debugLabel: 'titleKey');
  final _descriptionKey = GlobalKey(debugLabel: 'descriptionKey');
  final _chartKey = GlobalKey(debugLabel: 'chartKey');
  final _footerKey = GlobalKey(debugLabel: 'footerKey');

  bool _isDownloading = false;

  @override
  Widget build(BuildContext context) {
    final document = PdfDocument();
    final pdfPage = document.pages.add();
    final clientSize = pdfPage.getClientSize();
    document.dispose();

    final deviceWidth = MediaQuery.sizeOf(context).width;
    final renderWidth = min(deviceWidth, clientSize.width);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Flexible(
          child: SizedBox(
            width: renderWidth,
            child: AspectRatio(
              aspectRatio: _kA4AspectRatio,
              child: SingleChildScrollView(
                child: IgnorePointer(
                  child: _buildPreview(context),
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
            bottom: 12,
            top: 12,
            left: 16,
            right: 16,
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildButton(
                  text: widget.isPDF ? LocaleKeys.downloadPDF.tr() : LocaleKeys.downloadImage.tr(),
                  onTap: () => _onDownloadTapped(context),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildButton(
                  text: LocaleKeys.cancel.tr(),
                  onTap: () => Navigator.of(context).pop(),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPreview(BuildContext context) {
    return Center(
      child: Screenshot(
        controller: _screenshotController,
        child: Container(
          margin: const EdgeInsets.all(8),
          child: DecoratedBox(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.grey),
              color: AppColors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.all(1),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildLogo(),
                  const Divider(height: 8),
                  _buildTitle(),
                  const SizedBox(height: 10),
                  _buildDescription(),
                  const Divider(height: 10),
                  _buildChart(),
                  const Divider(height: 2),
                  _CopyrightFooter(
                    key: _footerKey,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButton({
    required String text,
    required VoidCallback onTap,
  }) {
    return TextButton(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.blueShade22,
        backgroundColor: AppColors.white,
        textStyle: AppTextStyles.s16w5cBlueLight,
        shape: RoundedRectangleBorder(
          side: const BorderSide(color: AppColors.blueShade22),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
      onPressed: onTap,
      child: Text(
        text,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildLogo() {
    return Padding(
      key: _logoKey,
      padding: const EdgeInsets.all(8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            AppImages.logoBayaanHorizontalPng,
            height: 30,
          ),
          const Spacer(),
          Image.asset(
            height: 40,
            AppImages.scadLogoHorz,
          ),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Padding(
      key: _titleKey,
      padding: const EdgeInsets.symmetric(horizontal: 6),
      child: Row(
        children: [
          Expanded(
            child: Text(
              widget.title,
              style: AppTextStyles.s16w5cBlackShade1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    if (widget.description.isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      key: _descriptionKey,
      padding: const EdgeInsets.only(bottom: 8, left: 6, right: 6),
      child: Text(
        widget.description,
        style: const TextStyle(
          fontSize: 13,
          color: AppColors.black,
        ),
      ),
    );
  }

  Widget _buildChart() {
    return Flexible(
      key: _chartKey,
      child: Screenshot(
        controller: _chartScreenshotController,
        child: IgnorePointer(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 13),
            child: widget.chart,
          ),
        ),
      ),
    );
  }

  Future<void> _onDownloadTapped(BuildContext context) async {
    if (_isDownloading) return;

    _isDownloading = true;

    final imageAsBytes = await _screenshotController.capture();
    if (imageAsBytes == null) {
      AppMessage.showOverlayNotificationError(
        message: LocaleKeys.somethingWentWrong.tr(),
      );
      _isDownloading = false;
      return;
    }

    if (!widget.isPDF) {
      await _saveImage(widget.title, imageAsBytes);
      _isDownloading = false;
      if (mounted) Navigator.of(this.context).pop();
      return;
    }

    if (context.mounted) {
      await _savePdf(this, widget.title);
      _isDownloading = false;
    }
  }
}

class _CopyrightFooter extends StatelessWidget {
  const _CopyrightFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      alignment: Alignment.centerLeft,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: Text(
              HiveUtilsSettings.isLanguageEnglish
                  ? 'Copyright © ${DateTime.now().year} Statistics Center Abu Dhabi. All Rights Reserved'
                  : 'حقوق النشر © ${DateTime.now().year} مركز الإحصاء بأبو ظبي. كافة الحقوق محفوظة',
              style: AppTextStyles.s10w5cWhite.copyWith(
                color: AppColors.blueGreyShade1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
