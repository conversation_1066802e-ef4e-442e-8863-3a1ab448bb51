part of 'download_as.dart';

class _DownloadRestrictedDelegate extends StatelessWidget {
  const _DownloadRestrictedDelegate();

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    final image = isLightMode ? AppImages.icDownloadRestrictedLight : AppImages.icDownloadRestrictedDark;

    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.colorF4E7E9 : AppColors.colorF4E7E9.withAlpha(36),
        borderRadius: BorderRadius.circular(8),
      ),
      child: IntrinsicHeight(
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: SvgPicture.asset(
                image,
                height: 29,
                width: 34,
              ),
            ),
            const VerticalDivider(
              color: AppColors.greyShade4,
              width: 1,
              indent: 0,
              endIndent: 0,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                child: Text(
                  LocaleKeys.indicatorDownloadRestricted.tr(),
                  style: TextStyle(
                    color: isLightMode ? AppColors.blueGreyShade1 : AppColors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _DownloadEnabledDelegate extends StatefulWidget {
  const _DownloadEnabledDelegate({
    required this.title,
    required this.description,
    required this.chart,
    required this.seriesList,
    required this.hasData,
    required this.isTableView,
    this.tableFieldList,
    this.frequency,
  });

  final String title;
  final String description;
  final Widget chart;
  final SeriesDataList seriesList;
  final List<TableFields>? tableFieldList;
  final ChartDataFrequency? frequency;
  final bool isTableView;
  final bool hasData;

  @override
  State<_DownloadEnabledDelegate> createState() => _DownloadEnabledDelegateState();
}

class _DownloadEnabledDelegateState extends State<_DownloadEnabledDelegate> {
  final isTermsAccepted = ValueNotifier(false);
  final isLightMode = HiveUtilsSettings.isLightMode;

  @override
  Widget build(BuildContext context) {
    final animationAsset = isLightMode ? AnimationAsset.animationDownload : AnimationAssetDark.animationDownload;

    return Opacity(
      opacity: widget.hasData ? 1 : 0.5,
      child: ValueListenableBuilder(
        valueListenable: isTermsAccepted,
        builder: (context, accepted, _) {
          return Column(
            children: [
              IgnorePointer(
                ignoring: !widget.hasData,
                child: Container(
                  height: 82,
                  decoration: BoxDecoration(
                    color: isLightMode ? const Color(0xFFEDEDEF) : AppColors.blueShade36,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: isLightMode ? AppColors.greyF3F4F6 : AppColors.blueShade36,
                    ),
                  ),
                  child: Row(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 18),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Lottie.asset(animationAsset),
                            const SizedBox(height: 6),
                            Text(
                              LocaleKeys.downloadAs.tr(),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: isLightMode ? AppColors.blueShade22 : AppColors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 82,
                          padding: const EdgeInsets.symmetric(horizontal: 14),
                          decoration: BoxDecoration(
                            color: isLightMode ? AppColors.greyF3F4F6 : AppColors.blueShade32,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: isLightMode ? AppColors.greyF3F4F6 : Colors.transparent,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildDownloadIcon(
                                enabled: !widget.isTableView,
                                text: LocaleKeys.pdf.tr(),
                                icon: isLightMode ? AppImages.icPdfLight : AppImages.icPdfDark,
                                onTap: () => _DownloadAsPreviewDialog.show(
                                  context,
                                  title: widget.title,
                                  description: widget.description,
                                  chart: widget.chart,
                                  isPDF: true,
                                ),
                              ),
                              _buildDownloadIcon(
                                text: LocaleKeys.excel.tr(),
                                icon: isLightMode ? AppImages.icXlsLight : AppImages.icXlsDark,
                                onTap: () => _saveXls(
                                  context,
                                  title: widget.title,
                                  seriesList: widget.seriesList,
                                  tableFieldList: widget.tableFieldList,
                                  frequency: widget.frequency,
                                ),
                              ),
                              _buildDownloadIcon(
                                enabled: !widget.isTableView,
                                text: LocaleKeys.image.tr(),
                                icon: isLightMode ? AppImages.icImgLight : AppImages.icImgDark,
                                onTap: () => _DownloadAsPreviewDialog.show(
                                  context,
                                  title: widget.title,
                                  description: widget.description,
                                  chart: widget.chart,
                                  isPDF: false,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 10),
              _buildTermsAndConditions(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDownloadIcon({
    required String text,
    required String icon,
    required VoidCallback onTap,
    bool enabled = true,
  }) {
    VoidCallback? onTapCallback;
    if (enabled) {
      if (isTermsAccepted.value) {
        onTapCallback = onTap;
      } else {
        onTapCallback = () => AppMessage.showOverlayNotificationError(
              message: LocaleKeys.termsAndConditionsWarning.tr(),
            );
      }
    }

    return Opacity(
      opacity: isTermsAccepted.value && enabled ? 1 : 0.5,
      child: InkWell(
        onTap: onTapCallback,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(icon),
            const SizedBox(height: 4),
            Text(
              text,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isLightMode ? AppColors.grey : AppColors.white,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTermsAndConditions() {
    final tapGesture = TapGestureRecognizer()
      ..onTap = () => AutoRouter.of(context).push(
            TermsAndConditionsScreenRoute(),
          );

    return Row(
      children: [
        IgnorePointer(
          ignoring: !widget.hasData,
          child: SizedBox.square(
            dimension: 16,
            child: Checkbox(
              checkColor: AppColors.white,
              activeColor: AppColors.blueLight,
              side: const BorderSide(color: AppColors.greyShade1),
              value: isTermsAccepted.value,
              onChanged: (value) {
                isTermsAccepted.value = value!;
              },
            ),
          ),
        ),
        const SizedBox(width: 10),
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: LocaleKeys.iAgreeTo.tr(),
                style: const TextStyle(
                  color: AppColors.grey,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: ' ${LocaleKeys.termsAndConditions.tr()}',
                recognizer: tapGesture,
                style: TextStyle(
                  color: isLightMode ? AppColors.blueLight : AppColors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
