import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/src/common/widgets/appbar/slim_appbar.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

part 'full_screen_expand_button.dart';

class FullScreenChartView extends StatelessWidget {
  const FullScreenChartView._({
    required this.title,
    required this.indicatorType,
    required this.chartBuilder,
    required this.legendBuilder,
    required this.titleSuffixBuilder,
  });

  final String title;
  final IndicatorType? indicatorType;
  final WidgetBuilder chartBuilder;
  final WidgetBuilder? legendBuilder;
  final WidgetBuilder? titleSuffixBuilder;

  static Future<void> show({
    required BuildContext context,
    required String title,
    required WidgetBuilder chartBuilder,
    WidgetBuilder? legendBuilder,
    WidgetBuilder? titleSuffixBuilder,
    IndicatorType? indicatorType,
  }) {
    return showDialog<void>(
      context: context,
      useSafeArea: false,
      builder: (context) => FullScreenChartView._(
        title: title,
        indicatorType: indicatorType,
        chartBuilder: chartBuilder,
        legendBuilder: legendBuilder,
        titleSuffixBuilder: titleSuffixBuilder,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    final size = MediaQuery.sizeOf(context);
    double chartHeight = size.width - 96;
    if (legendBuilder == null) chartHeight += 36;

    return Material(
      color: AppColors.scaffoldBackground,
      child: SafeArea(
        child: RotatedBox(
          quarterTurns: 1,
          child: Scrollbar(
            thumbVisibility: true,
            trackVisibility: true,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildAppBar(context),
                  Container(
                    height: chartHeight,
                    width: size.height,
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color: isLightMode ? AppColors.blueShade29 : AppColors.blueShade32,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: chartBuilder.call(context),
                  ),
                  if (legendBuilder != null)
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      alignment: Alignment.center,
                      child: legendBuilder!.call(context),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return SlimAppBar(
      titleWidget: Row(
        children: [
          Flexible(
            child: Text(
              title,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (indicatorType == IndicatorType.official)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SvgPicture.asset(
                AppImages.icOfficialActive,
              ),
            )
          else if (indicatorType == IndicatorType.experimental)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SvgPicture.asset(
                AppImages.icExperimentalActive,
              ),
            ),
        ],
      ),
      centerTitle: false,
      useMaterialNavigator: true,
      trailing: titleSuffixBuilder?.call(context),
    );
  }
}
