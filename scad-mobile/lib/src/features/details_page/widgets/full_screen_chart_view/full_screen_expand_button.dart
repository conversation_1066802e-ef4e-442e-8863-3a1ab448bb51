part of 'full_screen_chart_view.dart';

class FullScreenExpandButton extends StatelessWidget {
  const FullScreenExpandButton({
    required this.onTap,
    super.key,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Material(
      color: isLightMode ? AppColors.greyF3F4F6 : AppColors.blueShade36,
      borderRadius: BorderRadius.circular(70),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(70),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(70),
            border: Border.all(
              color: isLightMode ? AppColors.greyShade1 : AppColors.grey,
            ),
          ),
          child: SvgPicture.asset(
            MediaQuery.orientationOf(context) == Orientation.portrait ? AppImages.icExpand : AppImages.icCollapse,
            colorFilter: ColorFilter.mode(
              isLightMode ? AppColors.black : AppColors.white,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}
