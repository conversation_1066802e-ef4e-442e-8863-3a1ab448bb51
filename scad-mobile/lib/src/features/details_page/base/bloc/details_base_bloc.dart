import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';

abstract class DetailsBaseEvent extends Equatable {
  const DetailsBaseEvent();

  @override
  List<Object?> get props => [];
}

abstract class DetailsBaseState extends Equatable {
  const DetailsBaseState();

  @override
  List<Object?> get props => [];
}

class DetailsBaseVoidState extends DetailsBaseState {
  const DetailsBaseVoidState();

  @override
  List<Object?> get props => [];
}

abstract class DetailsBaseBloc extends Bloc<DetailsBaseEvent, DetailsBaseState> {
  DetailsBaseBloc() : super(const DetailsBaseVoidState());

  IndicatorDetailsResponse? _indicatorDetails;

  IndicatorDetailsResponse? get indicatorDetails => _indicatorDetails;

  @protected
  set indicatorDetails(IndicatorDetailsResponse? indicator) => _indicatorDetails = indicator;
}
