import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:scad_mobile/src/utils/app_utils/app_log.dart';
import 'package:scad_mobile/src/utils/app_utils/file_utils.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:screenshot/screenshot.dart';

class ScreenshotHelper {
  static String get chatScreenshotFileName => 'chat_capture.png';

  static String get chatScreenshotFileNameTemp => 'chat_capture_temp.png';

  static Future<String> chatScreenshotFilePath() async {
    return FileUtils.joinPathParts([FileUtils.dirPathChatCache, chatScreenshotFileName]);
  }

  static Future<void> capture({
    required BuildContext context,
    required ScreenshotController screenshotController,
    required String title,
  }) async {
    try {
      await cleanCaptureFile();

      final String? filePath = await screenshotController.captureAndSave(
        FileUtils.dirPathChatCache,
        fileName: chatScreenshotFileNameTemp,
      );

      if (filePath == null) return;

      final Uint8List uint8list = await ScreenshotController().captureFromWidget(
        ColoredBox(
          color: HiveUtilsSettings.isLightMode ? AppColors.white : AppColors.blueShade32,
          child: Column(mainAxisSize: MainAxisSize.min, children: [
            Padding(
              padding: const EdgeInsets.all(8),
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: HiveUtilsSettings.isLightMode ? AppColors.black : AppColors.white,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(8), child: Image.memory(File(filePath).readAsBytesSync())),
            ),
          ]),
        ),
      );

      final File file = await File(await chatScreenshotFilePath()).create(recursive: true);
      file.writeAsBytesSync(uint8list);

      AppLog.info('chat capture file: $filePath');
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
    }
  }

  static Future<void> cleanCaptureFile() async {
    final String path = FileUtils.dirPathChatCache;
    final File file = File(path + chatScreenshotFileNameTemp);
    if (file.existsSync()) {
      await file.delete();
    }
    final File file1 = File(path + chatScreenshotFileName);
    if (file1.existsSync()) {
      await file1.delete();
    }
  }
}
