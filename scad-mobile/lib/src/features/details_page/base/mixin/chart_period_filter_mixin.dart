import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart'
    as indicator_details;
import 'package:scad_mobile/translations/locale_keys.g.dart';

class ChartPeriodOption extends indicator_details.Options {
  ChartPeriodOption.fromOption(indicator_details.Options option)
      : super(
          id: option.id,
          label: option.label,
          value: option.value,
          unit: option.unit,
          isSelected: false,
        );

  ChartPeriodOption.recent()
      : super(
          id: 'Recent',
          label: LocaleKeys.recent.tr(),
          isSelected: false,
        );
}
