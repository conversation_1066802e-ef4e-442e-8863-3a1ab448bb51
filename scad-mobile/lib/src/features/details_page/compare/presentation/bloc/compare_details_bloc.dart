import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/details_page/base/bloc/details_base_bloc.dart';
import 'package:scad_mobile/src/features/details_page/compare/data/models/comparable_indicator.dart';
import 'package:scad_mobile/src/features/details_page/compare/domain/repositories/compare_details_repository.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'compare_details_event.dart';
part 'compare_details_state.dart';

class CompareDetailsBloc extends Bloc<CompareDetailsEvent, CompareDetailsState> {
  CompareDetailsBloc() : super(CompareIndicatorInitial()) {
    on<LoadIndicatorThemeEvent>(_onLoadDomainAndThemeIdsEvent);
    on<GetCompareIndicatorListEvent>(_onGetComparableIndicatorIndicatorsEvent);

    on<GetCompareIndicatorDetailsEvent>(_onGetCompareDetailsEvent);
  }

  IndicatorDetailsResponse? _indicatorDetails;

  IndicatorDetailsResponse? get indicatorDetails => _indicatorDetails;

  Future<void> _onLoadDomainAndThemeIdsEvent(
    LoadIndicatorThemeEvent event,
    Emitter<CompareDetailsState> emit,
  ) async {
    emit(LoadIndicatorThemeLoadingState());

    try {
      LoadIndicatorThemeBaseState state;
      if (event.screenerPayload == null) {
        state = await _loadOfficialIndicator(
          event.domainId!,
          event.classificationKey!,
          event.theme!,
          event.subTheme!,
        );
      } else {
        final result = await _getScreenerIndicators(event.screenerPayload!, event.isOfficial, 1);
        state = LoadExperimentalIndicatorSuccessState(indicatorList: result);
      }

      emit(state);
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        LoadIndicatorThemeErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }

  Future<LoadOfficialIndicatorSuccessState> _loadOfficialIndicator(
    String domainId,
    String classificationKey,
    String themeName,
    String subThemeName,
  ) async {
    final classificationId = await _getClassificationId(domainId, classificationKey);
    final theme = await _getTheme(domainId, classificationId, themeName);

    final subThemes = theme.subthemes ?? [];
    final index = subThemes.indexWhere(
      (element) => element.name == subThemeName,
    );

    if (index == -1) {
      throw Exception('SubTheme not found');
    }

    final subTheme = subThemes[index];
    final subThemeId = subTheme.id!;
    final result = await _getThemeIndicators(
      domainId,
      classificationId,
      subThemeId,
      theme.id!,
      1,
    );

    return LoadOfficialIndicatorSuccessState(
      domainId: domainId,
      classificationId: classificationId,
      themeId: theme.id!,
      subTheme: subTheme,
      indicatorList: result,
    );
  }

  Future<String> _getClassificationId(String domainId, String classificationKey) async {
    final body = {'domain_id': domainId};
    final response = await servicelocator<DomainsRepository>().getDomainClassificationList(body);

    if (!response.isSuccess) {
      throw Exception(response.errorMessage);
    }

    final classificationList = response.response ?? [];
    final index = classificationList.indexWhere(
      (element) => element.key == classificationKey,
    );

    final id = classificationList[index].id;
    if (id == null) {
      throw Exception('Classification ID not found');
    }

    return id;
  }

  Future<ThemeSubThemeResponse> _getTheme(String domainId, String classificationId, String themeName) async {
    final body = {
      'domain_id': domainId,
      'classification_id': classificationId,
    };
    final response = await servicelocator<DomainsRepository>().getThemeList(body);
    if (!response.isSuccess) {
      throw Exception(response.errorMessage);
    }

    final themes = response.response ?? [];
    final index = themes.indexWhere(
      (element) => element.name == themeName,
    );

    if (index == -1) {
      throw Exception('Theme not found');
    }

    return themes[index];
  }

  Future<void> _onGetComparableIndicatorIndicatorsEvent(
    GetCompareIndicatorListEvent event,
    Emitter<CompareDetailsState> emit,
  ) async {
    emit(GetCompareIndicatorListLoadingState());

    try {
      await Future<void>.delayed(const Duration(seconds: 2));
      final screener = event.screenerPayload;
      final result = screener == null
          ? await _getThemeIndicators(
              event.domainId!,
              event.classificationId!,
              event.subThemeId!,
              event.subDomainId!,
              event.pageNo,
            )
          : await _getScreenerIndicators(screener, event.isOfficial, event.pageNo);

      emit(
        GetCompareIndicatorListSuccessState(indicatorList: result),
      );
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        GetCompareIndicatorListErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }

  Future<ComparableIndicatorPageResult> _getThemeIndicators(
    String domainId,
    String classificationId,
    String subThemeId,
    String subDomainId,
    int pageNo,
  ) async {
    final payload = {
      'domain_id': domainId,
      'classification_id': classificationId,
      'sub_theme_id': subThemeId,
      'sub_domain_id': subDomainId,
      'page': pageNo.toString(),
    };
    final response = await servicelocator<DomainsRepository>().getThemeIndicatorList(payload);
    if (!response.isSuccess) {
      throw Exception(response.errorMessage);
    }

    if (response.response == null) {
      throw Exception('Theme Indicator List not found');
    }

    final themeIndicatorList = response.response!;
    return ComparableIndicatorPageResult(
      page: pageNo,
      total: themeIndicatorList.totalCount ?? 0,
      items: (themeIndicatorList.results ?? [])
          .map(
            (e) => ComparableIndicator.official(e),
          )
          .toList(),
    );
  }

  Future<ComparableIndicatorPageResult> _getScreenerIndicators(JSONObject screener, bool isOfficial, int pageNo) async {
    final classification = isOfficial ? 'official-insights' : 'innovative-insights';
    final response = await servicelocator<DomainsRepository>().getThemeExperimentalIndicatorList(
      classification: classification,
      pageNo: pageNo,
      filters: screener,
    );

    if (!response.isSuccess) {
      throw Exception(response.errorMessage);
    }

    final indicatorList = response.response!;
    return ComparableIndicatorPageResult(
      page: pageNo,
      total: indicatorList.totalCount ?? 0,
      items: (indicatorList.data ?? [])
          .map(
            (e) => ComparableIndicator.experimental(e),
          )
          .toList(),
    );
  }

  void resetBlocVariables() {
    _indicatorDetails = null;
  }

  Future<void> _onGetCompareDetailsEvent(
    GetCompareIndicatorDetailsEvent event,
    Emitter<CompareDetailsState> emit,
  ) async {
    emit(const GetCompareIndicatorDetailsLoadingState());

    try {
      final response = await servicelocator<CompareDetailsRepository>().compareIndicators(
        indicatorId1: event.indicatorId1,
        indicatorId2: event.indicatorId2,
      );

      if (response.isSuccess) {
        _indicatorDetails = response.response;

        emit(
          GetCompareIndicatorDetailsSuccessState(
            indicatorId1: event.indicatorId1,
            indicatorId2: event.indicatorId2,
            indicatorDetails: response.response!,
          ),
        );
      } else {
        emit(GetCompareIndicatorDetailsErrorState(error: response.errorMessage));
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(GetCompareIndicatorDetailsErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }
}
