part of 'compare_details_bloc.dart';

abstract class CompareDetailsState extends Equatable {
  const CompareDetailsState();

  @override
  List<Object> get props => [];
}

class CompareIndicatorInitial extends CompareDetailsState {}

abstract class GetCompareIndicatorDetailsBaseState extends CompareDetailsState {
  const GetCompareIndicatorDetailsBaseState();
}

class GetCompareIndicatorDetailsLoadingState extends GetCompareIndicatorDetailsBaseState {
  const GetCompareIndicatorDetailsLoadingState();
}

class GetCompareIndicatorDetailsErrorState extends GetCompareIndicatorDetailsBaseState {
  const GetCompareIndicatorDetailsErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class GetCompareIndicatorDetailsSuccessState extends GetCompareIndicatorDetailsBaseState {
  const GetCompareIndicatorDetailsSuccessState(
      {required this.indicatorDetails, required this.indicatorId1, required this.indicatorId2});

  final String indicatorId1;
  final String indicatorId2;
  final IndicatorDetailsResponse indicatorDetails;

  String get compareIndicatorId => '${indicatorId1}_$indicatorId2';

  @override
  List<Object> get props => [indicatorId1, indicatorId2, indicatorDetails.hashCode];
}

abstract class LoadIndicatorThemeBaseState extends CompareDetailsState {
  const LoadIndicatorThemeBaseState();
}

class LoadIndicatorThemeLoadingState extends LoadIndicatorThemeBaseState {}

class LoadIndicatorThemeErrorState extends LoadIndicatorThemeBaseState {
  const LoadIndicatorThemeErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class LoadOfficialIndicatorSuccessState extends LoadIndicatorThemeBaseState {
  const LoadOfficialIndicatorSuccessState({
    required this.domainId,
    required this.classificationId,
    required this.themeId,
    required this.subTheme,
    required this.indicatorList,
  });

  final String domainId;
  final String classificationId;
  final String themeId;
  final SubTheme subTheme;
  final ComparableIndicatorPageResult indicatorList;

  @override
  List<Object> get props => [
        domainId,
        classificationId,
        themeId,
        subTheme.hashCode,
        indicatorList.hashCode,
      ];
}

class LoadExperimentalIndicatorSuccessState extends LoadIndicatorThemeBaseState {
  const LoadExperimentalIndicatorSuccessState({
    required this.indicatorList,
  });

  final ComparableIndicatorPageResult indicatorList;

  @override
  List<Object> get props => [indicatorList.hashCode];
}

abstract class GetCompareIndicatorListBaseState extends CompareDetailsState {
  const GetCompareIndicatorListBaseState();
}

class GetCompareIndicatorListLoadingState extends GetCompareIndicatorListBaseState {}

class GetCompareIndicatorListErrorState extends GetCompareIndicatorListBaseState {
  const GetCompareIndicatorListErrorState({required this.error});

  final String error;

  @override
  List<Object> get props => [error];
}

class GetCompareIndicatorListSuccessState extends GetCompareIndicatorListBaseState {
  const GetCompareIndicatorListSuccessState({
    required this.indicatorList,
  });

  final ComparableIndicatorPageResult indicatorList;

  @override
  List<Object> get props => [indicatorList.hashCode];
}
