import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/utils/hive_utils/api_cache/api_cache.dart';

abstract class CompareDetailsRepository extends CacheableRepository {
  Future<RepoResponse<IndicatorDetailsResponse>> compareIndicators({
    required String indicatorId1,
    required String indicatorId2,
  });
}
