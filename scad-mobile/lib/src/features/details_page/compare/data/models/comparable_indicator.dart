import 'package:scad_mobile/src/features/domains/data/models/experimental_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_indicator_list_response.dart';

class ComparableIndicatorPageResult {
  ComparableIndicatorPageResult({
    required this.page,
    required this.total,
    required this.items,
  });

  final int page;
  final int total;
  final List<ComparableIndicator> items;
}

class ComparableIndicator {
  ComparableIndicator.official(ThemeIndicatorListItem item)
      : indicatorId = item.indicatorId,
        title = item.title,
        isComparable = item.isMultiDimension == false;

  ComparableIndicator.experimental(ExperimentalIndicatorListItem item)
      : indicatorId = item.indicatorId,
        title = item.title,
        isComparable = false;

  final String? indicatorId;
  final String? title;
  final bool isComparable;
}
