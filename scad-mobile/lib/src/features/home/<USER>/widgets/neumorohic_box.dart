import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';

class NeumorphicBox extends StatelessWidget {
  const NeumorphicBox({
    required this.height,
    required this.width,
    required this.child,
    this.borderRadius = 10,
    this.color,
    this.borderColor,
    super.key,
  });

  final double height;
  final double width;
  final Widget child;
  final double borderRadius;
  final Color? color;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        color: color ?? AppColors.scaffoldBackground,
       // border: Border.all(color: borderColor ?? AppColors.white),
        // boxShadow: const [
        //   BoxShadow(
        //     offset: Offset(0, -5),
        //     blurRadius: 15,
        //     color: Colors.black26,
        //     inset: true,
        //   ),
        //   BoxShadow(
        //     offset: Offset(0, 5),
        //     blurRadius: 8,
        //     color: Colors.black45,
        //     inset: true,
        //   ),
        // ],
      ),
      child: child,
    );
  }
}
