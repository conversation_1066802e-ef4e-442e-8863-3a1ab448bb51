import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/lazy_indicator_list_view/presentation/pages/lazy_indicator_list_view.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/key_indicator_list/key_indicator_list_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';

class KeyIndicatorList extends StatefulWidget {
  const KeyIndicatorList({
    required this.parentScrollController,
    super.key,
  });

  final ScrollController parentScrollController;

  @override
  State<KeyIndicatorList> createState() => _KeyIndicatorListState();
}

class _KeyIndicatorListState extends State<KeyIndicatorList> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  String? _errorMessage;

  final _allKeyIndicatorList = <KeyIndicatorListResponseItem>[];

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((timestamp) {
      _loadIndicators();
    });
  }

  void _loadIndicators() {
    _errorMessage = null;
    context.read<HomeBloc>().add(const KeyIndicatorsEvent());
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocConsumer<HomeBloc, HomeState>(
      listener: _homeBlocListener,
      buildWhen: (previousState, nextState) => nextState is KeyIndicatorsBaseState,
      builder: (context, state) {
        if (state is HomeInitial ||
            state is KeyIndicatorLoadingState ||
            (_allKeyIndicatorList.isEmpty && state is IndicatorHomeCurrentStatusState)) {
          return const Padding(
            padding: EdgeInsets.only(top: 150),
            child: CircularProgressIndicator(),
          );
        }

        if (_allKeyIndicatorList.isEmpty) {
          return const Padding(
            padding: EdgeInsets.only(top: 150),
            child: NoDataPlaceholder(),
          );
        }

        if (_errorMessage != null) {
          return ErrorReloadPlaceholder(
            padding: const EdgeInsets.only(top: 150),
            error: _errorMessage!,
            onReload: _loadIndicators,
          );
        }

        return LazyIndicatorListView<KeyIndicatorListResponseItem>(
          key: Key('keyIndicatorListView-${_allKeyIndicatorList.length}'),
          items: _allKeyIndicatorList,
          parentScrollController: widget.parentScrollController,
          padding: const EdgeInsets.fromLTRB(20, 15, 20, 0),
          getId: (item) => item.nodeId!,
          getContentType: (item) => item.contentType!,
          itemBuilder: (context, index, item, overview) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 25),
              child: IndicatorCardV2(
                key: Key(
                  'home.keyIndicators.IndicatorCardV2-${item.nodeId}',
                ),
                id: item.nodeId!,
                contentType: item.contentType!,
                overView: overview,
              ),
            );
          },
        );
      },
    );
  }

  void _homeBlocListener(BuildContext context, HomeState state) {
    switch (state) {
      case KeyIndicatorListSuccessState _:
        _allKeyIndicatorList
          ..clear()
          ..addAll(state.list);

      case KeyIndicatorErrorState _:
        _errorMessage = state.error;

      case IndicatorHomeCurrentStatusState _:
        for (final e in state.statusMap.entries) {
          final List<String> values = e.key.split('---');
          final index = _allKeyIndicatorList.indexWhere((element) {
            return element.nodeId == values.first && element.contentType == values[1] && e.value == 'error';
          });

          if (index != -1) {
            setState(
              () => _allKeyIndicatorList.removeAt(index),
            );
          }
        }
    }
  }
}
