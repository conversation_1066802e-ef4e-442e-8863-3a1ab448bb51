import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/lazy_indicator_list_view/presentation/pages/lazy_indicator_list_view.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/src/showcase_widget.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/recommended_indicator_list_item/recommended_indicator_list_item.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/features/onboarding/presentation/bloc/interest_domain_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class RecommendedIndicatorList extends StatefulWidget {
  const RecommendedIndicatorList({
    required this.stepKeys,
    required this.parentScrollController,
    super.key,
  });

  final List<GlobalKey> stepKeys;
  final ScrollController parentScrollController;

  @override
  State<RecommendedIndicatorList> createState() => _RecommendedIndicatorListState();
}

class _RecommendedIndicatorListState extends State<RecommendedIndicatorList> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  String? _errorMessage;

  int recommendedDomainTabIndex = 0;

  final _domainList = <DomainModel>[];
  final _recommendedIndicatorList = <RecommendedIndicatorListResponseItem>[];

  @override
  void initState() {
    super.initState();
    _loadRecommendedIndicators();
  }

  void _loadRecommendedIndicators() {
    _errorMessage = null;
    context.read<DomainsBloc>().add(const DomainsInitEvent());
    context.read<HomeBloc>().add(const RecommendedIndicatorsEvent());
    recommendedDomainTabIndex = 0;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocConsumer<InterestDomainBloc, InterestDomainState>(
      listener: (context, state) {
        if (state is InterestSavedState) {
          _loadRecommendedIndicators();
        }
      },
      builder: (context, state) {
        return BlocListener<DomainsBloc, DomainsState>(
          listener: _domainBlocListener,
          child: BlocConsumer<HomeBloc, HomeState>(
            listener: _homeBlocListener,
            buildWhen: (_, state) => state is RecommendedIndicatorBaseState,
            builder: (context, state) {
              if (_recommendedIndicatorList.isEmpty) {
                if (state is RecommendedIndicatorLoadingState || state is IndicatorHomeCurrentStatusState) {
                  return const Padding(
                    padding: EdgeInsets.only(top: 150),
                    child: CircularProgressIndicator(),
                  );
                }

                if (_errorMessage != null) {
                  return ErrorReloadPlaceholder(
                    padding: const EdgeInsets.only(top: 150),
                    error: _errorMessage!,
                    onReload: _loadRecommendedIndicators,
                  );
                }

                return const Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 100),
                    child: NoDataPlaceholder(),
                  ),
                );
              }

              final domainInterestList = _recommendedIndicatorList.map((e) => e.domain!).toSet().toList();

              final indicatorList = _recommendedIndicatorList
                  .where(
                    (element) => element.domain == domainInterestList[recommendedDomainTabIndex],
                  )
                  .toList();

              final isLightMode = HiveUtilsSettings.isLightMode;

              return Column(
                key: Key('recommendedIndicatorListView-${domainInterestList.join('.')}'),
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Divider(
                    height: 40,
                    color: AppColors.greyShade9,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Text(
                      LocaleKeys.statisticsInterest.tr(),
                      style: AppTextStyles.s16w5cBlueTitleText.copyWith(
                        color: !isLightMode ? AppColors.white : null,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  AppSlidingTab(
                    initialTabIndex: recommendedDomainTabIndex,
                    horizontalPadding: 24,
                    key: Key(
                      'recommended.domains.${domainInterestList.join('.')}',
                    ),
                    onTabChange: (i) => setState(
                      () => recommendedDomainTabIndex = i,
                    ),
                    tabs: domainInterestList
                        .map(
                          (e) => AppSlidingTabItem(
                            label: e,
                            iconUrl: _checkDomain(e),
                          ),
                        )
                        .toList(),
                  ),
                  LazyIndicatorListView<RecommendedIndicatorListResponseItem>(
                    key: const Key('recommendedIndicatorList'),
                    items: indicatorList,
                    getId: (item) => item.indicatorId!,
                    getContentType: (item) => item.type!,
                    parentScrollController: widget.parentScrollController,
                    padding: const EdgeInsets.fromLTRB(20, 15, 20, 0),
                    itemBuilder: _buildRecommendedIndicatorItem,
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildRecommendedIndicatorItem(
    BuildContext context,
    int index,
    RecommendedIndicatorListResponseItem item,
    OverView? overview,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 25),
      child: IndicatorCardV2(
        key: Key(
          'RecommendedIndicator:${item.indicatorId}-${item.contentType}',
        ),
        id: item.indicatorId!,
        contentType: item.type!,
        actionButtonsKey: index == 0 ? widget.stepKeys.elementAt(2) : null,
        openButtonKey: index == 0 ? widget.stepKeys.elementAt(3) : null,
        overView: overview,
        onUserGuideBackFromDetailsPage: (isPreviousActionTriggered) {
          if (!isPreviousActionTriggered) return;
          if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Home) {
            ShowCaseWidget.of(context).startShowCase(widget.stepKeys);
            ShowCaseWidget.of(context).jumpToId(widget.stepKeys.length - 1);
          }
        },
      ),
    );
  }

  void _domainBlocListener(BuildContext context, DomainsState state) {
    if (state is DomainShowResponseState) {
      _domainList
        ..clear()
        ..addAll(state.list);
    }

    if (state is DomainErrorState) {
      _errorMessage = state.error;
    }
  }

  void _homeBlocListener(BuildContext context, HomeState state) {
    if (state is RecommendedIndicatorListSuccessState) {
      _recommendedIndicatorList
        ..clear()
        ..addAll(state.list);
      recommendedDomainTabIndex = 0;
    }

    if (state is RecommendedIndicatorErrorState) {
      _errorMessage = state.error;
    }
  }

  String? _checkDomain(String e) {
    try {
      return _domainList
          .singleWhere(
            (element) => e == (DeviceType.isDirectionRTL(context) ? element.domainNameAr : element.domainName),
          )
          .domainIcon;
    } catch (e) {
      return '';
    }
  }
}
