import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:excel_view/excel_view.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/primary_icon_button.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/downloadHelper.dart';
import 'package:scad_mobile/src/utils/app_utils/download_service.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ExcelPreviewScreen extends StatefulWidget {
  const ExcelPreviewScreen({
    required this.url,
    required this.title,
    super.key,
  });

  final String title;
  final String url;

  @override
  State<ExcelPreviewScreen> createState() => _ExcelPreviewScreenState();
}

class _ExcelPreviewScreenState extends State<ExcelPreviewScreen> {
  String? _filePath;
  String? _error;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) => _downloadFile(),
    );
  }

  void _downloadFile() {
    try {
      DownloadService(
        widget.url,
        notify: false,
        directoryType: DownloadService.supportDir,
        folder: '.reports',
        onDownloadSuccess: (filename, filepath) {
          setState(() => _filePath = filepath);
        },
        onDownloadFailed: (error) {
          AppMessage.showOverlayNotificationError(
            message: LocaleKeys.unableToDownloadFile.tr(),
          );
        },
      ).start();
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      if (mounted) {
        AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
        context.back();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: FlatAppBar(
                    title: widget.title,
                    bottomPadding: 0,
                  ),
                ),
                if (kDebugMode)
                  PrimaryIconButton(
                    iconPath: AppImages.icDocument,
                    onTap: () => DownloadHelper.openFile(_filePath),
                  ),
              ],
            ),
            Expanded(
              child: Builder(
                builder: (context) {
                  if (_error != null) {
                    return ErrorReloadPlaceholder(error: _error!);
                  }

                  final filePath = _filePath;
                  if (filePath == null) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  return ExcelView(filePath: filePath);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    //  to-do: delete file from memory
    final filepath = _filePath;
    if (filepath != null) {
      File(filepath).deleteSync();
    }

    super.dispose();
  }
}
