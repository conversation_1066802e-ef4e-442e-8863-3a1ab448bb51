import 'dart:async';
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/maintenance/maintenance_view.dart';
import 'package:scad_mobile/src/common/widgets/primary_button.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/sla_check_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class MaintenanceScreen extends StatefulWidget {
  const MaintenanceScreen({
    required this.data,
    super.key,
  });

  final SlaCheckResponse data;

  @override
  State<MaintenanceScreen> createState() => _MaintenanceScreenState();
}

class _MaintenanceScreenState extends State<MaintenanceScreen> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state != AppLifecycleState.resumed) return;
    context.read<HomeBloc>().add(const SlaCheckEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listenWhen: (_, state) => state is SlaCheckBaseState,
      listener: _maintenanceStateListener,
      child: PopScope(
        canPop: false,
        child: Scaffold(
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Center(
                    child: MaintenanceView(
                      data: widget.data,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 24),
                  child: PrimaryButton(
                    text: LocaleKeys.close.tr(),
                    onTap: () {
                      if (context.router.canPop()) {
                        context.back();
                        return;
                      }

                      if (Platform.isAndroid) {
                        SystemNavigator.pop(animated: true);
                        return;
                      }

                      exit(0);
                    },
                    backgroundColor: AppColors.blueLightOld,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _maintenanceStateListener(BuildContext context, HomeState state) {
    if (state is! SlaCheckSuccessState) return;

    final slaResponse = state.access;
    final topRoute = context.router.stack.last.name;
    if (topRoute == GenAiChatPageRoute.name) return;

    final isUnderMaintenance = slaResponse.isFullMaintenance;
    if (isUnderMaintenance) return;
    unawaited(
      context.router.replaceAll([
        SplashPageRoute(),
      ]),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
