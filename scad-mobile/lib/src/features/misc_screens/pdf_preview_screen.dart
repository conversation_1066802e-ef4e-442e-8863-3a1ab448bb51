import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/download_service.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class PdfPreviewScreen extends StatefulWidget {
  const PdfPreviewScreen({
    required this.title,
    required this.url,
    super.key,
  });

  final String title;
  final String url;

  @override
  State<PdfPreviewScreen> createState() => _PdfPreviewScreenState();
}

class _PdfPreviewScreenState extends State<PdfPreviewScreen> {
  String? filePath;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) => _downloadFile(),
    );
  }

  void _downloadFile() {
    try {
      DownloadService(
        widget.url,
        notify: false,
        directoryType: DownloadService.supportDir,
        folder: '.reports',
        onDownloadSuccess: (filename, filepath) {
          setState(() => filePath = filepath);
        },
        onDownloadFailed: (error) {
          AppMessage.showOverlayNotificationError(
            message: LocaleKeys.unableToDownloadFile.tr(),
          );
        },
      ).start();
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      if (mounted) {
        AppMessage.showOverlayNotificationError(message: LocaleKeys.somethingWentWrong.tr());
        context.back();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppDrawer(
      child: Scaffold(
        body: Column(
          children: [
            FlatAppBar(
              title: widget.title,
              bottomPadding: 0,
            ),
            Expanded(
              child: filePath == null
                  ? const Center(child: CircularProgressIndicator())
                  : PDFView(
                      filePath: filePath,
                      autoSpacing: false,
                      // if set to true the link is handled in flutter
                      onRender: (pages) {
                        setState(() {
                          // pages = _pages;
                          // isReady = true;
                        });
                      },
                      onError: (error) {
                        // setState(() {
                        //   errorMessage = error.toString();
                        // });
                      },
                      onPageError: (page, error) {
                        // setState(() {
                        //   errorMessage = '$page: ${error.toString()}';
                        // });
                      },
                      // onViewCreated: (PDFViewController pdfViewController) {
                      //   _controller.complete(pdfViewController);
                      // },
                      // onLinkHandler: (String? uri) {
                      // },
                      // onPageChanged: (int? page, int? total) {
                      //   setState(() {
                      //     currentPage = page;
                      //   });
                      // },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
