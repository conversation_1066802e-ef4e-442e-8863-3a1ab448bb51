part of 'authentication_bloc.dart';

abstract class AuthenticationState extends Equatable {
  const AuthenticationState();

  @override
  List<Object> get props => [];
}

class AuthenticationInitial extends AuthenticationState {
  AuthenticationInitial() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class LoginLoadingState extends AuthenticationState {
  LoginLoadingState({
    required this.isResentOtp,
  }) {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;
  final bool isResentOtp;

  @override
  List<Object> get props => [rnd];
}

class LoginSubmittedState extends AuthenticationState {
  LoginSubmittedState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

/// emit when login success
class EmailLoginSuccessState extends AuthenticationState {
  EmailLoginSuccessState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class LoginSuccessState extends AuthenticationState {
  LoginSuccessState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class LoginFailureState extends AuthenticationState {
  LoginFailureState({required this.error}) {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;
  final String error;

  @override
  List<Object> get props => [error, rnd];
}

class NavToLogInState extends AuthenticationState {
  NavToLogInState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class NavToHomeState extends AuthenticationState {
  NavToHomeState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class SplashInitCheckState extends AuthenticationState {
  SplashInitCheckState() {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}
