part of 'authentication_bloc.dart';

abstract class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object> get props => [];
}

class UaePassLoginEvent extends AuthenticationEvent {
  const UaePassLoginEvent({
    required this.accessToken,
  });

  final String accessToken;

  @override
  List<Object> get props => [accessToken];
}

class EmailLoginSubmitEvent extends AuthenticationEvent {
  const EmailLoginSubmitEvent({
    required this.email,
    required this.password,
    required this.isResentOtp,
  });

  final String email;
  final String password;
  final bool isResentOtp;

  @override
  List<Object> get props => [email, password, isResentOtp];
}

class EmailLoginOtpVerifyEvent extends AuthenticationEvent {
  const EmailLoginOtpVerifyEvent({
    required this.email,
    required this.password,
    required this.otp,
    required this.deviceRegId,
    required this.uuid,
  });

  final String email;
  final String password;
  final String otp;
  final String deviceRegId;
  final String uuid;

  @override
  List<Object> get props => [email,password,deviceRegId,uuid];
}

class SplashInitCheckEvent extends AuthenticationEvent {
  const SplashInitCheckEvent();

  @override
  List<Object> get props => [];
}
