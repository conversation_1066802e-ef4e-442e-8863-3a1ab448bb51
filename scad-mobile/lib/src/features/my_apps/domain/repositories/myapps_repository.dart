part of 'myapps_repository_imports.dart';

abstract class MyAppsRepository extends CacheableRepository {
  Future<RepoResponse<MyAppsListResponse>> getIndicatorsOfAllCollection(
    RequestParamsMap requestParams,
  );

  Future<RepoResponse<List<CollectionResult>>> collectionsList();

  Future<RepoResponse<CreateCollectionResponse>> createCollection(
    CreateCollectionRequest payload,
  );

  Future<RepoResponse<CreateCollectionResponse>> updateCollection(
    String uuid,
    String name,
  );

  Future<RepoResponse<CreateCollectionResponse>> deleteCollection(
    String uuid,
  );

  Future<RepoResponse<AddToMyAppsResponse>> addToMyApps({
    required String indicatorId,
    required AddToMyAppsRequest payload,
  });

  Future<RepoResponse<AddToMyAppsResponse>> removeFromMyApps({
    required String indicatorId,
    required String uuid,
  });

  Future<RepoResponse<MyAppsListResponse>> collectionIndicatorsList(
    RequestParamsMap requestParams,
  );
}
