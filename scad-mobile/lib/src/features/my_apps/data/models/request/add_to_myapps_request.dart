class AddToMyAppsRequest {
  AddToMyAppsRequest({
    this.collection,
    this.nodeId,
    this.appType,
    this.contentType,
    this.key,
    this.title,
    this.domainName,
    this.name,
  });

  AddToMyAppsRequest.fromJson(Map<String, dynamic> json)
      : collection = json['collection'] as String?,
        nodeId = json['node_id'] as String?,
        appType = json['app_type'] as String?,
        contentType = json['content_type'] as String?,
        key = json['key'] as String?,
        title = json['title'] as String?,
        domainName = json['domain_name'] as String?,
        name = json['name'] as String?;

  final String? collection;
  final String? nodeId;
  final String? appType;
  final String? contentType;
  final String? key;
  final String? title;
  final String? domainName;
  final String? name;

  Map<String, dynamic> toJson() => {
        'collection': collection,
        'node_id': nodeId,
        'app_type': appType,
        'content_type': contentType,
        'key': key,
        'title': title,
        'domain_name': domainName,
        'name': name,
      };
}
