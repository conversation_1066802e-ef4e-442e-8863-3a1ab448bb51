class CreateCollectionResponse {
  CreateCollectionResponse({
    this.status,
    this.message,
    this.uuid,
  });

  CreateCollectionResponse.fromJson(Map<String, dynamic> json)
      : status = json['status'] as String?,
        uuid = json['uuid'] as String?,
        message = json['message'] as String?;

  final String? status;
  final String? message;
  final String? uuid;

  Map<String, dynamic> toJson() => {'status': status,'uuid': uuid, 'message': message};
}
