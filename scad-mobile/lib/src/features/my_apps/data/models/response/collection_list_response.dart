class CollectionListResponse {
  CollectionListResponse({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  CollectionListResponse.fromJson(Map<String, dynamic> json)
      : count = json['count'] as int?,
        next = json['next'] as int?,
        previous = json['previous'] as String?,
        results = (json['results'] as List?)
            ?.map((dynamic e) =>
                CollectionResult.fromJson(e as Map<String, dynamic>))
            .toList();
  final int? count;
  final int? next;
  final String? previous;
  final List<CollectionResult>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results?.map((e) => e.toJson()).toList(),
      };
}

class CollectionResult {
  CollectionResult({
    this.uuid,
    this.addedBy,
    this.editedBy,
    this.userprofile,
    this.name,
    this.myAppCount,
    this.active,
    this.createdTime,
    this.updatedTime,
  });

  CollectionResult.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        addedBy = (json['added_by'] as Map<String, dynamic>?) != null
            ? AddedBy.fromJson(json['added_by'] as Map<String, dynamic>)
            : null,
        editedBy = (json['edited_by'] as Map<String, dynamic>?) != null
            ? EditedBy.fromJson(json['edited_by'] as Map<String, dynamic>)
            : null,
        userprofile = (json['userprofile'] as Map<String, dynamic>?) != null
            ? Userprofile.fromJson(json['userprofile'] as Map<String, dynamic>)
            : null,
        name = json['name'] as String?,
        myAppCount = json['my_app_count'] as num?,
        active = json['active'] as bool?,
        createdTime = json['created_time'] as String?,
        updatedTime = json['updated_time'] as String?;
  final String? uuid;
  final AddedBy? addedBy;
  final EditedBy? editedBy;
  final Userprofile? userprofile;
  final String? name;
  final num? myAppCount;
  final bool? active;
  final String? createdTime;
  final String? updatedTime;

  Map<String, dynamic> toJson() => {
        'uuid': uuid,
        'added_by': addedBy?.toJson(),
        'edited_by': editedBy?.toJson(),
        'userprofile': userprofile?.toJson(),
        'name': name,
        'my_app_count': myAppCount,
        'active': active,
        'created_time': createdTime,
        'updated_time': updatedTime,
      };
}

class AddedBy {
  AddedBy({
    this.uuid,
    this.name,
  });

  AddedBy.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}

class EditedBy {
  EditedBy({
    this.uuid,
    this.name,
  });

  EditedBy.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}

class Userprofile {
  Userprofile({
    this.uuid,
    this.name,
  });

  Userprofile.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}
