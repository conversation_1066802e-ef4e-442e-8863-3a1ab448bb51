class MyAppsListResponse {
  MyAppsListResponse({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  MyAppsListResponse.fromJson(Map<String, dynamic> json)
      : count = json['count'] as int?,
        next = json['next'] as String?,
        previous = json['previous'] as String?,
        results = (json['results'] as List?)
            ?.map(
              (dynamic e) => MyAppsResults.fromJson(e as Map<String, dynamic>),
            )
            .toList();
  final int? count;
  final String? next;
  final String? previous;
  final List<MyAppsResults>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results?.map((e) => e.toJson()).toList(),
      };
}

class MyAppsResults {
  MyAppsResults({
    this.uuid,
    this.addedBy,
    this.editedBy,
    this.userprofile,
    this.collection,
    this.nodeId,
    this.appType,
    this.contentType,
    this.key,
    this.title,
    this.domainName,
    this.name,
  });

  MyAppsResults.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        addedBy = (json['added_by'] as Map<String, dynamic>?) != null
            ? AddedBy.fromJson(json['added_by'] as Map<String, dynamic>)
            : null,
        editedBy = (json['edited_by'] as Map<String, dynamic>?) != null
            ? EditedBy.fromJson(json['edited_by'] as Map<String, dynamic>)
            : null,
        userprofile = (json['userprofile'] as Map<String, dynamic>?) != null
            ? Userprofile.fromJson(json['userprofile'] as Map<String, dynamic>)
            : null,
        collection = (json['collection'] as Map<String, dynamic>?) != null
            ? Collection.fromJson(json['collection'] as Map<String, dynamic>)
            : null,
        nodeId = json['node_id'] as String?,
        appType = json['app_type'] as String?,
        contentType = json['content_type'] as String?,
        key = json['key'] as String?,
        title = json['title'] as String?,
        domainName = json['domain_name'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final AddedBy? addedBy;
  final EditedBy? editedBy;
  final Userprofile? userprofile;
  final Collection? collection;
  final String? nodeId;
  final String? appType;
  final String? contentType;
  final String? key;
  final String? title;
  final String? domainName;
  final String? name;

  Map<String, dynamic> toJson() => {
        'uuid': uuid,
        'added_by': addedBy?.toJson(),
        'edited_by': editedBy?.toJson(),
        'userprofile': userprofile?.toJson(),
        'collection': collection?.toJson(),
        'node_id': nodeId,
        'app_type': appType,
        'content_type': contentType,
        'key': key,
        'title': title,
        'domain_name': domainName,
        'name': name,
      };
}

class AddedBy {
  AddedBy({
    this.uuid,
    this.name,
  });

  AddedBy.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}

class EditedBy {
  EditedBy({
    this.uuid,
    this.name,
  });

  EditedBy.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}

class Userprofile {
  Userprofile({
    this.uuid,
    this.name,
  });

  Userprofile.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}

class Collection {
  Collection({
    this.uuid,
    this.name,
  });

  Collection.fromJson(Map<String, dynamic> json)
      : uuid = json['uuid'] as String?,
        name = json['name'] as String?;
  final String? uuid;
  final String? name;

  Map<String, dynamic> toJson() => {'uuid': uuid, 'name': name};
}
