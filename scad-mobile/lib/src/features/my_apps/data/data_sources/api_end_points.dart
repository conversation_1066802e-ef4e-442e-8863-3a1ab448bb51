import 'package:scad_mobile/src/config/app_config/api_config.dart';

class MyAppsEndPoints {
  static String appPath = ApiConfig.appApiPath;
  static String ifpPath = ApiConfig.ifpApiPath;

  static String getMyAppsList = '$appPath/my-apps/list/?offset={{page}}&limit=20';
  static String getMyAppsCollectionList = '$appPath/my-apps/collection/list/'; // ?offset=0&limit=20
  static String getMyAppsCollectionIndicatorsList =
      '$appPath/my-apps/collection/{{collection_id}}/list/?offset={{page}}&limit=20';

  static String addToMyApps = '$appPath/my-apps/create/';
  static String removeFromMyApps = '$appPath/my-apps/delete/';

  static String createMyAppsCollection = '$appPath/my-apps/collection/create/';
  static String updateMyAppsCollection = '$appPath/my-apps/collection/update/';
  static String deleteMyAppsCollection = '$appPath/my-apps/collection/delete/';
}
