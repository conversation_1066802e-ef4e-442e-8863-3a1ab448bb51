import 'dart:async';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/request/add_to_myapps_request.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/request/create_collection_request.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/add_to_myapps_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/create_collection_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/myapps_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/domain/repositories/myapps_repository_imports.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'myapps_event.dart';
part 'myapps_state.dart';

class MyAppsBloc extends Bloc<MyAppsEvent, MyAppsState> {
  MyAppsBloc() : super(MyAppsLoadingState()) {
    on<MyAppsLoadEvent>(_onLoadMyApps);
    on<AddToMyAppsEvent>(_onAddToMyApps);
    on<RemoveFromMyAppsEvent>(_onRemoveFromMyApps);
    on<RemoveIndicatorFromListEvent>(_onRemoveIndicatorFromList);
    on<CollectionsLoadEvent>(_onLoadCollection);
    on<CreateCollectionEvent>(_onCreateCollection);
    on<UpdateCollectionEvent>(_onUpdateCollection);
    on<DeleteCollectionEvent>(_onDeleteCollection);
    on<GetCollectionIndicatorsEvent>(_onLoadCollectionIndicators);
    on<CollectionSelectionEvent>(_onCollectionSelect);
    on<ReloadIndicatorListEvent>(_onCollectionReloadIndicatorList);
  }

  Future<void> _onLoadMyApps(
    MyAppsLoadEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    try {
      emit(MyAppsLoadingState());
      final RepoResponse<MyAppsListResponse> response =
          await servicelocator<MyAppsRepository>().getIndicatorsOfAllCollection({
        'page': event.pageNo.toString(),
      });
      if (response.isSuccess) {
        emit(MyAppsSuccessState(myAppsResponse: response.response!));
      } else {
        emit(
          MyAppsFailureState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(MyAppsFailureState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onLoadCollection(
    CollectionsLoadEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    try {
      if (event.showLoader) {
        emit(CollectionsLoadingState());
      }
      final RepoResponse<List<CollectionResult>> response =
          await servicelocator<MyAppsRepository>().collectionsList();
      if (response.isSuccess) {
        emit(
          CollectionsShowResponseState(
            collectionListResponse: response.response!,
          ),
        );
      } else {
        emit(
          CollectionsFailureState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(CollectionsFailureState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onAddToMyApps(
    AddToMyAppsEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    try {
      emit(AddToMyAppsLoadingState());
      final RepoResponse<AddToMyAppsResponse> response = await servicelocator<MyAppsRepository>().addToMyApps(
        indicatorId: event.indicatorId,
        payload: event.myAppsRequest,
      );

      if (response.isSuccess) {
        AppMessage.showOverlayNotificationSuccess(
          title: event.myAppsRequest.title ?? '',
          message: response.response?.message ?? '',
        );
        emit(
          MyAppsStatusSuccessResponseState(
            isRemoved: false,
            id: event.myAppsRequest.nodeId ?? '',
            message: response.response?.message ?? '',
          ),
        );
      } else {
        AppMessage.showOverlayNotificationError(message: response.errorMessage);
        emit(
          AddToMyAppsFailureState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(AddToMyAppsFailureState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onRemoveIndicatorFromList(
    RemoveIndicatorFromListEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    emit(RemoveIndicatorFromListState(id: event.id));
  }

  Future<void> _onRemoveFromMyApps(
    RemoveFromMyAppsEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    try {
      final RepoResponse<AddToMyAppsResponse> response = await servicelocator<MyAppsRepository>().removeFromMyApps(
        indicatorId: event.indicatorId,
        uuid: event.uuid,
      );
      if (response.isSuccess) {
        AppMessage.showOverlayNotificationSuccess(title: event.title ?? '', message: response.response?.message ?? '');
        emit(
          MyAppsStatusSuccessResponseState(
              isRemoved: true,
              id: event.id,
              message: response.response?.message ?? ''),
        );
      } else {
        emit(
          AddToMyAppsFailureState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(AddToMyAppsFailureState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onCreateCollection(
    CreateCollectionEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    try {
      emit(CreateCollectionLoadingState());
      final RepoResponse<CreateCollectionResponse> response =
          await servicelocator<MyAppsRepository>()
              .createCollection(event.request);
      if (response.isSuccess) {
        emit(
          CreateCollectionSuccessState(
            createCollectionResponse: response.response!,
          ),
        );
      } else {
        emit(
          CreateCollectionFailureState(error: response.errorMessage),
        );
      }
    } on DioException catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(CreateCollectionFailureState(
          error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onUpdateCollection(
    UpdateCollectionEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    try {
      emit(UpdateCollectionLoadingState(uuid: event.uuid));
      final RepoResponse<CreateCollectionResponse> response =
          await servicelocator<MyAppsRepository>()
              .updateCollection(event.uuid, event.name);
      if (response.isSuccess) {
        emit(
          UpdateCollectionSuccessState(
            createCollectionResponse: response.response!,
          ),
        );
      } else {
        emit(
          UpdateCollectionFailureState(error: response.errorMessage),
        );
      }
    } on DioException catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(UpdateCollectionFailureState(
          error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onDeleteCollection(
    DeleteCollectionEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    try {
      emit(DeleteCollectionLoadingState(uuid: event.uuid));
      final RepoResponse<CreateCollectionResponse> response =
          await servicelocator<MyAppsRepository>().deleteCollection(event.uuid);
      if (response.isSuccess) {
        emit(
          DeleteCollectionSuccessState(
            title: event.title,
            deleteCollectionResponseMessage: response.response!.message!,
          ),
        );
      } else {
        emit(
          DeleteCollectionFailureState(error: response.errorMessage),
        );
      }
    } on DioException catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(DeleteCollectionFailureState(
          error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onCollectionReloadIndicatorList(
    ReloadIndicatorListEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    emit(
      ReloadIndicatorListState(
        collectionUuid: event.collectionUuid,
      ),
    );
  }

  Future<void> _onCollectionSelect(
    CollectionSelectionEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    emit(
      CollectionSelectionState(
        selectedCollection: event.selectedCollection,
      ),
    );
  }

  Future<void> _onLoadCollectionIndicators(
    GetCollectionIndicatorsEvent event,
    Emitter<MyAppsState> emit,
  ) async {
    try {
      emit(MyAppsLoadingState());
      final RepoResponse<MyAppsListResponse> response =
          await servicelocator<MyAppsRepository>().collectionIndicatorsList({
        'collection_id': event.collectionUuid,
        'page': event.pageNo.toString(),
      });
      if (response.isSuccess) {
        emit(MyAppsSuccessState(myAppsResponse: response.response!));
      } else {
        emit(
          MyAppsFailureState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(MyAppsFailureState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }
}
