import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/grid_view_builder_domain.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/search_bar.dart';
import 'package:scad_mobile/src/features/search/presentation/pages/search/search_screen.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class BottomSheetModalDomain extends StatefulWidget {
  const BottomSheetModalDomain({super.key, required this.collection});

  final CollectionResult? collection;

  @override
  State<BottomSheetModalDomain> createState() => _BottomSheetModalDomainState();
}

class _BottomSheetModalDomainState extends State<BottomSheetModalDomain> {
  List<DomainModel> domains = [];
  final DomainsBloc _domainsBloc = DomainsBloc();

  @override
  void initState() {
    _domainsBloc.add(const DomainsInitEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    return OrientationBuilder(
      builder: (context, orientation) {
        return Container(
          decoration: BoxDecoration(
            color: isLightMode ? AppColors.white : AppColors.blueShade32,
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(20),
              topLeft: Radius.circular(20),
            ),
          ),
          padding: const EdgeInsets.only(left: 24, right: 24, bottom: 34),
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 24),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  context.router.removeWhere((route) => route.name == SearchScreenRoute.name);
                  context.pushRoute(SearchScreenRoute(type: SearchTypes.indicator));
                },
                child: const MyAppsSearchBar(
                    // onChanged: (searchQuery) {
                    //TODO: to get the confirmation
                    // context.read<DomainsBloc>().add(
                    //       DomainSearchEvent(
                    //         domains: domains,
                    //         searchQuery: searchQuery,
                    //       ),
                    //     );
                    // },
                    ),
              ),
              const SizedBox(height: 30),
              Text(
                LocaleKeys.chooseYourDomains.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.black : AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Flexible(
                child: BlocConsumer<DomainsBloc, DomainsState>(
                  bloc: _domainsBloc,
                  listener: (context, state) {
                    if (state is DomainShowResponseState) {
                      domains = state.list;
                    }
                    if (state is DomainSearchState) {
                      domains = state.searchResult;
                    }
                  },
                  builder: (context, state) {
                    if (state is DomainLoadingState) {
                      return const SizedBox(
                        height: 160,
                        child: Center(child: CircularProgressIndicator()),
                      );
                    } else if (state is DomainErrorState) {
                      return Text(state.error);
                    }
                    return GridViewBuilderDomain(
                      orientation: orientation,
                      collection: widget.collection,
                      domains: domains,
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
