import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/lazy_indicator_list_view/presentation/pages/lazy_indicator_list_view.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/myapps_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/my_apps_card.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/spatial_analytics_card.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class CollectionIndicators extends StatefulWidget {
  const CollectionIndicators({
    required this.title,
    required this.collection,
    super.key,
  });

  final String title;
  final CollectionResult? collection;

  @override
  State<CollectionIndicators> createState() => _CollectionIndicatorsState();
}

class _CollectionIndicatorsState extends State<CollectionIndicators> {
  List<MyAppsResults> indicatorList = [];

  int pageNo = -1;

  final _scrollController = ScrollController();

  int? count;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _pageReset();
      _loadData();
    });
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }

  void _pageReset() {
    indicatorList = [];
    count = null;
    pageNo = -1;
  }

  void _loadData() {
    if (count != null && indicatorList.length >= count!) return;

    pageNo++;
    if (widget.title == LocaleKeys.all.tr()) {
      context.read<MyAppsBloc>().add(
            MyAppsLoadEvent(pageNo: pageNo),
          );
    } else {
      context.read<MyAppsBloc>().add(
            GetCollectionIndicatorsEvent(
              pageNo: pageNo,
              collectionUuid: widget.collection?.uuid ?? '',
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FlatAppBar(
            title: widget.title,
            scrollController: _scrollController,
            mainScreen: true,
          ),
          if (!isDemoMode)
            Padding(
              padding: const EdgeInsets.only(left: 24, right: 24),
              child: MyAppsCard(
                isSmallWidget: true,
                collection: widget.collection,
              ),
            ),
          const SizedBox(
            height: 12,
          ),
          Expanded(
            child: BlocConsumer<MyAppsBloc, MyAppsState>(
              listener: _myAppBlocListener,
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    if (indicatorList.isEmpty)
                      state is MyAppsLoadingState
                          ? const Padding(
                              padding: EdgeInsets.only(top: 100),
                              child: Center(
                                child: CircularProgressIndicator(),
                              ),
                            )
                          : const Padding(
                              padding: EdgeInsets.only(top: 100),
                              child: Center(
                                child: NoDataPlaceholder(),
                              ),
                            )
                    else
                      const SizedBox(),
                    Expanded(
                      child: _buildIndicatorList(),
                    ),
                    if (state is MyAppsLoadingState && indicatorList.isNotEmpty)
                      const Padding(
                        padding: EdgeInsets.all(8),
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _myAppBlocListener(BuildContext context, MyAppsState state) {
    switch (state) {
      case ReloadIndicatorListState _:
        if (state.collectionUuid != widget.collection?.uuid) break;

        _pageReset();
        pageNo++;
        if (widget.title == LocaleKeys.all.tr()) {
          context.read<MyAppsBloc>().add(MyAppsLoadEvent(pageNo: pageNo));
        } else {
          context.read<MyAppsBloc>().add(
                GetCollectionIndicatorsEvent(
                  pageNo: pageNo,
                  collectionUuid: widget.collection?.uuid ?? '',
                ),
              );
        }

      case RemoveIndicatorFromListState _:
        final int i = indicatorList.indexWhere(
          (element) => element.nodeId == state.id,
        );

        if (i >= 0) {
          indicatorList.removeAt(i);
          count = count! - 1;
        }

      case MyAppsSuccessState _:
        count = state.myAppsResponse.count;

        (state.myAppsResponse.results ?? []).removeWhere(
          (element) => indicatorList.any(
            (element1) => element.nodeId == element1.nodeId,
          ),
        );

        indicatorList = [
          ...indicatorList,
          ...state.myAppsResponse.results ?? [],
        ];
    }
  }

  Widget _buildIndicatorList() {
    return LazyIndicatorListView(
      key: const Key('collectionIndicatorsListView'),
      items: indicatorList,
      scrollController: _scrollController,
      getId: (item) => item.nodeId!,
      getContentType: (item) => item.contentType!,
      padding: const EdgeInsets.fromLTRB(20, 15, 20, 150),
      itemBuilder: (context, index, item, overview) {
        final child = item.contentType == 'Spatial-Analytics'
            ? SpatialAnalyticsCard(
                id: item.nodeId ?? '-',
                postMyAppButtonTap: (bool isRemoved) {
                  if (isRemoved) {
                    context.read<MyAppsBloc>().add(
                          RemoveIndicatorFromListEvent(
                            id: item.nodeId!,
                          ),
                        );
                  }
                },
              )
            : IndicatorCardV2(
                key: Key('collection.indicators.IndicatorCardV2-${item.nodeId}'),
                id: item.nodeId!,
                contentType: item.contentType!,
                collection: widget.collection,
                comparedIndicatorName: item.name,
                overView: overview,
                postMyAppButtonTap: (bool isRemoved) {
                  if (isRemoved) {
                    context.read<MyAppsBloc>().add(
                          RemoveIndicatorFromListEvent(
                            id: item.nodeId!,
                          ),
                        );
                  }
                },
                postSubscriptionButtonTap: (bool isNotifyOn) {},
                domainName: item.domainName,
              );

        return Padding(
          padding: const EdgeInsets.only(bottom: 25),
          child: child,
        );
      },
      onScrollOverflow: _loadData,
    );
  }
}
