import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/showcaseview.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/persistent_bottom_nav_bar/persistent_bottom_nav_bar_v2.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/pages/collection_indicators.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/add_collection.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/collection_with_picture.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/create_new_collection.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/my_apps_card.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/suggestion_list.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class MyAppsLandingPage extends StatefulWidget {
  const MyAppsLandingPage({super.key});

  @override
  State<MyAppsLandingPage> createState() => _MyAppsLandingPageState();
}

class _MyAppsLandingPageState extends State<MyAppsLandingPage> {
  List<CollectionResult> collections = [
    CollectionResult(name: LocaleKeys.all.tr()),
  ];
  final scrollController = ScrollController();

  late Offset _tapDownPosition;

  String? deletingCollectionUuid;

  final GlobalKey addAppsKey = GlobalKey(debugLabel: 'my-apps-addAppsKey');
  final GlobalKey createCollectionKey = GlobalKey(debugLabel: 'my-apps-createCollectionKey');
  List<GlobalKey> steps = [];
  BuildContext? myContext;

  @override
  void initState() {
    super.initState();
    steps = [addAppsKey, createCollectionKey];
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        await _loadData();
        if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.MyDashboards) {
          ShowCaseWidget.of(myContext!).startShowCase(steps);
        }
      }
    });
  }

  Future<void> _loadData() async {
    deletingCollectionUuid = null;
    context.read<MyAppsBloc>().add(const CollectionsLoadEvent());
  }

  void scrollToWidget(GlobalKey key, {bool isPrevious = false}) {
    if (key.currentContext != null) {
      final RenderBox renderBox = key.currentContext!.findRenderObject()! as RenderBox;
      final position = renderBox.localToGlobal(Offset.zero);
      final screenHeight = MediaQuery.of(context).size.height;
      final scrollOffset = position.dy - screenHeight + (isPrevious ? -300 : 300);
      scrollController.jumpTo(scrollOffset);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ShowCaseWidget(
      builder: Builder(
        builder: (context) {
          myContext = context;
          return Scaffold(
            body: Column(
              children: [
                FlatAppBar(
                  title: LocaleKeys.myPanel.tr(),
                  mainScreen: true,
                  appBarHeight: 10,
                  scrollController: scrollController,
                ),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: _loadData,
                    child: SingleChildScrollView(
                      controller: scrollController,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 24, right: 24),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (!isDemoMode)
                              IntroWidget(
                                stepKey: addAppsKey,
                                stepIndex: 1,
                                totalSteps: steps.length,
                                onNext: () {
                                  scrollToWidget(steps.last);
                                  Future.delayed(const Duration(milliseconds: 500), () {
                                    setState(() {});
                                  });
                                },
                                title: LocaleKeys.addApps.tr(),
                                description: LocaleKeys.addAppsGuideDesc.tr(),
                                arrowAlignment: Alignment.topRight,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                targetBorderRadius: 20,
                                arrowPadding: EdgeInsets.only(
                                  right: MediaQuery.sizeOf(context).width * 0.2,
                                  left: MediaQuery.sizeOf(context).width * 0.2,
                                  bottom: 10,
                                ),
                                targetPadding: const EdgeInsets.all(10),
                                child: const MyAppsCard(
                                  isSmallWidget: false,
                                ),
                              ),
                            const SizedBox(height: 30),
                            BlocConsumer<MyAppsBloc, MyAppsState>(
                              listener: (context, state) {
                                if (state is CollectionsShowResponseState) {
                                  collections = [
                                    CollectionResult(
                                      name: LocaleKeys.all.tr(),
                                      myAppCount: 1,
                                    ),
                                    ...state.collectionListResponse,
                                  ];
                                }
                                if (state is DeleteCollectionFailureState) {
                                  AppMessage.showOverlayNotificationError(message: state.error);
                                  deletingCollectionUuid = null;
                                }
                                if (state is DeleteCollectionSuccessState) {
                                  collections.removeWhere(
                                    (element) => element.uuid == deletingCollectionUuid,
                                  );
                                  AppMessage.showOverlayNotificationSuccess(
                                    title: state.title,
                                    message: state.deleteCollectionResponseMessage,
                                  );
                                  deletingCollectionUuid = null;
                                }
                                if (state is DeleteCollectionLoadingState) {
                                  deletingCollectionUuid = state.uuid;
                                }
                                if (state is UpdateCollectionLoadingState) {
                                  deletingCollectionUuid = state.uuid;
                                }
                                if (state is UpdateCollectionSuccessState) {
                                  deletingCollectionUuid = null;
                                }
                              },
                              builder: (context, state) {
                                if (state is CollectionsLoadingState) {
                                  return const Center(
                                    child: CircularProgressIndicator(),
                                  );
                                } else if (state is CollectionsFailureState) {
                                  return Text(state.error);
                                } else if (state is CollectionsShowResponseState) {}

                                final crossAxisCount = DeviceType.isTab() ? 4 : 2;
                                return IgnorePointer(
                                  ignoring: deletingCollectionUuid != null,
                                  child: GridView.builder(
                                    primary: false,
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount: collections.length + (isDemoMode ? 0 : 1),
                                    padding: EdgeInsets.zero,
                                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisSpacing: 20,
                                      mainAxisSpacing: 20,
                                      crossAxisCount: crossAxisCount,
                                    ),
                                    itemBuilder: (_, int index) {
                                      if (collections.length == index && !isDemoMode) {
                                        return createNewCollectionButton(
                                          context,
                                        );
                                      }
                                      return collectionTile(index, context);
                                    },
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 30),
                            SuggestionList(
                              parentScrollController: scrollController,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  IntroWidget createNewCollectionButton(BuildContext context) {
    return IntroWidget(
      stepKey: createCollectionKey,
      stepIndex: 2,
      totalSteps: steps.length,
      title: LocaleKeys.createNewCollection.tr(),
      description: LocaleKeys.createNewCollectionGuideDesc.tr(),
      onPrevious: () {
        scrollToWidget(steps.first, isPrevious: true);
        Future.delayed(const Duration(milliseconds: 700), () {
          setState(() {});
        });
      },
      arrowAlignment: collections.length.isOdd ? Alignment.centerRight : Alignment.centerLeft,
      position: TooltipPosition.top,
      crossAxisAlignment: collections.length.isOdd ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      isDownArrow: true,
      targetBorderRadius: 20,
      arrowPadding: EdgeInsets.only(
        top: 10,
        right: MediaQuery.sizeOf(context).width * 0.2,
        left: MediaQuery.sizeOf(context).width * 0.2,
      ),
      targetPadding: const EdgeInsets.all(10),
      child: AddCollection(
        onTap: () {
          showModalBottomSheet<String>(
            isScrollControlled: true,
            useRootNavigator: true,
            constraints: BoxConstraints(
              minHeight: 100,
              maxHeight: MediaQuery.sizeOf(
                    context,
                  ).height *
                  .90,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            backgroundColor: AppColors.white,
            context: context,
            builder: (context) {
              return Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(
                    context,
                  ).viewInsets.bottom,
                ),
                child: const CreateNewCollection(),
              );
            },
          ).then((value) {
            if (value == 'success') {
              context.read<MyAppsBloc>().add(
                    const CollectionsLoadEvent(),
                  );
            }
          });
        },
      ),
    );
  }

  GestureDetector collectionTile(int index, BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return GestureDetector(
      onTapDown: (TapDownDetails details) {
        _tapDownPosition = details.globalPosition;
      },
      onLongPress: collections[index].name == LocaleKeys.all.tr()
          ? null
          : () async {
              final RenderBox overlay = Overlay.of(context).context.findRenderObject()! as RenderBox;

              await showMenu<String>(
                color: isLightMode ? AppColors.white : AppColors.blackShade5,
                surfaceTintColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                context: context,
                items: [
                  PopupMenuItem(
                    padding: EdgeInsets.zero,
                    onTap: () {
                      showModalBottomSheet<String>(
                        isScrollControlled: true,
                        useRootNavigator: true,
                        constraints: BoxConstraints(
                          minHeight: 100,
                          maxHeight: MediaQuery.sizeOf(
                                context,
                              ).height *
                              .90,
                        ),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(
                              20,
                            ),
                            topRight: Radius.circular(
                              20,
                            ),
                          ),
                        ),
                        backgroundColor: AppColors.white,
                        context: context,
                        builder: (context) {
                          return Padding(
                            padding: EdgeInsets.only(
                              bottom: MediaQuery.of(
                                context,
                              ).viewInsets.bottom,
                            ),
                            child: CreateNewCollection(
                              uuid: collections[index].uuid,
                              currentCollectionName: collections[index].name,
                            ),
                          );
                        },
                      ).then((value) {
                        if (value == 'success') {
                          context.read<MyAppsBloc>().add(
                                const CollectionsLoadEvent(),
                              );
                        }
                      });
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 18,
                            vertical: 10,
                          ),
                          child: Text(
                            LocaleKeys.edit.tr(),
                            style: AppTextStyles.s14w4cBlueTitleText.copyWith(
                              color: isLightMode ? AppColors.grey : AppColors.white,
                            ),
                          ),
                        ),
                        const Divider(
                          height: 1,
                          indent: 12,
                          endIndent: 12,
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    padding: EdgeInsets.zero,
                    onTap: () {
                      //todo delete confirm
                      context.read<MyAppsBloc>().add(
                            DeleteCollectionEvent(
                              title: collections[index].name!,
                              uuid: collections[index].uuid!,
                            ),
                          );
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 18,
                            vertical: 10,
                          ),
                          child: Text(
                            LocaleKeys.delete.tr(),
                            style: AppTextStyles.s14w4cBlueTitleText.copyWith(
                              color: isLightMode ? AppColors.grey : AppColors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                position: RelativeRect.fromLTRB(
                  _tapDownPosition.dx,
                  _tapDownPosition.dy,
                  overlay.size.width - _tapDownPosition.dx,
                  overlay.size.height - _tapDownPosition.dy,
                ),
              );
            },
      child: Stack(
        children: [
          Positioned.fill(
            child: CollectionWithPicture(
              name: collections[index].name ?? '',
              count: collections[index].myAppCount ?? 0,
              onTap: () async {
                // await AutoRouter.of(context).push(
                //   CollectionIndicatorsRoute(
                //     title: collections[index].name ?? '',
                //     collection: collections[index],
                //   ),
                // );
                await pushScreen(
                  context,
                  withNavBar: true,
                  screen: CollectionIndicators(
                    title: collections[index].name ?? '',
                    collection: collections[index],
                  ),
                );
                if (mounted) {
                  context.read<MyAppsBloc>().add(
                        const CollectionsLoadEvent(),
                      );
                }
              },
            ),
          ),
          if (deletingCollectionUuid != null && deletingCollectionUuid == collections[index].uuid)
            Positioned.fill(
              child: Container(
                padding: const EdgeInsets.only(
                  top: 5,
                  left: 5,
                  right: 5,
                  bottom: 5,
                ),
                decoration: BoxDecoration(
                  color: AppColors.black.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(
                    20,
                  ),
                ),
                child: const Center(
                  child: SizedBox(
                    height: 30,
                    width: 30,
                    child: CircularProgressIndicator(),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
