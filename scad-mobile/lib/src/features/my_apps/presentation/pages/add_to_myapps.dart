import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/bottom_sheet_top_notch.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_response_helper.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/request/add_to_myapps_request.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/collection_tiles.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/widgets/create_new_collection.dart';
import 'package:scad_mobile/src/utils/app_utils/app_message.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class AddToMyApps extends StatefulWidget {
  const AddToMyApps({
    required this.indicatorDetails,
    required this.nodeId,
    required this.contentType,
    super.key,
    this.collection,
    this.isComparisonActive = false,
  });

  final String nodeId;
  final String contentType;
  final IndicatorDetailsResponseHelper indicatorDetails;
  final CollectionResult? collection;
  final bool isComparisonActive;

  @override
  State<AddToMyApps> createState() => _AddToMyAppsState();
}

class _AddToMyAppsState extends State<AddToMyApps> {
  List<CollectionResult> collections = [];
  final ScrollController _scrollController = ScrollController();
  CollectionResult? selectedCollection;
  TextEditingController controller = TextEditingController();
  String? createdUuid = null;

  @override
  void initState() {
    context.read<MyAppsBloc>().add(const CollectionsLoadEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    final rtl = DeviceType.isDirectionRTL(context);

    return OrientationBuilder(
      builder: (context, orientation) {
        return Container(
          padding: EdgeInsets.only(
            left: 24,
            right: 14,
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(
                20,
              ),
            ),
            color: isLightMode ? AppColors.white : AppColors.blueShade32,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              const BottomSheetTopNotch(),
              const SizedBox(height: 16),
              Text(
                LocaleKeys.saveTo.tr(),
                style: TextStyle(
                  color: isLightMode ? AppColors.black : AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 10),
              BlocConsumer<MyAppsBloc, MyAppsState>(
                listener: (context, state) {
                  if (state is CollectionsShowResponseState) {
                    collections = [
                      ...[CollectionResult(name: LocaleKeys.all.tr())],
                      ...state.collectionListResponse,
                    ];

                    if ((widget.collection?.uuid ?? '').isNotEmpty) {
                      final CollectionResult selected = collections.firstWhere(
                        (element) => element.uuid == widget.collection?.uuid,
                        orElse: () => CollectionResult(),
                      );

                      if (selected.uuid != null) {
                        selectedCollection = selected;
                      }
                    } else if (createdUuid != null) {
                      final CollectionResult selected = collections.firstWhere(
                        (element) => element.uuid == createdUuid,
                        orElse: () => CollectionResult(),
                      );

                      if (selected.uuid != null) {
                        selectedCollection = selected;
                      }
                    } else {
                      selectedCollection = collections.first;
                    }
                  } else if (state is CreateCollectionSuccessState) {
                    createdUuid = state.createCollectionResponse.uuid;
                  }
                  if (state is CollectionSelectionState) {
                    selectedCollection = state.selectedCollection;
                  }
                  if (state is MyAppsStatusSuccessResponseState) {
                    Navigator.pop(context);
                    context.read<MyAppsBloc>().add(const CollectionsLoadEvent());
                  }
                },
                builder: (context, state) {
                  if (state is CollectionsLoadingState) {
                    return const SizedBox(
                      height: 60,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  } else if (state is CollectionsFailureState) {
                    return Text(state.error);
                  } else if (state is CollectionsShowResponseState) {}
                  return Flexible(
                    child: Scrollbar(
                      controller: _scrollController,
                      thumbVisibility: true,
                      child: ListView.builder(
                        shrinkWrap: true,
                        controller: _scrollController,
                        padding: rtl ? const EdgeInsets.only(left: 10) : const EdgeInsets.only(right: 10),
                        itemCount: collections.length,
                        itemBuilder: (BuildContext context, int index) {
                          return CollectionTiles(
                            name: collections[index].name ?? '',
                            count: (collections[index].myAppCount ?? 0).toInt(),
                            isSelected: selectedCollection?.uuid == collections[index].uuid,
                            onTap: () {
                              context.read<MyAppsBloc>().add(
                                    CollectionSelectionEvent(
                                      selectedCollection: collections[index],
                                    ),
                                  );
                            },
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
              if (widget.isComparisonActive) ...[
                const SizedBox(height: 16),
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: LocaleKeys.compareIndicatorLabel.tr(),
                        style: TextStyle(
                          color: isLightMode ? AppColors.greyShade4 : AppColors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      const WidgetSpan(child: SizedBox(width: 5)),
                      TextSpan(
                        text: '*',
                        style: AppTextStyles.s14w4cBlue.copyWith(
                          color: AppColors.redWarning,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                Form(
                  child: TextField(
                    autofocus: true,
                    controller: controller,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.black,
                    ),
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.only(
                        left: 10,
                        right: 10,
                        bottom: 15,
                        top: 15,
                      ),
                      fillColor: AppColors.white,
                      filled: true,
                      border: const OutlineInputBorder(
                        borderSide: BorderSide(color: AppColors.greyShade1),
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(color: AppColors.greyShade1),
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide(color: AppColors.greyShade1),
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                      ),
                      hintText: LocaleKeys.enterHere.tr(),
                      hintStyle: const TextStyle(
                        color: AppColors.greyShade1,
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 20),
              if (!isDemoMode)
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: isLightMode
                          ? const BorderSide(
                              color: AppColors.blueShade22,
                            )
                          : const BorderSide(
                              color: AppColors.white,
                            ),
                    ),
                    textStyle: const TextStyle(
                      color: AppColors.selectedChipBlue,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                    minimumSize: const Size.fromHeight(50),
                    backgroundColor: Colors.transparent,
                  ),
                  onPressed: () {
                    showModalBottomSheet<String>(
                      isScrollControlled: true,
                      useRootNavigator: true,
                      constraints: BoxConstraints(
                        minHeight: 100,
                        maxHeight: MediaQuery.sizeOf(context).height * .90,
                      ),
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                      backgroundColor: AppColors.white,
                      context: context,
                      builder: (context) {
                        return Padding(
                          padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom,
                          ),
                          child: const CreateNewCollection(),
                        );
                      },
                    ).then((value) {
                      if (value == 'success') {
                        context.read<MyAppsBloc>().add(const CollectionsLoadEvent());
                      }
                    });
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        LocaleKeys.createStartNewCollection.tr(),
                        style: TextStyle(
                          overflow: TextOverflow.ellipsis,
                          color: isLightMode ? AppColors.blueShade22 : AppColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      if (isLightMode)
                        SvgPicture.asset('assets/images/plus_circle.svg')
                      else
                        SvgPicture.asset('assets/images/plus_circle_white.svg')
                    ],
                  ),
                ),
              const SizedBox(
                height: 20,
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  textStyle: const TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                  minimumSize: const Size.fromHeight(50),
                ),
                onPressed: () {
                  if (collections.isEmpty) {
                    AppMessage.showOverlayNotificationError(message: LocaleKeys.pleaseWait.tr());
                  } else if (widget.isComparisonActive && controller.text.trim().isEmpty) {
                    AppMessage.showOverlayNotificationError(message: LocaleKeys.compareIndicatorError.tr());
                  } else {
                    addToMyApps();
                  }
                },
                child: Text(
                  LocaleKeys.done.tr(),
                  style: const TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  void addToMyApps() {
    final details = widget.indicatorDetails;
    final indicatorId = widget.indicatorDetails.indicatorDetails.id ?? '';
    print('_AddToMyAppsState.addToMyApps: $indicatorId');

    context.read<MyAppsBloc>().add(
          AddToMyAppsEvent(
            indicatorId: indicatorId,
            myAppsRequest: AddToMyAppsRequest(
              key: (details.indicatorDetails.contentClassificationKey ??
                      details.indicatorDetails.contentClassification) ??
                  '',
              title: details.title,
              nodeId: widget.nodeId,
              appType: details.indicatorDetails.type,
              collection: selectedCollection?.name == LocaleKeys.all.tr() ? null : selectedCollection?.name,
              contentType: widget.contentType,
              domainName: details.domainName,
              name: controller.text.isNotEmpty ? controller.text : '',
            ),
          ),
        );
  }
}
