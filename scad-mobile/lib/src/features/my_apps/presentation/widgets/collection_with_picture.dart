import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class CollectionWithPicture extends StatelessWidget {
  const CollectionWithPicture({
    required this.name,
    required this.onTap,
    required this.count,
    super.key,
  });

  final num count;
  final String name;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      margin: EdgeInsets.zero,
      elevation: 1,
      surfaceTintColor: Colors.transparent,
      color: isLightMode ? AppColors.white : AppColors.blueShade32,
      child: InkWell(
        customBorder: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.only(top: 5, left: 5, right: 5, bottom: 5),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: isLightMode
                        ? AppColors.greyShade8
                        : AppColors.blueShade36,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: count == 0
                      ? Container(color: Colors.transparent)
                      : ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: SvgPicture.asset(
                            HiveUtilsSettings.isLightMode
                                ? AppImages.imgDashboardLight
                                : AppImages.imgDashboardDark,
                            width: double.infinity,
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 6),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  name,
                  style: AppTextStyles.s14w4cBlueTitleText.copyWith(
                    color: isLightMode ? AppColors.grey : AppColors.white,
                  ),
                ),
              ),
              const SizedBox(height: 6),
            ],
          ),
        ),
      ),
    );
  }
}
