import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/lazy_indicator_list_view/presentation/pages/lazy_indicator_list_view.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/recommended_indicator_list_item/recommended_indicator_list_item.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class SuggestionList extends StatefulWidget {
  const SuggestionList({
    required this.parentScrollController,
    super.key,
  });

  final ScrollController parentScrollController;

  @override
  State<SuggestionList> createState() => _SuggestionListState();
}

class _SuggestionListState extends State<SuggestionList> {
  final List<RecommendedIndicatorListResponseItem> recommendedIndicatorList =
      [];

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        context.read<HomeBloc>().add(const RecommendedIndicatorsEvent());
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (isDemoMode) {
      return const SizedBox();
    }

    final isLightMode = HiveUtilsSettings.isLightMode;
    final rtl = DeviceType.isDirectionRTL(context);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
          alignment: rtl ? Alignment.centerRight : Alignment.centerLeft,
          child: Text(
            LocaleKeys.recommendedForYou.tr(),
            style: TextStyle(
              color: isLightMode ? AppColors.black : AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        BlocConsumer<HomeBloc, HomeState>(
          listener: _homeBlocListener,
          builder: (context, state) {
            return _buildRecommendedIndicatorList(state);
          },
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  void _homeBlocListener(BuildContext context, HomeState state) {
    if (state is RecommendedIndicatorListSuccessState) {
      recommendedIndicatorList
        ..clear()
        ..addAll(state.list);
    }
  }

  Widget _buildRecommendedIndicatorList(HomeState state) {
    if (recommendedIndicatorList.isEmpty) {
      if (state is RecommendedIndicatorLoadingState) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 100,
            ),
            child: CircularProgressIndicator(),
          ),
        );
      }

      return const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 100),
          child: NoDataPlaceholder(),
        ),
      );
    }

    return LazyIndicatorListView<RecommendedIndicatorListResponseItem>(
      key: const Key('suggestionIndicatorListView'),
      items: recommendedIndicatorList,
      parentScrollController: widget.parentScrollController,
      padding: const EdgeInsets.only(top: 20, bottom: 150),
      getId: (item) => item.indicatorId!,
      getContentType: (item) => item.type!,
      itemBuilder: (context, index, item, overview) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 25),
          child: IndicatorCardV2(
            key: Key(
              'collection.recommended.IndicatorCardV2-${item.indicatorId}',
            ),
            id: item.indicatorId!,
            contentType: item.type!,
            overView: overview,
          ),
        );
      },
    );
  }
}
