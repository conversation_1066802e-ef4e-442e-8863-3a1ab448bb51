import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class CollectionTiles extends StatelessWidget {
  const CollectionTiles({
    required this.name,
    required this.isSelected,
    required this.onTap,
    required this.count,
    super.key,
  });

  final String name;
  final bool isSelected;
  final VoidCallback onTap;
  final int count;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Card(
      margin: const EdgeInsets.only(top: 10),
      elevation: 0.1,
      color: isLightMode ? AppColors.white : AppColors.blueShade36,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(
          color: isSelected
              ? AppColors.blue
              : isLightMode
                  ? AppColors.greyShade1
                  : AppColors.greyBorder,
        ),
      ),
      child: InkWell(
        customBorder: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        // splashColor: AppColors.lightBlueContainer,
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.only(top: 5, left: 5, right: 5, bottom: 5),
          child: Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: isLightMode ? AppColors.greyShade8 : AppColors.blueShade32,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: count != 0 || name == LocaleKeys.all.tr()
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: SvgPicture.asset(
                          isLightMode ? AppImages.imgDashboardLight : AppImages.imgDashboardDark,
                          height: 88,
                          width: 110,
                        ),
                      )
                    : const SizedBox(height: 88, width: 110),
              ),
              const SizedBox(width: 25),
              Expanded(
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  name,
                  style: AppTextStyles.s14w4cBlueTitleText.copyWith(
                    color: isLightMode ? AppColors.grey : AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 10),
            ],
          ),
        ),
      ),
    );
  }
}
