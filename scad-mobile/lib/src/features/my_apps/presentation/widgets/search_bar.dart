import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class MyAppsSearchBar extends StatelessWidget {
  const MyAppsSearchBar({super.key, this.onChanged});

  final void Function(String)? onChanged;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: TextField(
        enabled: false,
        onChanged: onChanged,
        style: const TextStyle(fontSize: 14),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.only(left: 20, right: 20),
          fillColor: AppColors.whiteShade5,
          filled: true,
          border: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.all(Radius.circular(60)),
          ),
          enabledBorder: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.all(Radius.circular(60)),
          ),
          focusedBorder: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.all(Radius.circular(60)),
          ),
          hintStyle: const TextStyle(
            color: AppColors.grey,
            fontSize: 14,
            fontWeight: FontWeight.w300,
          ),
          hintText: LocaleKeys.searchApps.tr(),
          suffixStyle: const TextStyle(height: 1),
          suffixIcon: Padding(
            padding: const EdgeInsets.all(18),
            child: SvgPicture.asset(
              'assets/images/search_icon.svg',
            ),
          ),
        ),
      ),
    );
  }
}
