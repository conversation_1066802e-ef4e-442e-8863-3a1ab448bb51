import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';

class DomainButtonAndName extends StatelessWidget {
  const DomainButtonAndName({
    required this.icon,
    required this.name,
    required this.onTap,
    super.key,
  });

  final String icon;
  final String name;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return OrientationBuilder(
      builder: (context, orientation) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            children: [
              SizedBox(
                width: 66,
                height: 66,
                child: FilledButton(
                  onPressed: onTap,
                  style: ElevatedButton.styleFrom(
                    shape: const CircleBorder(),
                    backgroundColor: isLightMode
                        ? AppColors.whiteShade3
                        : AppColors.blueShade17,
                    padding: const EdgeInsets.all(18),
                    maximumSize: const Size(74, 74),
                  ),
                  child: Padding(
                    padding: orientation == Orientation.portrait
                        ? EdgeInsets.zero
                        : const EdgeInsets.all(30),
                    child: FittedBox(
                      child: SvgPicture.network(
                        icon,
                        width: 60,
                        height: 60,
                        colorFilter: ColorFilter.mode(
                          isLightMode ? AppColors.blue : AppColors.white,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                '$name\n',
                maxLines: 2,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isLightMode ? AppColors.grey : AppColors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
