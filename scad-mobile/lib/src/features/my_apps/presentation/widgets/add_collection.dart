import 'package:dotted_border/dotted_border.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/src/common/widgets/app_box_shadow.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class AddCollection extends StatelessWidget {
  const AddCollection({required this.onTap, super.key});
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Container(
      decoration: BoxDecoration(
        boxShadow: isLightMode ? AppBox.shadow() : null,
      ),
      child: InkWell(
        customBorder: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        splashColor: AppColors.lightBlueContainer,
        onTap: onTap,
        child: DottedBorder(
          borderType: BorderType.RRect,
          dashPattern: const [4, 4],
          color: AppColors.greyShade4,
          radius: const Radius.circular(20),
          child: SizedBox(
            width: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/images/small_plus.svg',
                  colorFilter: ColorFilter.mode(
                    isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                    BlendMode.srcIn,
                  ),
                ),
                const SizedBox(height: 11),
                Text(
                  LocaleKeys.newCollection.tr(),
                  style: AppTextStyles.s14w4cBlueTitleText.copyWith(
                      color: !isLightMode ? AppColors.white : null,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
