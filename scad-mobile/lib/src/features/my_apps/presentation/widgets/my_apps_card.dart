import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/landing_top_button.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/pages/bottom_modal_domian.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/animation_asset.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class MyAppsCard extends StatelessWidget {
  const MyAppsCard({
    required this.isSmallWidget,
    this.collection,
    super.key,
  });

  final bool isSmallWidget;
  final CollectionResult? collection;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return LandingTopButton(
      isSmallWidget: isSmallWidget,
      onTap: () async {
        await showModalBottomSheet<void>(
          isScrollControlled: true,
          useRootNavigator: true,
          constraints: BoxConstraints(
            minHeight: 100,
            maxHeight: MediaQuery.sizeOf(context).height * .90,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          backgroundColor: Colors.transparent,
          context: context,
          builder: (BuildContext context) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: BottomSheetModalDomain(collection: collection),
            );
          },
        );
      },
      label: LocaleKeys.addApps.tr(),
      animatedIcon: isLightMode
          ? AnimationAsset.animationAdd
          : AnimationAssetDark.animationAdd,
    );
  }
}
