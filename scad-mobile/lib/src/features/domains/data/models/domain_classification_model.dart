import 'package:scad_mobile/src/common/types.dart';

class DomainClassificationModel {
  DomainClassificationModel(
    this.id,
    this.key,
    this.name,
    this.lightIcon,
    this.darkIcon,
    this.count,
  );

  DomainClassificationModel.fromJson(JSONObject json)
      : id = json['id'] as String?,
        key = json['key'] as String?,
        name = json['name'] as String?,
        lightIcon = json['light_icon'] as String?,
        darkIcon = json['dark_icon'] as String?,
        count = json['count'];

  String? id;
  String? key;
  String? name;
  String? lightIcon;
  String? darkIcon;
  dynamic count;

  JSONObject toJson() => {
        'id': id,
        'key': key,
        'name': name,
        'light_icon': lightIcon,
        'dark_icon': darkIcon,
        'count': count,
      };
}
