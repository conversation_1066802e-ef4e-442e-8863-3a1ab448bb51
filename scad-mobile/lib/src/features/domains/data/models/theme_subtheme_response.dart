class ThemeSubThemeResponse {
  String? id;
  String? name;
  bool? showScreener;
  ScreenerConfiguration? screenerConfiguration;
  List<SubTheme>? subthemes;

  ThemeSubThemeResponse({
    this.id,
    this.name,
    this.showScreener,
    this.screenerConfiguration,
    this.subthemes,
  });

  ThemeSubThemeResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    name = json['name'] as String?;
    showScreener = json['showScreener'] as bool?;
    screenerConfiguration =
        (json['screenerConfiguration'] as Map<String, dynamic>?) != null
            ? ScreenerConfiguration.fromJson(
                json['screenerConfiguration'] as Map<String, dynamic>,)
            : null;
    subthemes = (json['subthemes'] as List?)
        ?.map((dynamic e) => SubTheme.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['name'] = name;
    json['showScreener'] = showScreener;
    json['screenerConfiguration'] = screenerConfiguration?.toJson();
    json['subthemes'] = subthemes?.map((e) => e.toJson()).toList();
    return json;
  }
}

class ScreenerConfiguration {
  String? screenerView;
  String? screenerIndicator;

  ScreenerConfiguration({
    this.screenerView,
    this.screenerIndicator,
  });

  ScreenerConfiguration.fromJson(Map<String, dynamic> json) {
    screenerView = json['screenerView'] as String?;
    screenerIndicator = json['screenerIndicator'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['screenerView'] = screenerView;
    json['screenerIndicator'] = screenerIndicator;
    return json;
  }
}

class SubTheme {

  SubTheme({
    this.id,
    this.name,
    this.showScreener,
    this.screenerConfiguration,
  });

  SubTheme.fromJson(Map<String, dynamic> json)
      : id = json['id'] as String?,
        name = json['name'] as String?,
        showScreener = json['showScreener'] as bool?,
        screenerConfiguration = (json['screenerConfiguration'] as Map<String,dynamic>?) != null ? ScreenerConfiguration.fromJson(json['screenerConfiguration'] as Map<String,dynamic>) : null;
  final String? id;
  final String? name;
  final bool? showScreener;
  final ScreenerConfiguration? screenerConfiguration;

  Map<String, dynamic> toJson() => {
    'id' : id,
    'name' : name,
    'showScreener' : showScreener,
    'screenerConfiguration' : screenerConfiguration?.toJson(),
  };
}

