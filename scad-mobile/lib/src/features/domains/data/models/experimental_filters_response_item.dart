class ExperimentalFiltersResponseItem {
  String? name;
  String? key;

  Items? defaultVal;

  List<Items>? items;

  ExperimentalFiltersResponseItem({
    this.name,
    this.key,
    this.defaultVal,
    this.items,
  });

  ExperimentalFiltersResponseItem.fromJson(Map<String, dynamic> json) {
    name = json['name'] as String?;
    key = json['key'] as String?;
    defaultVal = (json['default'] as Map<String, dynamic>?) != null
        ? Items.fromJson(json['default'] as Map<String, dynamic>)
        : null;
    items = (json['items'] as List?)
        ?.map((dynamic e) => Items.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['name'] = name;
    json['key'] = key;
    json['default'] = defaultVal?.toJson();
    json['items'] = items?.map((e) => e.toJson()).toList();
    return json;
  }
}

class Items {
  String? name;
  String? value;

  Items({
    this.name,
    this.value,
  });

  Items.fromJson(Map<String, dynamic> json) {
    name = json['name'] as String?;
    value = json['value'] as String?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['name'] = name;
    json['value'] = value;
    return json;
  }
}
