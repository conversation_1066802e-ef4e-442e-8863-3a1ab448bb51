typedef GovernmentAffairItemList = List<GovernmentAffairResponseItem>;

class GovernmentAffairResponseItem {
  const GovernmentAffairResponseItem({
    required this.domain,
    required this.title,
    required this.theme,
    required this.subTheme,
    required this.dashboardUrlLight,
    required this.dashboardUrlDark,
  });

  GovernmentAffairResponseItem.fromJson(Map<String, dynamic> json)
      : domain = json['domain'] as String,
        title = json['title'] as String,
        theme = json['theme'] as String,
        subTheme = json['subtheme'] as String,
        dashboardUrlLight = json['dashboard_light'] as String,
        dashboardUrlDark = json['dashboard_dark'] as String;

  final String? domain;

  final String? title;

  final String? theme;

  final String? subTheme;

  final String? dashboardUrlDark;

  final String? dashboardUrlLight;
}
