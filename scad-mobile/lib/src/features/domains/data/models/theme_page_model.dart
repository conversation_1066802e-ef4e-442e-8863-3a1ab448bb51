import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';

class ThemePageDomainModel {
  ThemePageDomainModel(
      {required this.domain, required this.classificationList,});

  final DomainModel domain;
  final List<Classification> classificationList;
}

class Classification {
  Classification({required this.classification, required this.list});

  final DomainClassificationModel classification;
  final List<dynamic> list;
}
