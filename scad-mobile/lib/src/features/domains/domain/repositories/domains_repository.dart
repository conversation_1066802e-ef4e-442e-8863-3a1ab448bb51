part of 'domains_repository_imports.dart';

abstract class DomainsRepository extends CacheableRepository {
  Future<RepoResponse<List<DomainModel>>> getDomainIconsList();

  Future<RepoResponse<List<DomainClassificationModel>>> getDomainClassificationList(RequestParamsMap requestParams);

  Future<RepoResponse<List<ThemeSubThemeResponse>>> getThemeList(RequestParamsMap requestParams);

  Future<RepoResponse<ThemeIndicatorListResponse>> getThemeIndicatorList(RequestParamsMap requestParams);

  Future<RepoResponse<ExperimentalIndicatorListResponse>> getThemeExperimentalIndicatorList({
    required String classification,
    required int pageNo,
    required Map<String, dynamic> filters,
  });

  Future<RepoResponse<List<ExperimentalFiltersResponseItem>>> getExperimentalFilters({
    required String classification,
    required String screenerIndicator,
  });

  Future<RepoResponse<DomainThemeSubThemeModelResponse>> getDomainListIfp();

  Future<RepoResponse<GovernmentAffairItemList>> getGovernmentAffairs();
}
