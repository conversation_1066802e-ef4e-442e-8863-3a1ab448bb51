part of 'domains_bloc.dart';

abstract class DomainsEvent extends Equatable {
  const DomainsEvent();

  @override
  List<Object> get props => [];
}

class DomainsInitEvent extends DomainsEvent {
  const DomainsInitEvent();

  @override
  List<Object> get props => [];
}

class ThemesInitEvent extends DomainsEvent {
  const ThemesInitEvent({required this.domainId});

  final String domainId;

  @override
  List<Object> get props => [domainId];
}

class GetThemesInitEvent extends DomainsEvent {
  const GetThemesInitEvent({
    required this.domainId,
    required this.classificationId,
  });

  final String domainId;
  final String classificationId;

  @override
  List<Object> get props => [classificationId, domainId];
}


class GetThemesEvent extends DomainsEvent {
  const GetThemesEvent({
    required this.domainId,
    required this.officialClassificationId,
    required this.experimentalClassificationId,
  });

  final String domainId;
  final String officialClassificationId;
  final String experimentalClassificationId;

  @override
  List<Object> get props =>
      [officialClassificationId, experimentalClassificationId, domainId];
}



class GetThemeIndicatorsEvent extends DomainsEvent {
  const GetThemeIndicatorsEvent({
    required this.subDomainId,
    required this.subThemeId,
    required this.pageNo,
    required this.domainId,
    required this.classificationId,
  });

  final String domainId;
  final String classificationId;
  final String subDomainId;
  final String subThemeId;
  final int pageNo;

  @override
  List<Object> get props =>
      [classificationId, domainId, subDomainId, subThemeId, pageNo];
}

class CompareIndicatorGetThemesEvent extends DomainsEvent {
  const CompareIndicatorGetThemesEvent({
    required this.subDomainId,
    required this.subThemeId,
    required this.pageNo,
    required this.domainId,
    required this.classificationId,
  });

  final String domainId;
  final String classificationId;
  final String subDomainId;
  final String subThemeId;
  final int pageNo;

  @override
  List<Object> get props =>
      [classificationId, domainId, subDomainId, subThemeId, pageNo];
}

class GetThemeExperimentalFiltersEvent extends DomainsEvent {
  const GetThemeExperimentalFiltersEvent({required this.classification, required this.screenerIndicator});

  final String screenerIndicator;
  final String classification;

  @override
  List<Object> get props => [classification, screenerIndicator];
}

class GetThemeExperimentalIndicatorListEvent extends DomainsEvent {
  const GetThemeExperimentalIndicatorListEvent(
      {required this.classification, required this.pageNo, required this.filters});

  final int pageNo;
  final Map<String, dynamic> filters;
  final String classification;

  @override
  List<Object> get props => [classification, pageNo, filters];
}

class GetClassificationsEvent extends DomainsEvent {
  const GetClassificationsEvent({required this.domainId});

  final String domainId;

  @override
  List<Object> get props => [domainId];
}

class GetGovernmentAffairsEvent extends DomainsEvent {}