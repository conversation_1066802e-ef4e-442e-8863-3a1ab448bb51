import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/chat_with_sme/data/models/domain_theme_sub_theme_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_filters_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/government_affair_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/domain/repositories/domains_repository_imports.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'domains_event.dart';
part 'domains_state.dart';

class DomainsBloc extends Bloc<DomainsEvent, DomainsState> {
  DomainsBloc() : super(DomainLoadingState()) {
    on<GetThemesEvent>(_onGetThemesEvent);
    on<DomainsInitEvent>(_onDomainsInitEvent);
    on<ThemesInitEvent>(_onThemesInitEvent);
    on<GetThemesInitEvent>(_onGetThemesInitEvent);
    on<GetThemeIndicatorsEvent>(_onGetThemeIndicatorsEvent);

    on<CompareIndicatorGetThemesEvent>(_onCompareIndicatorGetThemesEvent);
    on<GetThemeExperimentalFiltersEvent>(_onGetThemeExperimentalFiltersEvent);
    on<GetThemeExperimentalIndicatorListEvent>(_onGetThemeExperimentalIndicatorsListEvent);
    on<GetClassificationsEvent>(_onGetClassificationEvent);

    on<GetGovernmentAffairsEvent>(_onGetGovernmentAffairsEvent);
  }

  FutureOr<void> _onGetThemesEvent(GetThemesEvent event, Emitter<DomainsState> emit) async {
    try {
      emit(const ThemeLoadingState());

      final List<RepoResponse<dynamic>> responses = await Future.wait([
        servicelocator<DomainsRepository>().getThemeList({
          'domain_id': event.domainId,
          'classification_id': event.officialClassificationId,
        }),
        servicelocator<DomainsRepository>().getThemeList({
          'domain_id': event.domainId,
          'classification_id': event.experimentalClassificationId,
        }),
      ]);
      if (responses.every((element) => element.isSuccess)) {
        final RepoResponse<List<ThemeSubThemeResponse>> response1 =
            responses[0] as RepoResponse<List<ThemeSubThemeResponse>>;
        final RepoResponse<List<ThemeSubThemeResponse>> response2 =
            responses[1] as RepoResponse<List<ThemeSubThemeResponse>>;

        emit(
          GetThemesSuccessState(
            domainId: event.domainId,
            officialThemes: response1.response!,
            experimentalThemes: response2.response!,
          ),
        );
      } else {
        emit(
          ThemeErrorState(
            error: responses.firstWhere((element) => !element.isSuccess).errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  FutureOr<void> _onDomainsInitEvent(DomainsInitEvent event, Emitter<DomainsState> emit) async {
    try {
      emit(DomainLoadingState());
      final List<RepoResponse<dynamic>> responses = await Future.wait([
        servicelocator<DomainsRepository>().getDomainIconsList(),
        servicelocator<DomainsRepository>().getDomainListIfp(),
      ]);
      if (responses.every((element) => element.isSuccess)) {
        final RepoResponse<List<DomainModel>> response1 = responses[0] as RepoResponse<List<DomainModel>>;
        final RepoResponse<DomainThemeSubThemeModelResponse> response2 =
            responses[1] as RepoResponse<DomainThemeSubThemeModelResponse>;
        final List<DomainModel> domainList = response1.response!;
        final List<DomainModel> domainListCreated = [];
        final DomainThemeSubThemeModelResponse domainListIfp = response2.response!;
        final Map<String, String> domainListGrouped = {};
        for (final DomainThemeSubThemeModel domainValue in domainListIfp.dataList ?? []) {
          for (final Domain domainList in domainValue.domains ?? []) {
            domainListGrouped[domainList.id ?? ''] = domainList.name ?? '';
          }
        }

        for (final domainValue in domainListGrouped.entries) {
          for (final DomainModel domain in domainList) {
            if (domain.domainId == domainValue.key) {
              domain.hasAccess = true;
              domainListCreated.add(domain);
            }
          }
        }

        emit(DomainShowResponseState(list: domainListCreated));
      } else {
        emit(
          DomainErrorState(error: responses.firstWhere((element) => !element.isSuccess).errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(DomainErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  FutureOr<void> _onThemesInitEvent(ThemesInitEvent event, Emitter<DomainsState> emit) async {
    try {
      emit(const ThemeLoadingState());
      final RepoResponse<List<DomainClassificationModel>> response =
          await servicelocator<DomainsRepository>().getDomainClassificationList({
        'domain_id': event.domainId,
      });
      if (response.isSuccess) {
        final List<RepoResponse<dynamic>> responses = await Future.wait([
          servicelocator<DomainsRepository>().getDomainIconsList(),
          servicelocator<DomainsRepository>().getDomainListIfp(),
        ]);
        if (responses.every((element) => element.isSuccess)) {
          final RepoResponse<List<DomainModel>> response1 = responses[0] as RepoResponse<List<DomainModel>>;
          final RepoResponse<DomainThemeSubThemeModelResponse> response2 =
              responses[1] as RepoResponse<DomainThemeSubThemeModelResponse>;
          final List<DomainModel> domainList = response1.response!;
          final List<DomainModel> domainListCreated = [];
          final DomainThemeSubThemeModelResponse domainListIfp = response2.response!;
          final Map<String, String> domainListGrouped = {};

          for (final DomainThemeSubThemeModel domainValue in domainListIfp.dataList ?? []) {
            for (final Domain domainList in domainValue.domains ?? []) {
              domainListGrouped[domainList.id ?? ''] = domainList.name ?? '';
            }
          }
          for (final domainValue in domainListGrouped.entries) {
            for (final DomainModel domain in domainList) {
              if (domain.domainId == domainValue.key) {
                domainListCreated.add(domain);
              }
            }
          }
          emit(
            ThemeClassificationShowResponseState(
              domainId: event.domainId,
              list: response.response!,
              domains: domainListCreated,
            ),
          );
        } else {
          emit(
            ThemeErrorState(error: responses.firstWhere((element) => !element.isSuccess).errorMessage),
          );
        }
      } else {
        emit(
          ThemeErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  FutureOr<void> _onGetThemesInitEvent(GetThemesInitEvent event, Emitter<DomainsState> emit) async {
    try {
      emit(const ThemeLoadingState());
      final RepoResponse<List<ThemeSubThemeResponse>> response =
          await servicelocator<DomainsRepository>().getThemeList({
        'domain_id': event.domainId,
        'classification_id': event.classificationId,
      });
      if (response.isSuccess) {
        emit(
          ThemeListShowResponseState(
            domainId: event.domainId,
            list: response.response!,
          ),
        );
      } else {
        emit(
          ThemeErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  FutureOr<void> _onGetThemeIndicatorsEvent(GetThemeIndicatorsEvent event, Emitter<DomainsState> emit) async {
    try {
      emit(ThemeIndicatorsLoadingState());
      final RepoResponse<ThemeIndicatorListResponse> response =
          await servicelocator<DomainsRepository>().getThemeIndicatorList({
        'domain_id': event.domainId,
        'classification_id': event.classificationId,
        'sub_theme_id': event.subThemeId,
        'sub_domain_id': event.subDomainId,
        'page': event.pageNo.toString(),
      });
      if (response.isSuccess) {
        emit(
          ThemeIndicatorsListState(
            themeIndicatorListResponse: response.response!,
          ),
        );
      } else {
        emit(
          ThemeErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  FutureOr<void> _onCompareIndicatorGetThemesEvent(
    CompareIndicatorGetThemesEvent event,
    Emitter<DomainsState> emit,
  ) async {
    try {
      emit(ThemeIndicatorsLoadingState());
      final RepoResponse<ThemeIndicatorListResponse> response =
          await servicelocator<DomainsRepository>().getThemeIndicatorList({
        'domain_id': event.domainId,
        'classification_id': event.classificationId,
        'sub_theme_id': event.subThemeId,
        'sub_domain_id': event.subDomainId,
        'page': event.pageNo.toString(),
      });
      if (response.isSuccess) {
        emit(
          CompareThemeIndicatorListState(
            themeIndicatorListResponse: response.response!,
            subThemeId: event.subThemeId,
          ),
        );
      } else {
        emit(
          ThemeErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  FutureOr<void> _onGetThemeExperimentalFiltersEvent(
    GetThemeExperimentalFiltersEvent event,
    Emitter<DomainsState> emit,
  ) async {
    try {
      emit(ThemeIndicatorsLoadingState());
      final RepoResponse<List<ExperimentalFiltersResponseItem>> response =
          await servicelocator<DomainsRepository>().getExperimentalFilters(
        classification: event.classification,
        screenerIndicator: event.screenerIndicator,
      );
      if (response.isSuccess) {
        emit(
          ExperimentalFilterListState(
            experimentalFilters: response.response!,
          ),
        );
      } else {
        emit(
          ThemeErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  FutureOr<void> _onGetThemeExperimentalIndicatorsListEvent(
    GetThemeExperimentalIndicatorListEvent event,
    Emitter<DomainsState> emit,
  ) async {
    try {
      emit(ThemeIndicatorsLoadingState());
      final RepoResponse<ExperimentalIndicatorListResponse> response =
          await servicelocator<DomainsRepository>().getThemeExperimentalIndicatorList(
        classification: event.classification,
        pageNo: event.pageNo,
        filters: event.filters,
      );
      if (response.isSuccess) {
        emit(
          ThemeExperimentalIndicatorListResponseState(
            response: response.response!,
          ),
        );
      } else {
        emit(
          ThemeErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  FutureOr<void> _onGetClassificationEvent(GetClassificationsEvent event, Emitter<DomainsState> emit) async {
    try {
      emit(const ThemeLoadingState());
      final RepoResponse<List<DomainClassificationModel>> response =
          await servicelocator<DomainsRepository>().getDomainClassificationList({
        'domain_id': event.domainId,
      });
      if (response.isSuccess) {
        emit(
          GetThemeClassificationState(
            domainId: event.domainId,
            list: response.response!,
          ),
        );
      } else {
        emit(
          ThemeErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(ThemeErrorState(error: LocaleKeys.somethingWentWrong.tr()));
    }
  }

  Future<void> _onGetGovernmentAffairsEvent(
    GetGovernmentAffairsEvent event,
    Emitter<DomainsState> emit,
  ) async {
    try {
      emit(GetGovernmentAffairsLoadingState());
      final response = await servicelocator<DomainsRepository>().getGovernmentAffairs();
      if (response.isSuccess && response.response != null) {
        emit(
          GetGovernmentAffairsSuccessState(response.response!),
        );
      } else {
        emit(
          GetGovernmentAffairsErrorState(error: response.errorMessage),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        GetGovernmentAffairsErrorState(
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    }
  }
}
