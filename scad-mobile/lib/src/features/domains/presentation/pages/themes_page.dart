import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:scad_mobile/src/common/widgets/app_sliding_tab.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/count_widget.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget_list_item.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/src/showcase_widget.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/widget_extension.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/government_affair_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/domains/presentation/pages/theme_indicators_screen.dart';
import 'package:scad_mobile/src/features/domains/presentation/widgets/government_affairs_list.dart';
import 'package:scad_mobile/src/features/home/<USER>/widgets/persistent_bottom_nav_bar/persistent_bottom_nav_bar_v2.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

@RoutePage()
class ThemesPage extends StatefulWidget {
  const ThemesPage({
    this.domainId,
    super.key,
    this.collection,
  });

  final String? domainId;
  final CollectionResult? collection;

  @override
  State<ThemesPage> createState() => _ThemesPageState();
}

class _ThemesPageState extends State<ThemesPage> with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final bool isLightMode = HiveUtilsSettings.isLightMode;

  List<DomainModel> domainList = [];
  List<DomainClassificationModel> classificationList = [];
  List<ThemeSubThemeResponse> officialThemeSubThemeList = [];
  List<ThemeSubThemeResponse> experimentalThemeSubThemeList = [];

  int selectedDomainTabIndex = 0;

  late TabController classificationTabController;
  ScrollController scrollController = ScrollController();
  num indicatorsRefreshedAt = 0;

  List<GlobalKey> steps = [];
  bool isThemeSubThemeLoading = true;
  final ValueNotifier<int> _classificationTabIndex = ValueNotifier(0);

  DomainModel? get _selectedDomain => domainList.isEmpty ? null : domainList[selectedDomainTabIndex];

  DomainModel get _govAffairsDomainModel => DomainModel(
        domainId: 'gov_affairs',
        domainName: 'Government Affairs',
        domainNameAr: 'الشؤون الحكومية',
        assetPath: 'assets/images/ic_gov_affairs.svg',
      );

  Map<String, List<GovernmentAffairResponseItem>> trustedZoneMap = {};

  Map<String, List<GovernmentAffairResponseItem>> entitiesZoneMap = {};

  @override
  void initState() {
    super.initState();

    if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Domains) {
      steps = [
        GlobalKey(debugLabel: 'official-tab'),
        GlobalKey(debugLabel: 'experimental-tab'),
      ];
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        ShowCaseWidget.of(context).startShowCase(steps);
      });
    }

    classificationTabController = TabController(length: 2, vsync: this);
    _initFetchData();
  }

  void _initFetchData() {
    final domainBloc = context.read<DomainsBloc>();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final domainId = widget.domainId;
      if (domainId != null) {
        domainBloc.add(ThemesInitEvent(domainId: domainId));
      } else {
        domainBloc
          ..add(const DomainsInitEvent())
          ..add(GetGovernmentAffairsEvent());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return widget.domainId != null ? AppDrawer(child: buildThemePage()) : buildThemePage();
  }

  void _initializeGovAffairsList(GovernmentAffairItemList items) {
    for (final item in items) {
      if (item.domain != LocaleKeys.governmentAffairs.tr()) {
        continue;
      }

      if (item.theme == LocaleKeys.trustedZone.tr()) {
        final key = item.subTheme;
        if (trustedZoneMap.containsKey(key)) {
          trustedZoneMap[key]!.add(item);
        } else {
          trustedZoneMap[key ?? ''] = [item];
        }
      }

      if (item.theme == LocaleKeys.entitiesZone.tr()) {
        final key = item.subTheme;
        if (entitiesZoneMap.containsKey(key)) {
          entitiesZoneMap[key]!.add(item);
        } else {
          entitiesZoneMap[key ?? ''] = [item];
        }
      }
    }
  }

  Scaffold buildThemePage() {
    return Scaffold(
      body: Column(
        children: [
          FlatAppBar(
            mainScreen: widget.domainId == null,
            title: LocaleKeys.statisticalDomains.tr(),
            scrollController: scrollController,
          ),
          Expanded(
            child: BlocConsumer<DomainsBloc, DomainsState>(
              listener: _domainBlocListener,
              builder: (context, state) {
                if (state is GetGovernmentAffairsLoadingState || state is DomainLoadingState) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else if (state is DomainErrorState) {
                  return ErrorReloadPlaceholder(
                    error: state.error,
                    onReload: _initFetchData,
                  );
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (domainList.isNotEmpty) _buildSlidingDomainTabs(),
                    const SizedBox(height: 20),
                    Expanded(
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 100),
                        transitionBuilder: (child, anim) => FadeTransition(
                          opacity: anim,
                          child: child,
                        ),
                        child: _buildDomainsBody(state),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _domainBlocListener(BuildContext context, DomainsState state) {
    switch (state) {
      case GetGovernmentAffairsSuccessState _:
        _initializeGovAffairsList(state.items);

        if (trustedZoneMap.isEmpty && entitiesZoneMap.isEmpty) {
          return;
        }

        indicatorsRefreshedAt = DateTime.now().microsecondsSinceEpoch;
        if (!domainList.any(
          (e) => e.domainId == _govAffairsDomainModel.domainId,
        )) {
          domainList = [_govAffairsDomainModel, ...domainList];
        }

      case ThemeClassificationShowResponseState _:
        classificationList = state.list;
        domainList.addAll(
          [...domainList, ...state.domains]..removeWhere(
              (domain) => domainList.any(
                (element1) => domain.domainId == element1.domainId,
              ),
            ),
        );

        selectedDomainTabIndex = domainList.indexWhere(
          (element) => element.domainId == state.domainId,
        );

        if (selectedDomainTabIndex == -1) selectedDomainTabIndex = 0;
        context.read<DomainsBloc>().add(
              GetThemesEvent(
                domainId: state.domainId,
                officialClassificationId: '${classificationList[0].id}',
                experimentalClassificationId: '${classificationList[1].id}',
              ),
            );

        indicatorsRefreshedAt = DateTime.now().microsecondsSinceEpoch;

      case DomainShowResponseState _:
        context.read<DomainsBloc>().add(
              ThemesInitEvent(
                domainId: state.list.firstOrNull?.domainId ?? '',
              ),
            );

      case GetThemesSuccessState _:
        isThemeSubThemeLoading = false;
        officialThemeSubThemeList = state.officialThemes;
        experimentalThemeSubThemeList = state.experimentalThemes;
      // domainLoader.value = false;
      case ThemeErrorState _:
        isThemeSubThemeLoading = false;
    }
  }

  Widget _buildSlidingDomainTabs() {
    final rtl = DeviceType.isDirectionRTL(context);
    return ValueListenableBuilder(
      valueListenable: HiveUtilsSettings.box.listenable(),
      builder: (c, _, __) {
        return AppSlidingTab(
          key: Key(
            'theme.domain.$indicatorsRefreshedAt.${HiveUtilsSettings.getUserGuideStatus() == UserGuides.Domains}',
          ),
          initialTabIndex: selectedDomainTabIndex,
          onTabChange: (int index) {
            selectedDomainTabIndex = index;
            if (_selectedDomain?.domainId == _govAffairsDomainModel.domainId) {
              setState(() {});
              return;
            }

            context.read<DomainsBloc>().add(
                  GetThemesEvent(
                    domainId: '${_selectedDomain?.domainId}',
                    officialClassificationId: '${classificationList[0].id}',
                    experimentalClassificationId: '${classificationList[1].id}',
                  ),
                );
          },
          tabs: domainList
              .where((e) {
                if (HiveUtilsSettings.getUserGuideStatus() == UserGuides.Domains) {
                  return e.domainId != _govAffairsDomainModel.domainId;
                } else {
                  return true;
                }
              })
              .map(
                (e) => AppSlidingTabItem(
                  label: rtl ? e.domainNameAr ?? '-' : e.domainName ?? '-',
                  iconUrl: e.domainIcon,
                  assetPath: e.assetPath,
                ),
              )
              .toList(),
        );
      },
    );
  }

  Widget _buildDomainsBody(DomainsState state) {
    if (HiveUtilsSettings.getUserGuideStatus() != UserGuides.Domains &&
        _selectedDomain?.domainId == _govAffairsDomainModel.domainId) {
      return GovernmentAffairList(
        entitiesZoneMap: entitiesZoneMap,
        trustedZoneMap: trustedZoneMap,
      );
    }

    return Column(
      children: [
        if (classificationList.isNotEmpty)
          TabBar(
            onTap: (index) => _classificationTabIndex.value = index,
            controller: classificationTabController,
            labelPadding: const EdgeInsets.symmetric(horizontal: 1),
            indicatorSize: TabBarIndicatorSize.tab,
            labelColor: AppColors.blueLightOld,
            indicator: const UnderlineTabIndicator(
              borderSide: BorderSide(
                width: 3,
                color: AppColors.blueLightOld,
              ),
              insets: EdgeInsets.symmetric(horizontal: 4),
            ),
            tabs: _buildDomainTab(),
          ),
        Expanded(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 100),
            child: state is ThemeLoadingState
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 150),
                      child: CircularProgressIndicator(),
                    ),
                  )
                : ValueListenableBuilder(
                    valueListenable: _classificationTabIndex,
                    builder: (context, value, child) {
                      return _buildThemeList(
                        value == 0 ? officialThemeSubThemeList : experimentalThemeSubThemeList,
                        isLightMode,
                      );
                    },
                  ),
            transitionBuilder: (child, anim) => FadeTransition(
              opacity: anim,
              child: child,
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildDomainTab() {
    final width = MediaQuery.sizeOf(context).width;

    return [
      Tab(
        child: Padding(
          padding: const EdgeInsets.only(left: 4, right: 6),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  classificationList
                          .where(
                            (element) => element.key == 'official_statistics',
                          )
                          .firstOrNull
                          ?.name ??
                      '-',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: officialThemeSubThemeList.isNotEmpty
                      ? null
                      : TextStyle(
                          color: isLightMode ? AppColors.greyShade1 : AppColors.grey,
                        ),
                ),
              ),
              const SizedBox(width: 4),
              ListenableBuilder(
                listenable: classificationTabController,
                builder: (context, child) {
                  return SvgPicture.asset(
                    officialThemeSubThemeList.isEmpty
                        ? AppImages.icOfficialDisabled
                        : classificationTabController.index == 0
                            ? AppImages.icOfficialActive
                            : AppImages.icOfficialInactive,
                  );
                },
              ),
            ],
          ),
        ),
      ).introWidget(
        steps: steps,
        index: 0,
        title: LocaleKeys.official.tr(),
        description: LocaleKeys.officialDescription.tr(),
        arrowAlignment: Alignment.topLeft,
        crossAxisAlignment: CrossAxisAlignment.start,
        targetBorderRadius: 60,
        arrowPadding: width * .2,
      ),
      Tab(
        child: Padding(
          padding: const EdgeInsets.only(left: 6, right: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  classificationList
                          .where(
                            (element) => element.key == 'experimental_statistics',
                          )
                          .firstOrNull
                          ?.name ??
                      '-',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: experimentalThemeSubThemeList.isNotEmpty
                      ? null
                      : TextStyle(
                          color: isLightMode ? AppColors.greyShade1 : AppColors.grey,
                        ),
                ),
              ),
              const SizedBox(width: 2),
              ListenableBuilder(
                listenable: classificationTabController,
                builder: (context, child) {
                  final isSelected = classificationTabController.index == 1;

                  return SvgPicture.asset(
                    experimentalThemeSubThemeList.isEmpty
                        ? AppImages.icExperimentalDisabled
                        : isSelected
                            ? AppImages.icExperimentalActive
                            : AppImages.icExperimentalInactive,
                    colorFilter: experimentalThemeSubThemeList.isEmpty
                        ? ColorFilter.mode(
                            isLightMode ? AppColors.greyShade3 : AppColors.grey,
                            BlendMode.srcIn,
                          )
                        : ColorFilter.mode(
                            isSelected
                                ? AppColors.blueLightOld
                                : isLightMode
                                    ? AppColors.grey
                                    : AppColors.greyShade4,
                            BlendMode.srcIn,
                          ),
                  );
                },
              ),
            ],
          ),
        ),
      ).introWidget(
        steps: steps,
        index: 1,
        title: LocaleKeys.experimental.tr(),
        description: LocaleKeys.experimentalDescription.tr(),
        arrowAlignment: Alignment.topRight,
        crossAxisAlignment: CrossAxisAlignment.end,
        targetBorderRadius: 60,
        arrowPadding: width * .2,
      ),
    ];
  }

  Widget _buildThemeList(List<ThemeSubThemeResponse> list, bool isLightMode) {
    if (list.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(bottom: 150),
          child: isThemeSubThemeLoading ? const CircularProgressIndicator() : const NoDataPlaceholder(),
        ),
      );
    }

    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
      itemCount: list.length,
      itemBuilder: (context, index) {
        final ThemeSubThemeResponse item = list[index];

        return Padding(
          padding: const EdgeInsets.only(bottom: 14),
          child: ExpandableWidget(
            title: item.name ?? '-',
            trailingIcon: CountWidget(count: item.subthemes!.length),
            expandedChild: ListView.builder(
              itemCount: item.subthemes!.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(
                vertical: 14,
                horizontal: 14,
              ),
              itemBuilder: (context, i) {
                final SubTheme subThemeItem = item.subthemes![i];

                return Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: ExpandableWidgetListItem(
                    title: subThemeItem.name ?? '-',
                    trailingIcon: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Icon(
                        Icons.chevron_right_rounded,
                        size: 18,
                        color: isLightMode ? AppColors.blueShade22 : AppColors.white,
                      ),
                    ),
                    onTap: () {
                      final classification = classificationList[classificationTabController.index];

                      pushScreen(
                        context,
                        withNavBar: true,
                        screen: ThemeIndicatorsScreen(
                          isFromMainScreen: widget.domainId != null,
                          title: subThemeItem.name!,
                          domain: _selectedDomain!,
                          classification: classification,
                          subDomain: item,
                          subTheme: subThemeItem,
                          screenerConfiguration: classification.key == 'experimental_statistics'
                              ? item.screenerConfiguration
                              : subThemeItem.screenerConfiguration,
                          collection: widget.collection,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    super.dispose();
    scrollController.dispose();
    classificationTabController.dispose();
  }
}
