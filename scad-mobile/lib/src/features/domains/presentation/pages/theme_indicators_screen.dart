import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/appbar/flat_app_bar.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/error_reload_placeholder.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/presentation/pages/indicator_card_v2.dart';
import 'package:scad_mobile/src/common/widgets/lazy_indicator_list_view/presentation/pages/lazy_indicator_list_view.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/details_page/base/helper/route_helper.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_classification_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/domain_model/domain_model.dart';
import 'package:scad_mobile/src/features/domains/data/models/experimental_filters_response_item.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_indicator_list_response.dart';
import 'package:scad_mobile/src/features/domains/data/models/theme_subtheme_response.dart';
import 'package:scad_mobile/src/features/domains/presentation/bloc/domains_bloc.dart';
import 'package:scad_mobile/src/features/domains/presentation/widgets/screener_filter/screener_filter_button.dart';
import 'package:scad_mobile/src/features/my_apps/data/models/response/collection_list_response.dart';
import 'package:scad_mobile/src/features/my_apps/presentation/bloc/myapps_bloc.dart';

@RoutePage()
class ThemeIndicatorsScreen extends StatefulWidget {
  const ThemeIndicatorsScreen({
    required this.title,
    required this.domain,
    required this.classification,
    required this.subTheme,
    required this.subDomain,
    super.key,
    this.screenerConfiguration,
    this.collection,
    this.isFromMainScreen = false,
  });

  final String title;
  final DomainModel domain;
  final DomainClassificationModel classification;
  final SubTheme subTheme;
  final ThemeSubThemeResponse subDomain;
  final ScreenerConfiguration? screenerConfiguration;
  final CollectionResult? collection;
  final bool isFromMainScreen;

  @override
  State<ThemeIndicatorsScreen> createState() => _ThemeIndicatorsScreenState();
}

class _ThemeIndicatorsScreenState extends State<ThemeIndicatorsScreen> {
  ScrollController _scrollController = ScrollController();
  int pageNo = 1;
  int? count;
  bool apiLoading = false;

  List<ThemeIndicatorListItem> _indicatorList = [];

  List<ExperimentalFiltersResponseItem> experimentalFilters = [];
  ValueNotifier<List<Items?>> selectedValueList = ValueNotifier([]);
  Map<String, List<Items>> experimentalFilterMap = {};

  bool get isExperimentalStatsClassification => widget.classification.key == 'experimental_statistics';

  void _pageReset() {
    _indicatorList = [];
    count = null;
    pageNo = 1;
    apiLoading = false;
  }

  static const Map<String, String> classificationMap = {
    'official_statistics': 'official-insights',
    'experimental_statistics': 'innovative-insights',
  };

  @override
  void initState() {
    super.initState();

    _pageReset();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (isExperimentalStatsClassification || widget.subTheme.showScreener == true) {
        context.read<DomainsBloc>().add(
              GetThemeExperimentalFiltersEvent(
                classification: classificationMap['${widget.classification.key}'] ?? '',
                screenerIndicator: isExperimentalStatsClassification
                    ? widget.screenerConfiguration?.screenerIndicator ?? ''
                    : widget.subTheme.name ?? '',
              ),
            );
      } else {
        _loadData();
      }
    });
  }

  void _loadOfficialIndicators([int? page]) {
    final pageNo = page ?? this.pageNo;

    context.read<DomainsBloc>().add(
          GetThemeIndicatorsEvent(
            subDomainId: widget.subDomain.id!,
            subThemeId: widget.subTheme.id!,
            pageNo: pageNo,
            domainId: widget.domain.domainId!,
            classificationId: widget.classification.id!,
          ),
        );
  }

  void _loadScreenerIndicators([int? page]) {
    final Map<String, List<String>> filter = {};
    for (final entry in experimentalFilterMap.entries) {
      filter[entry.key] = entry.value.map((e) => e.value!).toList();
    }

    final Map<String, dynamic> body = {
      'viewName': widget.screenerConfiguration?.screenerView,
      'filters': filter,
      'sortBy': {'alphabetical': 'desc'},
    };

    final pageNo = page ?? this.pageNo;
    context.read<DomainsBloc>().add(
          GetThemeExperimentalIndicatorListEvent(
            classification: classificationMap['${widget.classification.key}'] ?? '',
            pageNo: pageNo,
            filters: body,
          ),
        );
  }

  void _loadData([int? page]) {
    if (isExperimentalStatsClassification || widget.subTheme.showScreener == true) {
      _loadScreenerIndicators(page);
    } else {
      _loadOfficialIndicators(page);
    }
    apiLoading = true;
  }

  @override
  Widget build(BuildContext context) {
    return widget.isFromMainScreen
        ? AppDrawer(
            child: _buildIndicatorList(),
          )
        : _buildIndicatorList();
  }

  Scaffold _buildIndicatorList() {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FlatAppBar(
            title: widget.title,
            scrollController: _scrollController,
            mainScreen: !widget.isFromMainScreen,
          ),
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: BlocConsumer<DomainsBloc, DomainsState>(
                listener: _domainBlocListener,
                builder: (context, state) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (experimentalFilters.isNotEmpty)
                        ScreenerFilterButton(
                          screener: experimentalFilters,
                          initialSelectedScreenerMap: experimentalFilterMap,
                          onChanged: (selectedScreenerMap) {
                            experimentalFilterMap
                              ..clear()
                              ..addAll(selectedScreenerMap);

                            _pageReset();
                            _loadScreenerIndicators();
                          },
                        ),
                      _buildIndicatorListBody(state),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicatorListBody(DomainsState state) {
    if (_indicatorList.isEmpty && state is! ThemeErrorState) {
      return Padding(
        padding: EdgeInsets.only(top: MediaQuery.sizeOf(context).height * .25),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (state is ThemeErrorState) {
      return Padding(
        padding: EdgeInsets.only(top: MediaQuery.sizeOf(context).height * .25),
        child: Center(
          child: ErrorReloadPlaceholder(error: state.error),
        ),
      );
    }

    if (_indicatorList.isEmpty) {
      return Padding(
        padding: EdgeInsets.only(top: MediaQuery.sizeOf(context).height * .2),
        child: const Center(
          child: NoDataPlaceholder(),
        ),
      );
    }

    final idsHash = _indicatorList.map((e) => e.id).toSet().toList();
    return Column(
      children: [
        LazyIndicatorListView<ThemeIndicatorListItem>(
          key: Key('themeIndicatorListView-${idsHash.hashCode}'),
          parentScrollController: _scrollController,
          padding: EdgeInsets.fromLTRB(20, 15, 20, apiLoading ? 0 : 150),
          items: _indicatorList,
          shouldLoadOverview: widget.screenerConfiguration == null,
          getId: (item) => item.id!,
          getContentType: (item) => item.contentType!,
          itemBuilder: (context, index, item, overview) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 25),
              child: IndicatorCardV2(
                key: Key('theme.indicators.IndicatorCardV2-${item.id}'),
                id: item.id!,
                contentType: item.contentType!,
                collection: widget.collection,
                overView: overview,
                postMyAppButtonTap: (isRemoved) {
                  if (widget.collection != null) {
                    context.read<MyAppsBloc>().add(
                          ReloadIndicatorListEvent(
                            collectionUuid: widget.collection?.uuid ?? '',
                          ),
                        );
                  }
                },
                onDetailsButtonTapped: (indicatorDetails) {
                  JSONObject? screener;
                  if (isExperimentalStatsClassification) {
                    final filter = <String, List<String>>{};
                    for (final entry in experimentalFilterMap.entries) {
                      filter[entry.key] = entry.value.map((e) => e.value!).toList();
                    }

                    screener = {
                      'viewName': widget.screenerConfiguration?.screenerView,
                      'filters': filter,
                      'sortBy': {'alphabetical': 'desc'},
                    };
                  }

                  DetailsPageRouteHelper.navigateToOfficialExperimentalDetailsPage(
                    context,
                    id: indicatorDetails.id ?? '',
                    contentType: item.contentType ?? '',
                    title: indicatorDetails.componentTitle ?? '',
                    screenerPayload: screener,
                  );
                },
              ),
            );
          },
          onScrollOverflow: () {
            if (apiLoading || _indicatorList.length >= (count ?? 0)) return;
            _loadData(++pageNo);
          },
        ),
        if (apiLoading)
          Container(
            margin: const EdgeInsets.only(top: 20, bottom: 100),
            height: 30,
            width: 30,
            child: const CircularProgressIndicator(),
          ),
      ],
    );
  }

  void _domainBlocListener(BuildContext context, DomainsState state) {
    switch (state) {
      case ThemeErrorState _:
        apiLoading = false;

      case ThemeIndicatorsListState _:
        _indicatorList.addAll(
          (state.themeIndicatorListResponse.results ?? [])
            ..removeWhere(
              (element) => _indicatorList.indexWhere((element1) => element1.id == element.id) >= 0,
            ),
        );
        _scrollController = ScrollController();
        count = state.themeIndicatorListResponse.totalCount ?? 0;
        apiLoading = false;
        if (mounted) {
          setState(() {});
        }

      case ExperimentalFilterListState _:
        experimentalFilters = state.experimentalFilters;

        selectedValueList.value = List.generate(
          experimentalFilters.length,
          (index) => null,
        );

        for (int i = 0; i < experimentalFilters.length; i++) {
          final Items? defaultVal = experimentalFilters[i].defaultVal;
          selectedValueList.value[i] = defaultVal;
        }

        for (int i = 0; i < experimentalFilters.length; i++) {
          experimentalFilterMap['${experimentalFilters[i].key}'] = [
            experimentalFilters[i].defaultVal!,
          ];
        }
        _pageReset();
        _loadScreenerIndicators();

      case ThemeExperimentalIndicatorListResponseState _:
        count = state.response.totalCount ?? 0;

        _indicatorList.addAll(
          (state.response.data ?? [])
              .map(
                (e) => ThemeIndicatorListItem(
                  id: e.indicatorId,
                  title: e.title,
                  contentType: classificationMap['${widget.classification.key}'] ?? '',
                ),
              )
              .toList()
            ..removeWhere(
              (element) => _indicatorList.any((element1) => element.id == element1.id),
            ),
        );
        apiLoading = false;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose();
  }
}
