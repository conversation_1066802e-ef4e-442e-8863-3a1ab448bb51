part of 'screener_filter_button.dart';

class ScreenerFilterBottomSheet extends StatefulWidget {
  const ScreenerFilterBottomSheet._({
    required this.screener,
    required this.initialSelectedScreenerMap,
  });

  final List<ExperimentalFiltersResponseItem> screener;
  final SelectedScreenerMap initialSelectedScreenerMap;

  static Future<SelectedScreenerMap?> show(
    BuildContext context, {
    required List<ExperimentalFiltersResponseItem> screener,
    required SelectedScreenerMap initialSelectedScreenerMap,
  }) {
    return showModalBottomSheet<SelectedScreenerMap>(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      builder: (context) => ScreenerFilterBottomSheet._(
        screener: screener,
        initialSelectedScreenerMap: initialSelectedScreenerMap,
      ),
    );
  }

  @override
  State<ScreenerFilterBottomSheet> createState() => _ScreenerFilterBottomSheetState();
}

class _ScreenerFilterBottomSheetState extends State<ScreenerFilterBottomSheet> {
  late final SelectedScreenerMap _selectedScreenerMap = widget.initialSelectedScreenerMap;

  void _onClearTapped() {
    final defaultScreenerFilter = _getDefaultScreenMap(widget.screener);
    Navigator.pop(context, defaultScreenerFilter);
  }

  void _onApplyTapped() => Navigator.pop(context, _selectedScreenerMap);

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Container(
      decoration: BoxDecoration(
        color: isLightMode ? AppColors.white : AppColors.blueShade32,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(20, 10, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Center(
              child: Container(
                height: 5,
                width: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  color: isLightMode ? AppColors.greyShade5 : AppColors.blueShade36,
                ),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              LocaleKeys.filters.tr(),
              style: TextStyle(
                color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            Flexible(
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  mainAxisExtent: 80 * HiveUtilsSettings.textSizeFactor,
                  crossAxisCount: 2,
                  crossAxisSpacing: 20,
                  mainAxisSpacing: 8,
                ),
                itemCount: widget.screener.length,
                itemBuilder: (context, index) {
                  final item = widget.screener.elementAt(index);
                  final key = item.key!;

                  return MultiSelectDropDown<Items>(
                    label: item.name!,
                    minSelection: 1,
                    enableSelectAllOption: true,
                    compareFn: (v1, v2) => v1.value == v2.value,
                    selectedOptions: _selectedScreenerMap[key]!
                        .map(
                          (e) => ValueItem(label: e.name ?? '', value: e),
                        )
                        .toList(),
                    updatedSelection: (List<ValueItem<Items>> selectedOptions) {
                      _selectedScreenerMap[key] = selectedOptions.map((e) => e.value!).toList();
                    },
                    options: item.items!
                        .map(
                          (Items? item) => ValueItem(label: item?.name ?? '', value: item),
                        )
                        .toList(),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            TextButton(
              style: TextButton.styleFrom(
                backgroundColor: isLightMode ? AppColors.blueLight.withValues(alpha: 0.1) : null,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                  side: isLightMode ? BorderSide.none : const BorderSide(color: AppColors.white),
                ),
              ),
              onPressed: _onClearTapped,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    LocaleKeys.clear.tr(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isLightMode ? AppColors.blueLight : AppColors.white,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Lottie.asset(
                    isLightMode ? AnimationAsset.animationSync : AnimationAssetDark.animationSync,
                    width: 20,
                    height: 20,
                    fit: BoxFit.cover,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 14),
            TextButton(
              style: TextButton.styleFrom(
                backgroundColor: isLightMode ? AppColors.blueLight : AppColors.blueLightOld,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: _onApplyTapped,
              child: Text(
                LocaleKeys.apply.tr(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
