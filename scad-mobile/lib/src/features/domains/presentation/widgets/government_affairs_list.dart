import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/count_widget.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget.dart';
import 'package:scad_mobile/src/common/widgets/expandable_widget_list_item.dart';
import 'package:scad_mobile/src/common/widgets/no_data_placeholder.dart';
import 'package:scad_mobile/src/features/domains/data/models/government_affair_response_item.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

class GovernmentAffairList extends StatefulWidget {
  const GovernmentAffairList({required this.trustedZoneMap, required this.entitiesZoneMap, super.key});

  final Map<String, List<GovernmentAffairResponseItem>> trustedZoneMap;
  final Map<String, List<GovernmentAffairResponseItem>> entitiesZoneMap;

  @override
  State<GovernmentAffairList> createState() => _GovernmentAffairListState();
}

class _GovernmentAffairListState extends State<GovernmentAffairList>
    with TickerProviderStateMixin {
  final tabs = [
    LocaleKeys.trustedZone.tr(),
    LocaleKeys.entitiesZone.tr(),
  ];

  late final _tabController = TabController(length: 2, vsync: this);

  int _zoneIndex = 0;

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;

    return Column(
      children: [
        TabBar(
          onTap: (index) => setState(() => _zoneIndex = index),
          controller: _tabController,
          overlayColor: WidgetStateProperty.all<Color>(
            AppColors.blueLight.withValues(alpha: 0.1),
          ),
          labelPadding: const EdgeInsets.symmetric(horizontal: 1),
          indicatorSize: TabBarIndicatorSize.tab,
          labelColor: AppColors.blueLightOld,
          indicator: const UnderlineTabIndicator(
            borderSide: BorderSide(
              width: 3,
              color: AppColors.blueLightOld,
            ),
            insets: EdgeInsets.symmetric(horizontal: 4),
          ),
          tabs: List.generate(
            tabs.length,
            (i) => Tab(
              child: Text(
                tabs[i],
                style: [widget.trustedZoneMap, widget.entitiesZoneMap]
                        .elementAt(i)
                        .isEmpty
                    ? TextStyle(
                        color:
                            isLightMode ? AppColors.greyShade1 : AppColors.grey,
                      )
                    : null,
              ),
            ),
          ),
        ),
        Expanded(
          child: Builder(
            builder: (context) {
              final itemMap = _zoneIndex == 0
                  ? widget.trustedZoneMap
                  : widget.entitiesZoneMap;

              if (itemMap.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 150),
                    child: NoDataPlaceholder(),
                  ),
                );
              }

              return ListView.builder(
                itemCount: itemMap.keys.length,
                shrinkWrap: true,
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
                itemBuilder: (context, index) {
                  final MapEntry<String, List<GovernmentAffairResponseItem>>
                      item = itemMap.entries.toList().elementAt(index);

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 14),
                    child: ExpandableWidget(
                      title: item.key,
                      trailingIcon: item.value.isEmpty
                          ? null
                          : CountWidget(count: item.value.length),
                      expandedChild: ListView.builder(
                        itemCount: item.value.length,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(
                          vertical: 14,
                          horizontal: 14,
                        ),
                        itemBuilder: (context, i) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 10),
                            child: ExpandableWidgetListItem(
                              title: item.value[i].title ?? '',
                              trailingIcon: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Icon(
                                  Icons.chevron_right_rounded,
                                  size: 18,
                                  color: isLightMode
                                      ? AppColors.blueShade22
                                      : AppColors.white,
                                ),
                              ),
                              onTap: () {
                                final url = isLightMode
                                    ? item.value[i].dashboardUrlLight
                                    : item.value[i].dashboardUrlLight;
                                if (url == null) return;

                                final uri = Uri.parse(url);
                                launchUrl(
                                  uri,
                                  mode: LaunchMode.inAppBrowserView,
                                );
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
