part of 'debug_overlay_button.dart';

class DebugBottomPanel extends StatefulWidget {
  const DebugBottomPanel({
    required this.onClose,
    super.key,
  });

  final VoidCallback onClose;

  @override
  State<DebugBottomPanel> createState() => _DebugBottomPanelState();
}

class _DebugBottomPanelState extends State<DebugBottomPanel> {
  bool _enableInterceptorLog = false;
  bool _disableSlaCheck = false;

  bool _enableApiCaching = true;
  bool _enableApiCachingLog = false;
  bool _enableIndicatorDebug = false;
  double _textScaleFactor = 1;

  final _box = HiveUtilsPersistent.box;

  @override
  void initState() {
    super.initState();

    _enableInterceptorLog = _box.get('enableInterceptorLog', defaultValue: false) as bool;
    _disableSlaCheck = _box.get('disableSlaCheck', defaultValue: false) as bool;

    _enableApiCaching = _box.get('enableApiCaching', defaultValue: true) as bool;
    _enableApiCachingLog = _box.get('enableApiCachingLog', defaultValue: false) as bool;
    _enableIndicatorDebug = _box.get('enableIndicatorDebug', defaultValue: false) as bool;
    _textScaleFactor = HiveUtilsSettings.textSizeFactor;
  }

  Future<void> _clearApiCache() async {
    await HiveApiCacheBox.instance.clear();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('API Cache cleared successfully'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTitleBar(),
              const Divider(),
              const SizedBox(height: 8),
              const Text(
                'API Requests',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SwitchListTile(
                title: const Text('Enable Http Logs'),
                value: _enableInterceptorLog,
                activeColor: AppColors.blueLightOld,
                onChanged: (value) {
                  _box.put('enableInterceptorLog', value);
                  setState(() => _enableInterceptorLog = value);
                },
              ),
              SwitchListTile(
                title: const Text('Disable SLA Check APIs'),
                value: _disableSlaCheck,
                activeColor: AppColors.blueLightOld,
                onChanged: (value) {
                  _box.put('disableSlaCheck', value);
                  setState(() => _disableSlaCheck = value);
                },
              ),
              const Divider(),
              const SizedBox(height: 8),
              const Text(
                'API Cache',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SwitchListTile(
                title: const Text('Enable API Caching'),
                value: _enableApiCaching,
                activeColor: AppColors.blueLightOld,
                onChanged: (value) {
                  _box.put('enableApiCaching', value);
                  setState(() => _enableApiCaching = value);
                },
              ),
              SwitchListTile(
                title: const Text('Enable API Caching Logs'),
                value: _enableApiCachingLog,
                activeColor: AppColors.blueLightOld,
                onChanged: (value) {
                  _box.put('enableApiCachingLog', value);
                  setState(() => _enableApiCachingLog = value);
                },
              ),
              ListTile(
                title: const Text('Clear API Cache'),
                trailing: ElevatedButton(
                  onPressed: _clearApiCache,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.blueLightOld,
                  ),
                  child: const Text('Clear'),
                ),
              ),
              const Divider(),
              const SizedBox(height: 8),
              const Text(
                'Text Size',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    const Text('Small'),
                    Expanded(
                      child: Slider(
                        value: _textScaleFactor,
                        min: 0.7,
                        max: 1.3,
                        divisions: 2,
                        label: _textScaleFactor.toStringAsFixed(1),
                        activeColor: AppColors.blueLightOld,
                        onChanged: (value) {
                          setState(() {
                            _textScaleFactor = value;
                            HiveUtilsSettings.setTextSizeFactor(value);
                          });
                        },
                      ),
                    ),
                    const Text('Large'),
                  ],
                ),
              ),
              const Divider(),
              const SizedBox(height: 8),
              const Text(
                'Indicator Debug',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SwitchListTile(
                title: const Text('Show Indicator Debug Data'),
                value: _enableIndicatorDebug,
                activeColor: AppColors.blueLightOld,
                onChanged: (value) {
                  _box.put('enableIndicatorDebug', value);
                  setState(() => _enableIndicatorDebug = value);
                },
              ),
              const SizedBox(height: 8),
              // Add more debug options here as needed
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitleBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'Debug Options',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: widget.onClose,
        ),
      ],
    );
  }
}
