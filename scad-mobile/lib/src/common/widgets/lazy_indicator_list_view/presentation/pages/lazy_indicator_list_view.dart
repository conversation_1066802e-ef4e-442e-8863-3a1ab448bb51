import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/lazy_indicator_list_view/presentation/bloc/lazy_indicator_list_view_bloc.dart';

const _kBufferSize = 10;
const _kMinLoadingSize = 5;

typedef LazyItemBuilder<T> = Widget Function(
  BuildContext context,
  int index,
  T item,
  OverView? overview,
);

typedef ObjectParamGetter<T, R> = R Function(T item);

class LazyIndicatorListView<T> extends StatefulWidget {
  const LazyIndicatorListView({
    required this.items,
    required this.itemBuilder,
    required this.getId,
    required this.getContentType,
    this.padding = EdgeInsets.zero,
    this.scrollController,
    this.parentScrollController,
    this.onScrollOverflow,
    this.shouldLoadOverview = true,
    super.key,
  }) : assert(
          parentScrollController == null || scrollController == null,
          'Please use either `parentScrollController` or `scrollController`',
        );

  final List<T> items;
  final EdgeInsetsGeometry padding;

  /// The default scroll controller is this list view will be overridden with [parentScrollController]
  ///
  /// This controller __will not be assigned__ to any [ListView] also if this controller is not null
  ///
  /// then the [ListView] will have [NeverScrollableScrollPhysics]
  final ScrollController? parentScrollController;

  /// Controller that will override the scroll controller in the [ListView]
  final ScrollController? scrollController;
  final LazyItemBuilder<T> itemBuilder;

  final ObjectParamGetter<T, String> getId;
  final ObjectParamGetter<T, String> getContentType;

  /// This will be called when no more items can be lazily loaded
  ///
  /// Can be used for paginated api calls etc.,
  final VoidCallback? onScrollOverflow;

  final bool shouldLoadOverview;

  @override
  State<LazyIndicatorListView<T>> createState() => _LazyIndicatorListViewState<T>();
}

class _LazyIndicatorListViewState<T> extends State<LazyIndicatorListView<T>> {
  Iterable<T> get _allList => widget.items;
  final _lazyList = <T>[];

  bool _canLoadNextSet = false;

  final _pendingLoadingNotifier = ValueNotifier(0);

  final Map<String, OverView> _overviews = {};

  late final _scrollController =
      widget.parentScrollController == null ? widget.scrollController ?? ScrollController() : null;

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((timestamp) {
      _registerScrollListeners();
      _loadNextSet();
    });
  }

  void _registerScrollListeners() {
    final controller = widget.parentScrollController ?? _scrollController;
    controller?.addListener(
      () => _scrollListener(controller),
    );
  }

  @override
  void didUpdateWidget(covariant LazyIndicatorListView<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    _registerScrollListeners();

    if (oldWidget.items.hashCode != widget.items.hashCode) {
      _lazyList.clear();
      return _loadNextSet();
    }

    if (_lazyList.isEmpty || _canLoadNextSet) {
      return _loadNextSet();
    }
  }

  @override
  Widget build(BuildContext context) {
    final scrollPhysics = widget.parentScrollController != null ? const NeverScrollableScrollPhysics() : null;

    return BlocListener<LazyIndicatorListViewBloc, LazyIndicatorListViewState>(
      listener: _blocListener,
      child: ListView.builder(
        controller: _scrollController,
        shrinkWrap: true,
        physics: scrollPhysics,
        padding: widget.padding,
        itemCount: _lazyList.length + 1,
        itemBuilder: (context, index) {
          if (index == _lazyList.length) {
            return _buildLoadingText();
          }

          final item = _lazyList.elementAt(index);
          final overview = _overviews[widget.getId(item)];
          return widget.itemBuilder(context, index, item, overview);
        },
      ),
    );
  }

  Widget _buildLoadingText() {
    return ValueListenableBuilder(
      valueListenable: _pendingLoadingNotifier,
      builder: (context, value, child) {
        if (value < _kMinLoadingSize) return const SizedBox.shrink();
        return child!;
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24),
        child: Center(
          child: Text(
            'Loading...',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
        ),
      ),
    );
  }

  void _blocListener(BuildContext context, LazyIndicatorListViewState state) {
    if (state is LazyIndicatorItemState) {
      _pendingLoadingNotifier.value = state.statusMap.values.where((e) => e == 'loading').length;

      if (_canLoadNextSet && _pendingLoadingNotifier.value < _kMinLoadingSize) {
        _loadNextSet();
      }
    }

    if (state is GetIndicatorOverviewOfTypeState) {
      if (state.overviews.isNotEmpty) {
        setState(
          () => _overviews.addAll(state.overviews),
        );
      }
    }

    if (state is GetIndicatorOverviewOfTypeErrorState) {
      debugPrint('_LazyIndicatorListViewState._blocListener: '
          '🐞failed to load indicator overview');
    }
  }

  void _scrollListener(ScrollController controller) {
    if (controller.position.pixels < controller.position.maxScrollExtent) {
      return;
    }

    _canLoadNextSet = true;
    if (_lazyList.length >= _allList.length) {
      return widget.onScrollOverflow?.call();
    }

    _loadNextSet();
  }

  void _loadNextSet() {
    if (_pendingLoadingNotifier.value >= _kMinLoadingSize || _lazyList.length == _allList.length) {
      return;
    }

    final list = _allList.skip(_lazyList.length).take(_kBufferSize).toList();

    if (widget.shouldLoadOverview) {
      _loadIndicatorOverview(list);
    }

    if (mounted) {
      setState(
        () {
          _lazyList.addAll(list);
          _canLoadNextSet = false;
        },
      );
    }
  }

  void _loadIndicatorOverview(List<T> indicators) {
    final map = <String, List<String>>{};
    for (final indicator in indicators) {
      final contentType = widget.getContentType(indicator);

      const kAllowedContentTypes = [
        'scad_official_indicator',
        'innovative-insights',
        'official_statistics',
      ];

      if (!kAllowedContentTypes.contains(contentType)) continue;

      final id = widget.getId(indicator);
      if (map.containsKey(contentType)) {
        map[contentType]!.add(id);
      } else {
        map[contentType] = [id];
      }
    }

    for (final key in map.keys) {
      if (mounted) {
        context.read<LazyIndicatorListViewBloc>().add(
              GetIndicatorOverviewOfTypeEvent(ids: map[key]!..removeWhere((e) => e.contains('_')), type: key),
            );
      }
    }
  }
}
