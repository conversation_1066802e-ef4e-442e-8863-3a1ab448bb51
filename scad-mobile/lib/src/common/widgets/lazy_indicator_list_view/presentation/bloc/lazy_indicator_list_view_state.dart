part of 'lazy_indicator_list_view_bloc.dart';

class LazyIndicatorListViewState extends Equatable {
  const LazyIndicatorListViewState();

  @override
  List<Object?> get props => [];
}

class LazyIndicatorListViewVoidState extends LazyIndicatorListViewState {}

class LazyIndicatorItemState extends LazyIndicatorListViewState {
  LazyIndicatorItemState({
    required this.statusMap,
  }) : rnd = Random().nextInt(10000);

  late final int rnd;
  final Map<String, String> statusMap;

  @override
  List<Object> get props => [statusMap, rnd];
}

class GetIndicatorOverviewOfTypeState extends LazyIndicatorListViewState {
  const GetIndicatorOverviewOfTypeState(this.event, this.overviews);

  final Map<String, OverView> overviews;
  final GetIndicatorOverviewOfTypeEvent event;

  @override
  List<Object> get props => [overviews.hashCode, event.hashCode];
}

class GetIndicatorOverviewOfTypeErrorState extends LazyIndicatorListViewState {
  const GetIndicatorOverviewOfTypeErrorState({
    required this.error,
    required this.event,
  });

  final String error;
  final GetIndicatorOverviewOfTypeEvent event;

  @override
  List<Object> get props => [error, event.hashCode];
}
