part of 'lazy_indicator_list_view_bloc.dart';

class LazyIndicatorListViewEvent extends Equatable {
  const LazyIndicatorListViewEvent();

  @override
  List<Object?> get props => [];
}

class LazyIndicatorItemEvent extends LazyIndicatorListViewEvent {
  const LazyIndicatorItemEvent({
    required this.id,
    required this.contentType,
    required this.status,
  });

  final String id;
  final String contentType;
  final String status;

  @override
  List<Object> get props => [id, contentType, status];
}

class GetIndicatorOverviewOfTypeEvent extends LazyIndicatorListViewEvent {
  const GetIndicatorOverviewOfTypeEvent({
    required this.ids,
    required this.type,
  });

  final List<String> ids;
  final String type;

  @override
  List<Object> get props => [...ids, type];
}
