import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_details_helper_v2.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/features/settings/presentation/bloc/setting_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class FlatAppBar extends StatefulWidget {
  const FlatAppBar({
    required this.title,
    this.mainScreen = false,
    super.key,
    this.onBack,
    this.scrollController,
    this.toHideBellIcon = false,
    this.fromChatScreen = false,
    this.bellIconKey,
    this.searchIconKey,
    this.appBarHeight,
    this.bottomPadding = 30,
    this.indicatorType,
    this.isDetailsPage = false,
  });

  final String title;
  final bool mainScreen;
  final VoidCallback? onBack;
  final ScrollController? scrollController;
  final bool toHideBellIcon;
  final bool fromChatScreen;
  final GlobalKey? bellIconKey;
  final GlobalKey? searchIconKey;
  final double? appBarHeight;
  final double? bottomPadding;
  final IndicatorType? indicatorType;
  final bool isDetailsPage;

  @override
  State<FlatAppBar> createState() => _FlatAppBarState();
}

class _FlatAppBarState extends State<FlatAppBar> {
  ValueNotifier<bool> isExpanded = ValueNotifier<bool>(true);
  bool isUnreadAllNotification = false;

  final isLightMode = HiveUtilsSettings.isLightMode;

  @override
  void initState() {
    super.initState();
    if (widget.scrollController != null) {
      widget.scrollController!.addListener(_onScroll);
    }
    isUnreadAllNotification =
        HiveUtilsSettings.getAllNotificationsUnReadStatus();
  }

  @override
  void didUpdateWidget(covariant FlatAppBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    final hasScrollControllerChanged =
        oldWidget.scrollController.hashCode != widget.scrollController.hashCode;
    if (hasScrollControllerChanged) {
      oldWidget.scrollController?.removeListener(_onScroll);
      widget.scrollController?.addListener(_onScroll);
    }
  }

  void _onScroll() {
    if (widget.scrollController != null) {
      if (widget.scrollController!.position.pixels <
          widget.scrollController!.position.minScrollExtent +
              (widget.fromChatScreen ? 0 : 10)) {
        isExpanded.value = true;
      } else if (widget.scrollController!.position.extentTotal >
          MediaQuery.sizeOf(context).height) {
        isExpanded.value = false;
      }
      if (widget.scrollController!.position.maxScrollExtent >
          (MediaQuery.sizeOf(context).height * 1.5)) {
        if (widget.scrollController!.position.userScrollDirection ==
            ScrollDirection.forward) {
          isExpanded.value = true;
        }
      }
    }
  }

  @override
  void dispose() {
    widget.scrollController?.removeListener(_onScroll);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isLightMode = HiveUtilsSettings.isLightMode;
    final bool isArabic = HiveUtilsSettings.isLanguageArabic;

    final Widget child = Container(
      height: 11,
      width: 11,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.red,
      ),
    );
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: ValueListenableBuilder(
        valueListenable: isExpanded,
        builder: (context, domain, w) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: MediaQuery.paddingOf(context).top),
              Row(
                children: [
                  Transform.flip(
                    flipX: isArabic,
                    child: appDrawerController.drawerButton(
                      lightIcon: isLightMode ? false : true,
                    ),
                  ),
                  IgnorePointer(
                    // Added dummy IconButton to align the title in center
                    child: IconButton(
                      onPressed: () {},
                      icon: const SizedBox(),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: AnimatedOpacity(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.fastEaseInToSlowEaseOut,
                        opacity: !domain ? 1 : 0,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Flexible(
                              child: Text(
                                widget.title,
                                style: AppTextStyles.s20w5cBlackOrWhiteShade
                                    .copyWith(
                                  color: isLightMode
                                      ? AppColors.black
                                      : AppColors.white,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                            ),
                            indicatorTypeIcon(
                              padding: const EdgeInsets.only(
                                left: 6,
                                bottom: 6,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (!widget.toHideBellIcon)
                    widget.bellIconKey != null
                        ? IntroWidget(
                            stepKey: widget.bellIconKey ??
                                GlobalKey(debugLabel: 'null-bellIconKey'),
                            stepIndex: 1,
                            totalSteps: 2,
                            title: LocaleKeys.notifications.tr(),
                            description:
                                LocaleKeys.notificationsGuideDesc.tr(),
                            arrowAlignment: Alignment.topRight,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            targetBorderRadius: 60,
                            arrowPadding: EdgeInsets.only(
                              right:
                                  MediaQuery.sizeOf(context).width * 0.11,
                              left: MediaQuery.sizeOf(context).width * 0.11,
                              bottom: 10,
                            ),
                            child: notificationIconButton(context, child),
                          )
                        : notificationIconButton(context, child),
                  if (widget.searchIconKey != null)
                    IntroWidget(
                      stepKey: widget.searchIconKey ??
                          GlobalKey(debugLabel: 'null-searchIconKey'),
                      stepIndex: 2,
                      totalSteps: 2,
                      title: LocaleKeys.globalSearch.tr(),
                      description: LocaleKeys.globalSearchGuideDesc.tr(),
                      arrowAlignment: Alignment.topRight,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      targetBorderRadius: 60,
                      arrowPadding: const EdgeInsets.only(
                        right: 2,
                        left: 2,
                        bottom: 10,
                      ),
                      child: searchIconButton(context),
                    )
                  else
                    searchIconButton(context),
                ],
              ),
              AnimatedSize(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOutCubicEmphasized,
                child: SizedBox(
                  height: isExpanded.value ? null : 0,
                  child: _expandedView(context),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  IconButton searchIconButton(BuildContext context) {
    return IconButton(
      onPressed: () {
        context.router
            .removeWhere((route) => route.name == SearchScreenRoute.name);
        context.pushRoute(SearchScreenRoute());
      },
      icon: SvgPicture.asset(
        AppImages.icSearch,
        colorFilter: ColorFilter.mode(
          isLightMode ? const Color(0xFF37506A) : Colors.grey,
          BlendMode.srcIn,
        ),
      ),
    );
  }

  IconButton notificationIconButton(BuildContext context, Widget child) {
    return IconButton(
      onPressed: () {
        context.pushRoute(const NotificationListRoute());
      },
      icon: Stack(
        children: [
          SvgPicture.asset(
            AppImages.icNotification,
            colorFilter: ColorFilter.mode(
              isLightMode ? const Color(0xFF37506A) : Colors.grey,
              BlendMode.srcIn,
            ),
          ),
          BlocConsumer<SettingBloc, SettingState>(
            listener: (context, state) {
              if (state is DefaultSettingSuccessState) {
                isUnreadAllNotification =
                    state.defaultSettingResponse.notificationUnread ?? false;
              }
            },
            builder: (context, state) =>
                BlocConsumer<NotificationBloc, NotificationState>(
              listener: (context, state) {
                if (state is ReadNotificationSuccessState) {
                  isUnreadAllNotification = state.isAllUnRead;
                }
              },
              builder: (context, state) => isUnreadAllNotification
                  ? DeviceType.isDirectionRTL(context)
                      ? Positioned(
                          top: 0,
                          left: 0,
                          child: child,
                        )
                      : Positioned(
                          top: 0,
                          right: 0,
                          child: child,
                        )
                  : const SizedBox(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _expandedView(BuildContext context) {
    return Column(
      children: [
        Row(
          key: const Key('expanded'),
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (widget.mainScreen)
              const SizedBox()
            else
              Row(
                children: [
                  InkWell(
                    onTap: () {
                      try {
                        Navigator.pop(context);
                        // context.back();
                        if (widget.onBack != null) {
                          widget.onBack?.call();
                        }
                      } catch (e) {
                        // error;
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 14,
                          vertical: 14,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            RotatedBox(
                              quarterTurns:
                                  DeviceType.isDirectionRTL(context) ? 2 : 0,
                              child: SvgPicture.asset(
                                AppImages.icArrowLeft,
                                colorFilter: ColorFilter.mode(
                                  isLightMode
                                      ? AppColors.blueLight
                                      : AppColors.blue,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                            // Text(
                            //   LocaleKeys.back.tr(),
                            //   style: AppTextStyles.s14w4cBlue,
                            //   textScaler:
                            //       TextScaler.linear(textScaleFactor.value),
                            // ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),

            if (widget.isDetailsPage)
              Expanded(
                child: Padding(
                  padding: widget.mainScreen
                      ? const EdgeInsets.symmetric(horizontal: 10)
                      : (DeviceType.isDirectionRTL(context)
                          ? const EdgeInsets.only(left: 16)
                          : const EdgeInsets.only(right: 16)),
                  child: _title(),
                ),
              )
            else
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: Center(
                        child: Padding(
                          padding: widget.mainScreen
                              ? const EdgeInsets.symmetric(horizontal: 8)
                              : (DeviceType.isDirectionRTL(context)
                                  ? const EdgeInsets.only(left: 20)
                                  : const EdgeInsets.only(right: 20)),
                          child: Text(
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            widget.title,
                            textAlign: TextAlign.center,
                            style: AppTextStyles.s24w5cBlackOrWhiteShade.copyWith(
                              color: isLightMode
                                  ? AppColors.blueTitleText
                                  : AppColors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        SizedBox(height: widget.bottomPadding),
      ],
    );
  }

  Widget _title() {
    return Center(
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: widget.title,
              style: TextStyle(
                color: isLightMode ? AppColors.blackShade1 : AppColors.white,
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
            ),
            WidgetSpan(
              child: indicatorTypeIcon(
                padding: const EdgeInsets.only(left: 14),
              ),
            ),
          ],
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget indicatorTypeIcon({EdgeInsets padding = EdgeInsets.zero}) {
    return Padding(
      padding: padding,
      child: widget.indicatorType == IndicatorType.official
          ? SvgPicture.asset(AppImages.icOfficialActive)
          : widget.indicatorType == IndicatorType.experimental
              ? SvgPicture.asset(AppImages.icExperimentalActive)
              : const SizedBox(),
    );
  }
}
