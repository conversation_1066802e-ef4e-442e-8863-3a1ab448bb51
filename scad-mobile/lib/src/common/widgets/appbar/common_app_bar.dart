import 'dart:math' as math;

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:scad_mobile/route_manager/route_imports.gr.dart';
import 'package:scad_mobile/src/common/widgets/drawer/app_drawer_part.dart';
import 'package:scad_mobile/src/features/notification/presentation/bloc/notification_bloc.dart';
import 'package:scad_mobile/src/features/settings/presentation/bloc/setting_bloc.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_utils_settings.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';

class CommonAppBar extends StatefulWidget {
  const CommonAppBar({
    required this.title,
    this.showButtons = true,
    super.key,
  });

  final String title;
  final bool showButtons;

  @override
  State<CommonAppBar> createState() => _CommonAppBarState();
}

class _CommonAppBarState extends State<CommonAppBar> {
  bool isUnreadAllNotification = false;

  @override
  void initState() {
    super.initState();

    if(widget.showButtons) {
      isUnreadAllNotification =
          HiveUtilsSettings.getAllNotificationsUnReadStatus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final rtl = DeviceType.isDirectionRTL(context);
    final Widget diagonalWidget = Container(
      height: 200,
      width: MediaQuery.sizeOf(context).width,
      decoration: BoxDecoration(color: AppColors.white.withValues(alpha: 0.08)),
    );

    return Container(
      width: double.maxFinite,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        color: AppColors.blueShade22,
      ),
      child: Stack(
        children: [
          if (rtl)
            Positioned(
              bottom: -80,
              left: -240,
              child: Transform.rotate(
                angle: math.pi / 6.0,
                child: diagonalWidget,
              ),
            )
          else
            Positioned(
              bottom: -100,
              right: -240,
              child: Transform.rotate(
                angle: -math.pi / 6.0,
                child: diagonalWidget,
              ),
            ),
          if (rtl)
            Positioned(
              left: 30,
              bottom: 0,
              child: Transform.flip(
                flipX: true,
                child: SvgPicture.asset(
                  AppImages.icEnvelope,
                  height: 60,
                  width: 60,
                ),
              ),
            )
          else
            Positioned(
              right: 30,
              bottom: 0,
              child: SvgPicture.asset(
                AppImages.icEnvelope,
                height: 60,
                width: 60,
              ),
            ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: MediaQuery.paddingOf(context).top + 12),
              if (!widget.showButtons)
                const SizedBox(height: 20)
              else
                Row(
                  children: [
                    Transform.flip(
                      flipX: HiveUtilsSettings.isLanguageArabic,
                      child: appDrawerController.drawerButton(),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () {
                        context.pushRoute(const NotificationListRoute());
                      },
                      icon: Stack(
                        children: [
                          SvgPicture.asset(
                            AppImages.icNotification,
                            colorFilter: const ColorFilter.mode(
                              Colors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                          BlocConsumer<SettingBloc, SettingState>(
                            listener: (context, state) {
                              if (state is DefaultSettingSuccessState) {
                                isUnreadAllNotification = state
                                        .defaultSettingResponse
                                        .notificationUnread ??
                                    false;
                              }
                            },
                            builder: (context, state) => BlocConsumer<
                                NotificationBloc, NotificationState>(
                              listener: (context, state) {
                                if (state is ReadNotificationSuccessState) {
                                  isUnreadAllNotification = state.isAllUnRead;
                                }
                              },
                              builder: (context, state) {
                                return isUnreadAllNotification
                                    ? Builder(
                                        builder: (context) {
                                          final Widget child = Container(
                                            height: 11,
                                            width: 11,
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: AppColors.red,
                                            ),
                                          );
                                          return DeviceType.isDirectionRTL(context)
                                              ? Positioned(
                                                  top: 0,
                                                  left: 0,
                                                  child: child,
                                                )
                                              : Positioned(
                                                  top: 0,
                                                  right: 0,
                                                  child: child,
                                                );
                                        },
                                      )
                                    : const SizedBox();
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        context.router.removeWhere((route) => route.name == SearchScreenRoute.name);
                        context.pushRoute(SearchScreenRoute());
                      },
                      icon: SvgPicture.asset(
                        AppImages.icSearch,
                        colorFilter: const ColorFilter.mode(
                          Colors.white,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
              Row(
                children: [
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        context.maybePop();
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: RotatedBox(
                          quarterTurns: DeviceType.isDirectionRTL(context) ? 2 : 0,
                          child: SvgPicture.asset(
                            AppImages.icArrowLeft,
                            colorFilter: const ColorFilter.mode(
                              AppColors.white,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 2),
                      child: Text(
                        widget.title,
                        style: AppTextStyles.s24w5cBlackOrWhiteShade.copyWith(color: AppColors.white),
                      ),
                    ),
                  ),
                  const SizedBox(width: 100),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ],
      ),
    );
  }
}
