import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/user_guide/showcaseview_package/intro_widget.dart';

extension IntroWidgetHelper on Widget {
  Widget introWidget({
    required String title,
    required String description,
    required List<GlobalKey> steps,
    required int index,
    required AlignmentGeometry arrowAlignment,
    required CrossAxisAlignment crossAxisAlignment,
    required double arrowPadding,
    double targetBorderRadius = 150,
     VoidCallback? onNext,
     VoidCallback? onPrevious,
  }) {
    if(steps.isEmpty){
      return this;
    }
    return IntroWidget(
      stepKey: steps[index],
      stepIndex: index+1,
      totalSteps: steps.length,
      title: title,
      description: description,
      arrowAlignment: arrowAlignment,
      crossAxisAlignment: crossAxisAlignment,
      targetBorderRadius: targetBorderRadius,
      arrowPadding: EdgeInsets.symmetric(horizontal: arrowPadding),
      onNext: onNext,
      onPrevious: onPrevious,
      child: this,
    );
  }
}
