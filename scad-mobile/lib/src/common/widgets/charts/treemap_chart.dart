import 'package:flutter/material.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/utils/app_utils/device_type.dart';
import 'package:scad_mobile/src/utils/app_utils/string_utils.dart';
import 'package:scad_mobile/src/utils/constants/color_constants/color_constants.dart';
import 'package:scad_mobile/src/utils/extentions/color_extensions.dart';
import 'package:scad_mobile/src/utils/styles/app_text_styles.dart';
import 'package:syncfusion_flutter_treemap/treemap.dart';

class TreemapChart extends StatefulWidget {
  const TreemapChart({
    required this.chartSeriesData,
    super.key,
  });

  final List<SeriesMeta> chartSeriesData;

  @override
  State<TreemapChart> createState() => _TreemapChartState();
}

class _TreemapChartState extends State<TreemapChart> {
  List<TreemapColorMapper> colorMappers = [];
  List<TreeMapChartData> chartData = [];

  @override
  void didUpdateWidget(covariant TreemapChart oldWidget) {
    getChartData();
    super.didUpdateWidget(oldWidget);
  }

  void getChartData() {
    final rtl = DeviceType.isDirectionRTL(context);

    chartData = [];
    colorMappers = [];
    for (final value in widget.chartSeriesData) {
      if (double.parse('${value.data?.firstOrNull?['VALUE_PERC_ECO'] ?? '0'}') == 0) {
        continue;
      }

      chartData.add(
        TreeMapChartData(
          sector: value.data?.firstOrNull?[rtl ? 'SECTOR_AR' : 'SECTOR'].toString() ?? '',
          currentValue: double.parse(
            '${value.data?.firstOrNull?['VALUE_CURRENT'] ?? '0'}',
          ),
          forecastedValue: double.parse(
            '${value.data?.firstOrNull?['VALUE_FORECAST'] ?? '0'}',
          ),
          qqChange: double.parse(
            '${value.data?.firstOrNull?['CHANGE'] ?? '0'}',
          ),
          yyChange: double.parse(
            '${value.data?.firstOrNull?['CHANGE_PY'] ?? '0'}',
          ),
          proportionOfTotalEconomy: double.parse(
            '${value.data?.firstOrNull?['VALUE_PERC_ECO'] ?? '0'}',
          ),
        ),
      );

      colorMappers.add(
        TreemapColorMapper.value(
          value: value.data?.firstOrNull?['CHANGE'].toString(),
          color: value.color.toString().toColor(),
        ),
      );
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    getChartData();

    return SfTreemap(
      dataCount: chartData.length,
      weightValueMapper: (int index) {
        return chartData[index].proportionOfTotalEconomy;
      },
      colorMappers: colorMappers,
      levels: [
        TreemapLevel(
          groupMapper: (int index) {
            return chartData[index].sector;
          },
          colorValueMapper: (TreemapTile tile) {
            return chartData[tile.indices.first].qqChange.toString();
          },
          labelBuilder: (BuildContext context, TreemapTile tile) {
            return Container(
              padding: const EdgeInsets.all(4),
              child: Column(
                children: [
                  Expanded(
                    child: Center(
                      child: Text.rich(
                        maxLines: 2,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        TextSpan(
                          children: [
                            TextSpan(
                              text: '${tile.group}:',
                              style: const TextStyle(fontSize: 10),
                            ),
                            TextSpan(
                              text: ' ${chartData[tile.indices.first].qqChange.toStringAsFixed(1)}',
                              style: const TextStyle(fontSize: 10),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Align(
                    alignment: Alignment.bottomRight,
                    child: Text(
                      '${ShortNumberPipe.transform(number: chartData[tile.indices.first].proportionOfTotalEconomy, angularPipeFormat: '0.0-2')}%',
                      style: const TextStyle(fontSize: 10),
                    ),
                  ),
                ],
              ),
            );
          },
          tooltipBuilder: (BuildContext context, TreemapTile tile) {
            return Padding(
              padding: const EdgeInsets.all(8),
              child: Text(
                toolTipString(tile),
                style: AppTextStyles.s12w5cBlack,
              ),
            );
          },
        ),
      ],
      tooltipSettings: const TreemapTooltipSettings(
        color: AppColors.greyFAFAFA,
        hideDelay: 60,
      ),
    );
  }

  String toolTipString(TreemapTile tile) {
    return '${tile.group}\n'
        'Current Value: ${ShortNumberPipe.transform(number: chartData[tile.indices.first].currentValue, angularPipeFormat: '0.0-2')}\n'
        'Forecasted Value: ${ShortNumberPipe.transform(number: chartData[tile.indices.first].forecastedValue, angularPipeFormat: '0.0-2')}\n'
        'Q/Q Change: ${ShortNumberPipe.transform(number: chartData[tile.indices.first].qqChange, angularPipeFormat: '0.0-2')}\n'
        'Y/Y Change: ${ShortNumberPipe.transform(number: chartData[tile.indices.first].yyChange, angularPipeFormat: '0.0-2')}\n'
        'Proportion of Total Economy: ${ShortNumberPipe.transform(number: chartData[tile.indices.first].proportionOfTotalEconomy, angularPipeFormat: '0.0-2')}%';
  }
}

class TreeMapChartData {
  const TreeMapChartData({
    required this.sector,
    required this.proportionOfTotalEconomy,
    required this.qqChange,
    required this.yyChange,
    required this.currentValue,
    required this.forecastedValue,
  });

  final String sector; // SECTOR/SECTOR_AR
  final double proportionOfTotalEconomy; // VALUE_PERC_ECO
  final double qqChange; // CHANGE
  final double yyChange; // CHANGE_PY
  final double currentValue; // VALUE_CURRENT
  final double forecastedValue; // VALUE_FORECAST
}
