class ChartUtils {
  ChartUtils._();

  static double calculateInterval({
    required int dataLength,
    required String frequency,
    required Map<String, DateTime> maxAndMin,
  }) {
    final days = maxAndMin['max']?.difference(maxAndMin['min']!).inDays ?? 0;
    if (days == 0 || dataLength < 2) return 1;

    // For yearly data, we want to ensure the interval is exactly 1 year
    // This will make the grid lines align perfectly with the bars
    if (frequency == 'Yearly') {
      final years = days ~/ 365;
      if (years > 20) return 5;
      return 1;
    }

    if (frequency == 'Quarterly') {
      final quarters = days ~/ 90;
      if (quarters > 4 * 32) return 48; //  32 years
      if (quarters > 4 * 16) return 24; // 24 years
      if (quarters > 4 * 8) return 12; //  12 years
      if (quarters > 4 * 4) return 6; //  4 years
      // For quarterly data, we want to ensure the interval is a multiple of 3 months
      return 3;
    }

    // For monthly data, we want to ensure the interval is a multiple of months
    // that aligns with the data points
    final months = days ~/ 30;
    if (months > 96) return 24;
    if (months > 48) return 12;
    if (months > 24) return 4;
    if (months > 12) return 2;
    return 1;
  }
}
