import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/src/common/widgets/maintenance/maintenance_view.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/sla_check_response.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';

typedef MaintenanceCheckPredicate = bool Function(SlaCheckResponse);

class MaintenanceChecker extends StatefulWidget {
  const MaintenanceChecker({
    required this.checkPredicate,
    required this.child,
    super.key,
  });

  /// If this response is true. Maintenance view will be shown
  final MaintenanceCheckPredicate checkPredicate;

  final Widget child;

  @override
  State<MaintenanceChecker> createState() => _MaintenanceCheckerState();
}

class _MaintenanceCheckerState extends State<MaintenanceChecker> {
  SlaCheckResponse? _slaResponse;

  bool get _isUnderMaintenance {
    final sla = _slaResponse;
    if (sla == null) return false;
    return widget.checkPredicate.call(sla);
  }

  @override
  void initState() {
    super.initState();
    context.read<HomeBloc>().add(const SlaCheckEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listener: _maintenanceListener,
      child: Center(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 600),
          switchInCurve: Curves.fastEaseInToSlowEaseOut,
          switchOutCurve: Curves.fastEaseInToSlowEaseOut,
          child: _isUnderMaintenance
              ? _MaintenanceObserverOnLifecycle(
                  data: _slaResponse!,
                )
              : widget.child,
        ),
      ),
    );
  }

  void _maintenanceListener(BuildContext context, HomeState state) {
    if (state is! SlaCheckSuccessState) return;

    final isPredicateTrue = widget.checkPredicate.call(state.access);
    if (isPredicateTrue != _isUnderMaintenance) {
      setState(() => _slaResponse = state.access);
    }
  }
}

/// Widget to check maintenance when widget life cycle changes.
///
/// Applying this directly in `MaintenanceChecker` will increase cost if wrapped on multiple widgets.`
class _MaintenanceObserverOnLifecycle extends StatefulWidget {
  const _MaintenanceObserverOnLifecycle({
    required this.data,
  });

  final SlaCheckResponse data;

  @override
  State<_MaintenanceObserverOnLifecycle> createState() => _MaintenanceObserverOnLifecycleState();
}

class _MaintenanceObserverOnLifecycleState extends State<_MaintenanceObserverOnLifecycle> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state != AppLifecycleState.resumed) return;
    context.read<HomeBloc>().add(const SlaCheckEvent());
  }

  @override
  Widget build(BuildContext context) => MaintenanceView(
        data: widget.data,
      );

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
