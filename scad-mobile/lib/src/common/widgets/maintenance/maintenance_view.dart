import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:scad_mobile/src/features/home/<USER>/models/sla_check_response.dart';
import 'package:scad_mobile/src/utils/constants/asset_constants/image_constants.dart';

class MaintenanceView extends StatelessWidget {
  const MaintenanceView({
    required this.data,
    super.key,
  });

  final SlaCheckResponse data;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          AppImages.maintenance,
        ),
        const SizedBox(height: 32),
        Text(
          data.title ?? '',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          data.body ?? '',
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        Text(
          '${data.fromDate} to ${data.endDate}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
