part of '../app_drawer_part.dart';

/// Advanced Drawer Controller that manage drawer state.
class AppDrawerController extends ValueNotifier<DrawerState> {
  /// Creates controller with initial drawer state. (Hidden by default)
  AppDrawerController([DrawerState? value])
      : super(value ?? DrawerState.hidden());

  /// Shows drawer.
  void showDrawer() {
    value = DrawerState.visible();
    notifyListeners();
  }

  /// Hides drawer.
  void hideDrawer() {
    value = DrawerState.hidden();
    notifyListeners();
  }

  /// Toggles drawer.
  void toggleDrawer() {
    if (value.visible) {
      return hideDrawer();
    }

    return showDrawer();
  }

  Widget drawerButton({bool lightIcon=true, bool isArabic = false}) {
    return Material(
      color: Colors.transparent,
      child: IconButton(
        onPressed: () {
          showDrawer();
        },
        icon: Transform.flip(
          flipX: isArabic,
          child: SvgPicture.asset(
              AppImages.icHamburgerMenu,
          colorFilter: ColorFilter.mode(
              lightIcon
              ?AppColors.white
              :AppColors.black
              , BlendMode.srcIn,),
        ),
        ),
      ),
    );
  }
}
