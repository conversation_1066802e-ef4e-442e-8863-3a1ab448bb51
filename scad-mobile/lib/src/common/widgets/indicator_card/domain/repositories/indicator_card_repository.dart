part of 'indicator_card_repository_imports.dart';

abstract class IndicatorCardRepository extends CacheableRepository {
  Future<RepoResponse<IndicatorDetailsResponse>> indicatorDetails({
    required String contentType,
    required String id,
    Map<String, String> payload,
  });

  Future<RepoResponse<Map<String, OverView>>> allIndicatorDetailsOverview({
    required List<String> ids,
    required String type,
  });

  Future<RepoResponse<IndicatorStatusResponse>> getIndicatorStatus({required String id});
}
