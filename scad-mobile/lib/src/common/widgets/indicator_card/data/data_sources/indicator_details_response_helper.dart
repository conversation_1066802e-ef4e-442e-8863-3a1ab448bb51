import 'dart:async';

import 'package:scad_mobile/src/common/functions/indicator_date_setting.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/utils/app_utils/string_utils.dart';

class IndicatorDetailsResponseHelper {
  IndicatorDetailsResponseHelper(this.indicatorDetails, {this.overView});

  IndicatorDetailsResponse indicatorDetails;
  final OverView? overView;

  String get chartType => indicatorDetails.indicatorVisualizations!.visualizationsMeta?.firstOrNull?.type ?? '';

  String get title => indicatorDetails.componentTitle ?? '';

  String get domainName => indicatorDetails.domain ?? '';

  String? get value {
    final (String?, String?) orgValue = _originalValue;

    final String? value = orgValue.$1;
    final String? format = orgValue.$2;

    return ShortNumberPipe.transform(
          number: value ?? '',
          angularPipeFormat: format ?? '',
        ) ??
        '';
  }

  String get unit => indicatorDetails.unit ?? '';

  bool get negativeArrow => num.parse(compareValue()) < 0;

  (String?, String?) get _originalValue {
    if (indicatorDetails.type == 'insights-discovery') {
      final defaultVisualization = indicatorDetails.defaultVisualisation ?? '';
      try {
        final valList = indicatorDetails.visualizations!
                .singleWhere((element) => element.id == defaultVisualization)
                .indicatorValues
                ?.valuesMeta ??
            [];

        if (valList.isEmpty) {
          return (null, null);
        }

        return ('${valList.firstOrNull?.value}', '${valList.firstOrNull?.valueFormat}');
      } catch (e, s) {
        Completer<dynamic>().completeError(e, s);
        return (null, null);
      }
    }

    if (overView?.value != null) {
      return ('${overView?.value}', '${overView?.valueFormat}');
    }

    final List<OverviewValuesMeta> valList = indicatorDetails.indicatorValues?.overviewValuesMeta ?? [];
    return (
      '${valList.isEmpty ? null : valList.firstOrNull?.value}',
      '${valList.isEmpty ? null : valList.firstOrNull?.valueFormat}'
    );
  }

  List<JSONObject> getFilteredSeries({
    int seriesMetaIndex = 0,
    String? filterVisualization,
  }) {
    List<JSONObject> filteredList = [];
    if (indicatorDetails.type == 'insights-discovery') {
      final String defaultVisualization = filterVisualization ?? indicatorDetails.defaultVisualisation ?? '';
      final List<JSONObject> list = indicatorDetails.visualizations!
              .singleWhere((element) => element.id == defaultVisualization)
              .indicatorVisualizations
              ?.visualizationsMeta
              ?.firstOrNull
              ?.seriesMeta?[seriesMetaIndex]
              .data ??
          [];

      final filterPanel = indicatorDetails.visualizations!
          .singleWhere(
            (element) => element.id == defaultVisualization,
          )
          .filterPanel;

      filteredList = filterSeries(
        list,
        filterPanel,
      );
    } else {
      final seriesData =
          indicatorDetails.indicatorVisualizations?.visualizationsMeta?.firstOrNull?.seriesMeta![seriesMetaIndex].data;
      final List<JSONObject> list = List.generate(
        seriesData?.length ?? 0,
        (index) => seriesData![index],
      );

      filteredList = filterSeries(list, indicatorDetails.filterPanel);
    }

    return filteredList;
  }

  List<List<JSONObject>> getFilteredSeriesForMultiDrivers({
    int visualizationsMetaIndex = 0,
  }) {
    List<SeriesMeta> list = [];
    List<JSONObject> tempList = [];

    dynamic filter;
    final List<List<JSONObject>> seriesList = [];
    String dbColumn = '';
    if (indicatorDetails.type == 'insights-discovery') {
      final String defaultVisualization = indicatorDetails.defaultVisualisation ?? '';

      final VisualizationsMeta? visualizationsMeta = indicatorDetails.visualizations!
          .singleWhere((element) => element.id == defaultVisualization)
          .indicatorVisualizations
          ?.visualizationsMeta
          ?.firstOrNull;
      list = visualizationsMeta?.seriesMeta ?? [];

      filter =
          indicatorDetails.visualizations!.singleWhere((element) => element.id == defaultVisualization).filterPanel;

      dbColumn = visualizationsMeta?.dbColumn ?? '';
    } else {
      if ((indicatorDetails.indicatorVisualizations?.visualizationsMeta ?? []).isEmpty) {
        list = [];
      } else {
        list = List.generate(
          (indicatorDetails.indicatorVisualizations?.visualizationsMeta?[visualizationsMetaIndex].seriesMeta ?? [])
              .length,
          (index) =>
              indicatorDetails.indicatorVisualizations!.visualizationsMeta![visualizationsMetaIndex].seriesMeta![index],
        );
      }
    }

    for (final SeriesMeta series in list) {
      final String dbColumnValue = series.dbIndicatorId ?? '';

      final List<JSONObject> list = List.generate(series.data?.length ?? 0, (index) => series.data![index]);

      if (dbColumn.isNotEmpty) {
        list.removeWhere((e) => e[dbColumn] != dbColumnValue);
      }

      tempList = filterSeries(list, indicatorDetails.filterPanel ?? filter);
      seriesList.add(tempList);
    }

    return seriesList;
  }

  List<VisualizationsMeta> getFilteredVisualizationMetaList() {
    List<VisualizationsMeta> list = [];
    if (indicatorDetails.type == 'insights-discovery') {
      final String defaultVisualization = indicatorDetails.defaultVisualisation ?? '';
      list = indicatorDetails.visualizations!
              .singleWhere((element) => element.id == defaultVisualization)
              .indicatorVisualizations
              ?.visualizationsMeta ??
          [];
    } else {
      list = indicatorDetails.indicatorVisualizations?.visualizationsMeta ?? [];
    }
    return list;
  }

  List<JSONObject> filterSeries(List<JSONObject> series, dynamic filterPanel) {
    final dataList = List.generate(series.length, (index) => series[index])
      ..removeWhere((element) => element['VALUE'] == 'null')
      ..map((e) => e).toList();

    bool hasFilter = false;
    try {
      hasFilter = bool.parse('${filterPanel ?? 'false'}');
    } catch (e) {
      //  ignore: bool parse error
      hasFilter = true;
    }

    if (hasFilter && filterPanel is FilterPanel) {
      filterPanel.properties = IndicatorDateSetting.removeDuplicates(filterPanel.properties ?? []);
      for (int i = 0; i < (filterPanel.properties! as List).length; i++) {
        final String filterKey = '${filterPanel.properties![i].path}';
        final String filterValue = '${filterPanel.properties![i].defaultVal}';
        if (filterKey != 'OBS_DT') {
          dataList.removeWhere((element) {
            return element[filterKey].toString().toLowerCase() != filterValue.toLowerCase();
          });
        }
      }
    }

    return dataList;
  }

  String compareValue() {
    String compareValue = '0';
    if ((overView?.compareFilters ?? []).isNotEmpty) {
      switch (overView?.compareFilters!.firstOrNull) {
        case 'Y/Y':
          compareValue = '${overView?.yearlyCompareValue ?? 0}';
        case 'Q/Q':
          compareValue = '${overView?.quarterlyCompareValue ?? 0}';
        case 'M/M':
          compareValue = '${overView?.monthlyCompareValue ?? 0}';
      }
    }

    return compareValue;
  }
}
