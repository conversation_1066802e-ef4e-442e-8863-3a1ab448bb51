import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:scad_mobile/demo/demo_api_responses.dart';
import 'package:scad_mobile/main.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/data_sources/indicator_card_api_end_points.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_status_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/services/http_service_impl.dart';
import 'package:scad_mobile/src/utils/hive_utils/hive_keys.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

class IndicatorCardImpl extends IndicatorCardRepository {
  final _httpService = HttpServiceRequests();

  Future<RepoResponse<IndicatorDetailsResponse>> _getAnalyticalAppIndicatorDetails({
    required String id,
    required String contentType,
    required Map<String, String> payload,
  }) async {
    final String endpoint = IndicatorCardApiEndPoints.indicatorDetailsAnalyticalApps(
      id: id,
      contentType: 'analytical-apps',
    );

    final body = {'indicatorDrivers': payload};
    final cacheKey = getCacheKey(endpoint, payload: body);

    return fetchWithCache<IndicatorDetailsResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.postJson(
        endpoint,
        server: ApiServer.ifp,
        jsonPayloadMap: body,
      ),
      parseResult: (json) => IndicatorDetailsResponse.fromJson(json),
    );
  }

  Future<RepoResponse<IndicatorDetailsResponse>> _getNonAnalyticalAppIndicatorDetails({
    required String id,
    required String contentType,
  }) async {
    String cType = contentType;
    const Map<String, String> map = {
      'scad_official_indicator': 'statistics-insights',
      'analytical-apps': 'analytical-apps',
      'analytical_apps': 'analytical_apps',
      'official-insights': 'official-insights',
      'innovative-insights': 'innovative-insights',
      'experimental_statistics': 'innovative-insights',
      'official_statistics': 'statistics-insights',
      'statistics-insights': 'statistics-insights',
      'coi': 'statistics-insights',
    };

    if (map.keys.contains(contentType)) {
      cType = map[contentType]!;
    }

    final String endpoint = cType == 'innovative-insights'
        ? IndicatorCardApiEndPoints.indicatorDetailsInnovativeInsights(
            contentType: cType,
            id: id,
          )
        : IndicatorCardApiEndPoints.indicatorDetails(
            contentType: cType,
            id: id,
          );

    final cacheKey = getCacheKey(endpoint);
    final cache = getCache<JSONObject>(cacheKey);
    if (cache != null) {
      return RepoResponse.success(
        response: IndicatorDetailsResponse.fromJson(cache),
      );
    }

    try {
      final response = await _httpService.get(
        endpoint,
        server: ApiServer.ifp,
      );

      if (response.isSuccess) {
        await setCache(key: cacheKey, value: response.response);
        return RepoResponse<IndicatorDetailsResponse>.success(
          response: IndicatorDetailsResponse.fromJson(response.response),
        );
      } else {
        return RepoResponse<IndicatorDetailsResponse>.error(
          errorMessage: response.message,
          statusCode: response.responseCode,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<IndicatorDetailsResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<IndicatorDetailsResponse>> indicatorDetails({
    required String contentType,
    required String id,
    Map<String, String> payload = const {},
  }) async {
    try {
      if (isDemoMode) {
        return RepoResponse<IndicatorDetailsResponse>.success(
          response: IndicatorDetailsResponse.fromJson(demoIndicatorDetailsResponse(id, contentType)),
        );
      }

      final isAnalyticalApp = contentType == 'analytical-apps' || contentType == 'analytical_apps';
      if (isAnalyticalApp) {
        return _getAnalyticalAppIndicatorDetails(
          id: id,
          contentType: contentType,
          payload: payload,
        );
      }

      return _getNonAnalyticalAppIndicatorDetails(
        id: id,
        contentType: contentType,
      );
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<IndicatorDetailsResponse>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }

  @override
  Future<RepoResponse<IndicatorStatusResponse>> getIndicatorStatus({
    required String id,
  }) async {
    final cacheKey = getStaticCacheKey(HiveKeys.keyIndicatorStatus(id));
    final String endpoint = '${IndicatorCardApiEndPoints.getIndicatorStatus}/$id/';
    return fetchWithCache<IndicatorStatusResponse>(
      cacheKey: cacheKey,
      whenNoCache: () => _httpService.get(endpoint),
      demoApiResponse: () => demoMyAppsResponse,
      parseResult: (json) => IndicatorStatusResponse.fromJson(json),
    );
  }

  @override
  Future<RepoResponse<Map<String, OverView>>> allIndicatorDetailsOverview({
    required List<String> ids,
    required String type,
  }) async {
    try {
      if (isDemoMode) {
        return RepoResponse<Map<String, OverView>>.success(response: {});
      }

      String cType = type;
      const Map<String, String> cTypeMap = {
        'scad_official_indicator': 'official_statistics',
      };
      if (cTypeMap.keys.contains(type)) {
        cType = cTypeMap[type]!;
      }

      final map = <String, OverView>{};

      final cacheKeyPrefix = 'allIndicatorDetailsOverview-$cType';
      final pendingIds = <String>[];
      for (final id in ids) {
        final cacheKey = getCacheKey('$cacheKeyPrefix-$id');
        final cache = getCache<JSONObject>(cacheKey);
        if (cache == null) {
          pendingIds.add(id);
          continue;
        }

        map[id] = OverView.fromJson(cache);
      }

      if (pendingIds.isEmpty) {
        return RepoResponse<Map<String, OverView>>.success(response: map);
      }

      final response = await _httpService.postJson(
        IndicatorCardApiEndPoints.indicatorOverview,
        server: ApiServer.ifp,
        jsonPayloadMap: {
          'ids': pendingIds,
          'type': cType,
        },
      );

      if (response.isSuccess) {
        for (final key in response.response.keys) {
          final json = response.response[key] as Map<String, dynamic>;
          final cacheKey = getCacheKey('$cacheKeyPrefix-$key');
          await setCache(key: cacheKey, value: json);
          map[key] = OverView.fromJson(json);
        }

        return RepoResponse<Map<String, OverView>>.success(response: map);
      } else {
        return RepoResponse<Map<String, OverView>>.error(
          errorMessage: response.message,
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      return RepoResponse<Map<String, OverView>>.error(
        errorMessage: LocaleKeys.somethingWentWrong.tr(),
      );
    }
  }
}
