class IndicatorStatusResponse {
  NodeIdUuid? subscriptions;
  NodeIdUuid? myApps;
  bool? chatThreadExist;

  IndicatorStatusResponse({
    this.subscriptions,
    this.myApps,
    this.chatThreadExist,
  });

  IndicatorStatusResponse.fromJson(Map<String, dynamic> json) {
    subscriptions = (json['subscriptions'] as Map<String,dynamic>?) != null ? NodeIdUuid.fromJson(json['subscriptions'] as Map<String,dynamic>) : null;
    myApps = (json['my_apps'] as Map<String,dynamic>?) != null ? NodeIdUuid.fromJson(json['my_apps'] as Map<String,dynamic>) : null;
    chatThreadExist = json['chat_thread_exist'] as bool?;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['subscriptions'] = subscriptions;
    json['my_apps'] = myApps?.toJson();
    return json;
  }
}

class NodeIdUuid {
  NodeIdUuid({
    this.nodeId,
    this.uuid,
  });

  NodeIdUuid.fromJson(Map<String, dynamic> json) {
    nodeId = json['node_id'] as String?;
    uuid = json['uuid'] as String?;
  }

  String? nodeId;
  String? uuid;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['node_id'] = nodeId;
    json['uuid'] = uuid;
    return json;
  }
}
