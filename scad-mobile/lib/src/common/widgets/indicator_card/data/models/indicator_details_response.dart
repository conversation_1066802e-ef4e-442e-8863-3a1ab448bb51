import 'package:scad_mobile/src/common/types.dart';
import 'package:scad_mobile/src/features/details_page/base/mixin/indicator_filter_adapter_mixin.dart';

part 'dependency_compare_util.dart';

class IndicatorDetailsResponse {
  IndicatorDetailsResponse({
    this.id,
    this.componentTitle,
    this.componentSubtitle,
    // this.pageIcon,
    // this.pageLightIcon,
    // this.domains,
    this.domain,
    this.defaultVisualisation,
    this.theme,
    this.subtheme,
    // this.product,
    this.type,
    // this.note,
    // this.narrative,
    // this.searchTags,
    // this.attachment,
    // this.indicator,
    // this.policyGuide,
    // this.enableCompare,
    // this.compareData,
    // this.insights,
    // this.enablePointToggle,
    // this.maxPointLimit,
    // this.minLimitYAxis,
    // this.enableVisualizationTypes,
    // this.enableUploadAndCompare,
    // this.showInsights,
    this.contentClassification,
    this.contentClassificationKey,
    this.domainId,
    // this.themeId,
    // this.subthemeId,
    // this.productId,
    this.tagName,
    this.overView,
    this.dataSource,
    this.publicationDate,
    this.updated,
    // this.language,
    // this.indicatorTools,
    this.indicatorFilters,
    this.indicatorDrivers,
    this.indicatorValues,
    this.indicatorVisualizations,
    // this.isUploadCompareData,
    this.metaData,
    this.unit,
    // this.viewName,
    this.indicatorId,
    this.isMultiDimension,
    this.filterPanel,
    this.tableFields,
    this.visualizations,
    this.multiDrivers = false,
    this.indicatorType,
    this.message,
    this.status,
    // this.showValue,
    this.security,
  });

  IndicatorDetailsResponse.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    componentTitle =
        (json['component_title'] as String?)?.replaceAll('&quot;', '"');
    componentSubtitle = json['component_subtitle'] as String?;
    // pageIcon = json['page_icon'] as String?;
    // pageLightIcon = json['page_light_icon'] as String?;
    // domains =
    //     (json['domains'] as List?)?.map((dynamic e) => e as String).toList();
    domain = json['domain'] as String?;
    defaultVisualisation = json['default_visualisation'] as String?;
    theme = json['theme'] as String?;
    subtheme = json['subtheme'] as String?;
    // product = json['product'] as String?;
    type = json['type'] as String?;
    // note = json['note'] as String?;
    // narrative = json['narrative'] as String?;
    // searchTags = (json['search_tags'] as List?)
    //     ?.map((dynamic e) => e as String)
    //     .toList();
    // attachment = json['attachment'] as String?;
    // indicator = json['Indicator'] as String?;
    // policyGuide = json['policy_guide'] as String?;
    // // enableCompare = json['enableCompare'] as bool?;
    // compareData = json['compare_data'] as String?;
    // insights = json['insights'] as String?;
    // enablePointToggle = json['enablePointToggle'];
    // maxPointLimit = json['maxPointLimit'] as String?;
    // minLimitYAxis = json['minLimitYAxis'] as String?;
    // enableVisualizationTypes = json['enableVisualizationTypes'] as bool?;
    // enableUploadAndCompare = json['enableUploadAndCompare'] as bool?;
    // showInsights = json['showInsights'] as String?;
    contentClassification = json['content_classification'] as String?;
    contentClassificationKey = json['content_classification_key'] as String?;
    domainId = json['domain_id'].toString();
    // if (json['theme_id'] !=null) {
    //   themeId = json['theme_id'].toString();
    // } else {
    //   themeId = null;
    // }
    // subthemeId = json['subtheme_id'] == null?null:json['subtheme_id'].toString();
    // productId = json['product_id'] == null?null:json['product_id'].toString();
    tagName = json['tagName'] as String?;
    overView = (json['overView'] as Map<String, dynamic>?) != null
        ? OverView.fromJson(json['overView'] as Map<String, dynamic>)
        : null;
    dataSource = json['data_source'] as String?;
    publicationDate = json['publication_date'] as String?;
    updated = json['updated'] as String?;
    // language = json['language'] as String?;
    // indicatorTools = (json['indicatorTools'] as List?)
    //     ?.map((dynamic e) => IndicatorTools.fromJson(e as Map<String, dynamic>))
    //     .toList();
    indicatorFilters = (json['indicatorFilters'] as List?)
        ?.map(
          (dynamic e) => IndicatorFilters.fromJson(e as Map<String, dynamic>),
        )
        .toList();
    indicatorDrivers = (json['indicatorDrivers'] as List?)
        ?.map(
          (dynamic e) => IndicatorDriver.fromJson(e as Map<String, dynamic>),
        )
        .toList();
    indicatorValues = json['indicatorValues'] is List
        ? null
        : (json['indicatorValues'] as Map<dynamic, dynamic>?) != null
            ? IndicatorValues.fromJson(
                json['indicatorValues'] as Map<dynamic, dynamic>,
              )
            : null;
    indicatorVisualizations =
        (json['indicatorVisualizations'] as Map<String, dynamic>?) != null
            ? IndicatorVisualizations.fromJson(
                json['indicatorVisualizations'] as Map<String, dynamic>,
              )
            : null;
    // isUploadCompareData = json['isUploadCompareData'] as bool?;
    if (json['metaData'] is List<dynamic>) {
      final List<MetaData?>? value = (json['metaData'] as List?)
          ?.map((dynamic e) => MetaData.fromJson(e as Map<String, dynamic>))
          .toList();
      value?.removeWhere((value) => (value?.value ?? '').isEmpty);
      metaData = value;
    } else {
      final List<MetaData?> value = [
        if (json['metaData'] == null)
          null
        else
          MetaData.fromJson(json['metaData'] as Map<String, dynamic>)
      ]..removeWhere((v) => v == null);
      metaData = json['metaData'] == null ? [] : value as List<MetaData?>?;
    }
    unit = json['unit'] as String?;
    // viewName = json['viewName'] as String?;
    indicatorId = json['indicatorId'] as String?;
    isMultiDimension = json['isMultiDimension'] as bool?;
    filterPanel = (json['filterPanel'] is bool?)
        ? (json['filterPanel'] as bool?)
        : (json['filterPanel'] as Map<String, dynamic>?) != null
            ? FilterPanel.fromJson(json['filterPanel'] as Map<String, dynamic>)
            : null;
    tableFields = (json['tableFields'] as List?)
        ?.map((dynamic e) => TableFields.fromJson(e as Map<String, dynamic>))
        .toList();
    visualizations = (json['visualizations'] as List?)
        ?.map((dynamic e) => Visualizations.fromJson(e as Map<String, dynamic>))
        .toList();
    multiDrivers = json['multiDrivers'] as bool?;
    indicatorType = json['indicatorType'] as String?;
    message = json['message'] as String?;
    status = json['status'] as String?;
    // showValue= json['showValue'] as bool?;
    security = (json['security'] as Map<String,dynamic>?) != null
        ? Security.fromJson(json['security'] as Map<String,dynamic>)
        : null;
  }

  String? id;
  String? componentTitle;
  String? componentSubtitle;
  // String? pageIcon;
  // String? pageLightIcon;
  // List<String>? domains;
  String? domain;
  String? defaultVisualisation;
  String? theme;
  String? subtheme;
  // String? product;
  String? type;
  // String? note;
  // String? narrative;
  // List<String>? searchTags;
  // String? attachment;
  // String? indicator;
  // String? policyGuide;
  // // bool? enableCompare;
  // String? compareData;
  // String? insights;
  // dynamic enablePointToggle;
  // String? maxPointLimit;
  // String? minLimitYAxis;
  // bool? enableVisualizationTypes;
  // bool? enableUploadAndCompare;
  // String? showInsights;
  String? contentClassification;
  String? contentClassificationKey;
  String? domainId;
  // String? themeId;
  // String? subthemeId;
  // String? productId;
  String? tagName;
  OverView? overView;
  String? dataSource;
  String? publicationDate;
  String? updated;
  // String? language;
  // List<IndicatorTools>? indicatorTools;
  List<IndicatorFilters>? indicatorFilters;
  List<IndicatorDriver>? indicatorDrivers;
  IndicatorValues? indicatorValues;
  IndicatorVisualizations? indicatorVisualizations;
  // bool? isUploadCompareData;
  List<MetaData?>? metaData;
  String? unit;
  // String? viewName;
  String? indicatorId;
  bool? isMultiDimension;
  dynamic filterPanel;
  List<TableFields>? tableFields;
  List<Visualizations>? visualizations;
  bool? multiDrivers;
  String? indicatorType;
  String? message;
  String? status;
  // bool? showValue;
  Security? security;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['component_title'] = componentTitle;
    json['component_subtitle'] = componentSubtitle;
    // json['page_icon'] = pageIcon;
    // json['page_light_icon'] = pageLightIcon;
    // json['domains'] = domains;
    json['domain'] = domain;
    json['default_visualisation'] = defaultVisualisation;
    json['theme'] = theme;
    json['subtheme'] = subtheme;
    // json['product'] = product;
    json['type'] = type;
    // json['note'] = note;
    // json['narrative'] = narrative;
    // json['search_tags'] = searchTags;
    // json['attachment'] = attachment;
    // json['Indicator'] = indicator;
    // json['policy_guide'] = policyGuide;
    // // json['enableCompare'] = enableCompare;
    // json['compare_data'] = compareData;
    // json['insights'] = insights;
    // json['enablePointToggle'] = enablePointToggle;
    // json['maxPointLimit'] = maxPointLimit;
    // json['minLimitYAxis'] = minLimitYAxis;
    // json['enableVisualizationTypes'] = enableVisualizationTypes;
    // json['enableUploadAndCompare'] = enableUploadAndCompare;
    // json['showInsights'] = showInsights;
    json['content_classification'] = contentClassification;
    json['content_classification_key'] = contentClassificationKey;
    json['domain_id'] = domainId;
    // json['theme_id'] = themeId;
    // json['subtheme_id'] = subthemeId;
    // json['product_id'] = productId;
    json['tagName'] = tagName;
    json['overView'] = overView?.toJson();
    json['data_source'] = dataSource;
    json['publication_date'] = publicationDate;
    json['updated'] = updated;
    // json['language'] = language;
    // json['indicatorTools'] = indicatorTools?.map((e) => e.toJson()).toList();
    json['indicatorFilters'] =
        indicatorFilters?.map((e) => e.toJson()).toList();
    json['indicatorDrivers'] = indicatorDrivers;
    json['indicatorValues'] = indicatorValues?.toJson();
    json['indicatorVisualizations'] = indicatorVisualizations?.toJson();
    // json['isUploadCompareData'] = isUploadCompareData;
    json['metaData'] = metaData?.map((e) => e?.toJson()).toList() ;
    json['unit'] = unit;
    // json['viewName'] = viewName;
    json['indicatorId'] = indicatorId;
    json['isMultiDimension'] = isMultiDimension;
    json['filterPanel'] = filterPanel;
    json['tableFields'] = tableFields?.map((e) => e.toJson()).toList();
    json['visualizations'] = visualizations?.map((e) => e.toJson()).toList();
    json['multiDrivers'] = multiDrivers;
    json['indicatorType'] = indicatorType;
    json['message'] = message;
    json['status'] = status;
    json['security'] = security?.toJson();
    // json['showValue'] = showValue;
    return json;
  }
}

class OverView {
  OverView({
    this.id,
    this.compareFilters,
    this.valueFormat,
    // this.templateFormat,
    // this.baseDate,
    this.value,
    this.yearlyCompareValue,
    this.yearlyChangeValue,
    this.quarterlyCompareValue,
    this.quarterlyChangeValue,
    this.monthlyCompareValue,
    this.monthlyChangeValue,
  });

  OverView.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    compareFilters = (json['compareFilters'] as List?)
        ?.map((dynamic e) => e as String)
        .toList();
    valueFormat = json['valueFormat'] as String?;
    // templateFormat = json['templateFormat'] as String?;
    // baseDate = json['baseDate'] as String?;
    value = json['value'];
    yearlyCompareValue = json['yearlyCompareValue'];
    yearlyChangeValue = json['yearlyChangeValue'];
    quarterlyCompareValue = json['quarterlyCompareValue'];
    quarterlyChangeValue = json['quarterlyChangeValue'];
    monthlyCompareValue = json['monthlyCompareValue'];
    monthlyChangeValue = json['monthlyChangeValue'];
  }
  String? id;
  List<String>? compareFilters;
  String? valueFormat;
  // String? templateFormat;
  // String? baseDate;
  dynamic value;
  dynamic yearlyCompareValue;
  dynamic yearlyChangeValue;
  dynamic quarterlyCompareValue;
  dynamic quarterlyChangeValue;
  dynamic monthlyCompareValue;
  dynamic monthlyChangeValue;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['compareFilters'] = compareFilters;
    json['valueFormat'] = valueFormat;
    // json['templateFormat'] = templateFormat;
    // json['baseDate'] = baseDate;
    json['value'] = value;
    json['yearlyCompareValue'] = yearlyCompareValue;
    json['yearlyChangeValue'] = yearlyChangeValue;
    json['quarterlyCompareValue'] = quarterlyCompareValue;
    json['quarterlyChangeValue'] = quarterlyChangeValue;
    json['monthlyCompareValue'] = monthlyCompareValue;
    json['monthlyChangeValue'] = monthlyChangeValue;
    return json;
  }
}

// class IndicatorTools {
//   IndicatorTools({
//     this.id,
//     this.disabled,
//     this.label,
//   });
//
//   IndicatorTools.fromJson(Map<String, dynamic> json) {
//     id = json['id'] as String?;
//     disabled = json['disabled'] as bool?;
//     label = json['label'] as String?;
//   }
//   String? id;
//   bool? disabled;
//   String? label;
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> json = <String, dynamic>{};
//     json['id'] = id;
//     json['disabled'] = disabled;
//     json['label'] = label;
//     return json;
//   }
// }

class IndicatorFilters {
  IndicatorFilters({
    this.id,
    this.options,
  });

  IndicatorFilters.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    options = (json['options'] as List?)
        ?.map((dynamic e) => Options.fromJson(e as Map<String, dynamic>))
        .toList();
  }
  String? id;
  List<Options>? options;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['options'] = options?.map((e) => e.toJson()).toList();
    return json;
  }
}

class IndicatorDriver {
  IndicatorDriver({
    this.title,
    // this.type,
    this.id,
    // this.subtitle,
    this.options,
    this.viewOnly
  });

  IndicatorDriver.fromJson(Map<String, dynamic> json) :
      title = json['title'] as String?,
      // type = json['type'] as String?,
        id = json['id'] as String?,
        // subtitle = json['subtitle'] as String?,
        viewOnly= json['viewOnly'] as bool?,
        options = (json['options'] as List?)
            ?.map(
              (dynamic e) => DriverOptions.fromJson(e as Map<String, dynamic>),
            )
            .toList();
  final String? title;
  // final String? type;
  final String? id;
  // final String? subtitle;
  final bool?  viewOnly;
  final List<DriverOptions>? options;

  Map<String, dynamic> toJson() => {
        // 'title': title,
        // 'type': type,
        'id': id,
        // 'subtitle': subtitle,
        'options': options?.map((e) => e.toJson()).toList(),
        'viewOnly': viewOnly
      };
}

class DriverOptions {
  DriverOptions({
    this.label,
    this.value,
    this.isSelected,
  });
  DriverOptions.fromJson(Map<String, dynamic> json)
      : label = json['label'] as String?,
        value = json['value'] as String?,
        isSelected = json['isSelected'] as bool?;

  String? label;
  final String? value;
  final bool? isSelected;

  Map<String, dynamic> toJson() =>
      {'label': label, 'value': value, 'isSelected': isSelected};
}

class Options {
  Options({
    this.id,
    this.label,
    this.unit,
    this.value,
    this.isSelected = false,
  });

  Options.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    label = json['label'] as String?;
    unit = json['unit'] as String?;
    value = json['value'] as int?;
  }
  String? id;
  String? label;
  String? unit;
  int? value;
  bool isSelected = false;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['label'] = label;
    json['unit'] = unit;
    json['value'] = value;
    return json;
  }
}

class IndicatorValues {
  IndicatorValues({
    this.overviewValuesMeta,
    this.multiValuesMeta,
    this.valuesMeta,
  });

  IndicatorValues.fromJson(Map<dynamic, dynamic> json) {
    // if (json['valuesMeta'] != null) {
    //   overviewValuesMeta = (json['valuesMeta'] as List?)
    //       ?.map(
    //         (dynamic e) =>
    //             OverviewValuesMeta.fromJson(e as Map<String, dynamic>),
    //       )
    //       .toList();
    // } else
      if (json['overviewValueMeta'] != null) {
      overviewValuesMeta = (json['overviewValueMeta'] as List?)
          ?.map(
            (dynamic e) =>
                OverviewValuesMeta.fromJson(e as Map<String, dynamic>),
          )
          .toList();
    } else
      if (json['overviewValuesMeta'] != null) {
      overviewValuesMeta = (json['overviewValuesMeta'] as List?)
          ?.map(
            (dynamic e) =>
                OverviewValuesMeta.fromJson(e as Map<String, dynamic>),
          )
          .toList();
    }
      multiValuesMeta = (json['multiValuesMeta'] as List?)
        ?.map(
          (list) => (list as List)
              .map(
                (map) =>
                    OverviewValuesMeta.fromJson(map as Map<String, dynamic>),
              )
              .toList(),
        )
        .toList();
    valuesMeta = (json['valuesMeta'] as List?)
        ?.map(
          (dynamic e) => OverviewValuesMeta.fromJson(e as Map<String, dynamic>),
        )
        .toList();
  }
  List<OverviewValuesMeta>? overviewValuesMeta;
  List<List<OverviewValuesMeta>>? multiValuesMeta;
  List<OverviewValuesMeta>? valuesMeta;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['overviewValuesMeta'] =
        overviewValuesMeta?.map((e) => e.toJson()).toList();
    json['multiValuesMeta'] = multiValuesMeta?.map(
      (list) => list.map(
        (e) => e.toJson(),
      ).toList(),
    ).toList();
    json['valuesMeta'] = valuesMeta?.map((e) => e.toJson()).toList();
    return json;
  }
}

class OverviewValuesMeta {
  OverviewValuesMeta({
    this.id,
    this.type,
    this.valueFormat,
    this.templateFormat,
    // this.viewName,
    // this.comboIdTable,
    // this.dimension,
    // this.dateFormat,
    // this.dateStart,
    this.value,
    this.title,
    // this.compareFilters,
    // this.color,
    this.aggregation,
    this.values,
    this.jsonValue,
    this.invertColor,
    this.invertArrow,
  });

  OverviewValuesMeta.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    type = json['type'] as String?;
    valueFormat = json['valueFormat'] as String?;
    templateFormat = json['templateFormat'] as String?;
    // viewName = json['viewName'] as String?;
    // comboIdTable = json['comboIdTable'] as String?;
    // dimension = (json['dimension'] as Map<String, dynamic>?) != null
    //     ? Dimension.fromJson(json['dimension'] as Map<String, dynamic>)
    //     : null;
    // dateFormat = json['dateFormat'] as String?;
    // dateStart = json['dateStart'] as String?;
    value = json['value']?.toString();
    invertColor = json['invertColor'] as bool?;
    invertArrow = json['invertArrow'] as bool?;
    title = json['title'] as String?;
    headerTitle = json['header_title'] as String?;
    // compareFilters = json['compareFilters'];
    // color = json['color'] as String?;
    aggregation = json['aggregation'] as String?;
    values = (json['values'] as List?)?.map((dynamic e) => Values.fromJson(e as Map<String,dynamic>)).toList();
    jsonValue = json;
  }
  String? id;
  String? type;
  String? valueFormat;
  String? templateFormat;
  // String? viewName;
  // String? comboIdTable;
  // Dimension? dimension;
  // String? dateFormat;
  // String? dateStart;
  String? value;
  bool? invertColor;
  bool? invertArrow;
  String? title;
  String? headerTitle;
  // dynamic compareFilters;
  // String? color;
  String? aggregation;
  List<Values>? values;
  Map<String, dynamic>? jsonValue;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['type'] = type;
    json['valueFormat'] = valueFormat;
    json['templateFormat'] = templateFormat;
    // json['viewName'] = viewName;
    // json['comboIdTable'] = comboIdTable;
    // json['dimension'] = dimension?.toJson();
    // json['dateFormat'] = dateFormat;
    // json['dateStart'] = dateStart;
    json['value'] = value;
    json['invertArrow'] = invertArrow;
    json['invertColor'] = invertColor;
    json['title'] = title;
    json['header_title'] = headerTitle;
    // json['compareFilters'] = compareFilters;
    // json['color'] = color;
    json['aggregation'] = aggregation;
    json['values'] = values?.map((e) => e.toJson()).toList();
    json['jsonValue'] = jsonValue;

    return json;
  }
}

// class Dimension {
//   Dimension({
//     this.tYPE,
//     this.sECTOR,
//     this.oILNONOIL,
//     this.iNDUSTRY,
//     this.iNDICATORID,
//     this.cOUNTRYREGION,
//     this.oWNERTYPE,
//   });
//
//   Dimension.fromJson(Map<String, dynamic> json) {
//     tYPE = json['TYPE'] as String?;
//     sECTOR = json['SECTOR'] as String?;
//     oILNONOIL = json['OIL_NONOIL'] as String?;
//     iNDUSTRY = json['INDUSTRY'] as String?;
//     iNDICATORID = json['INDICATOR_ID'] is List
//         ? json['INDICATOR_ID'] as List<dynamic>
//         : json['INDICATOR_ID'] as String?;
//     cOUNTRYREGION = json['COUNTRY_REGION'] as String?;
//     oWNERTYPE = json['OWNER_TYPE'] as String?;
//   }
//   String? tYPE;
//   String? sECTOR;
//   String? oILNONOIL;
//   String? iNDUSTRY;
//   dynamic iNDICATORID;
//   String? cOUNTRYREGION;
//   String? oWNERTYPE;
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> json = <String, dynamic>{};
//     json['TYPE'] = tYPE;
//     json['SECTOR'] = sECTOR;
//     json['OIL_NONOIL'] = oILNONOIL;
//     json['INDUSTRY'] = iNDUSTRY;
//     json['INDICATOR_ID'] = iNDICATORID;
//     json['COUNTRY_REGION'] = cOUNTRYREGION;
//     json['OWNER_TYPE'] = oWNERTYPE;
//     return json;
//   }
// }

class Values {

  Values({
    this.value,
    this.type,
    this.aggregation,
    // this.date,
    // this.dimension,
  });

  Values.fromJson(Map<String, dynamic> json)
      : value = json['value']?.toString(),
        type = json['type'] as String?,
        aggregation = json['aggregation'] as String?;//,
        // date = json['date'] as String?,
        // dimension = json['dimension'] as String?;
  final String? value;
  final String? type;
  final String? aggregation;
  // final String? date;
  // final String? dimension;

  Map<String, dynamic> toJson() => {
    'value' : value,
    'type' : type,
    'aggregation' : aggregation,
    // 'date' : date,
    // 'dimension' : dimension,
  };
}

class IndicatorVisualizations {
  IndicatorVisualizations({
    this.visualizationsMeta,
    this.visualizationDefault,
  });

  IndicatorVisualizations.fromJson(Map<String, dynamic> json) {
    visualizationsMeta = ((json['visualizationsMeta'] as List?)
          ?..removeWhere((element) => element == null))
        ?.map(
          (dynamic e) => VisualizationsMeta.fromJson(e as Map<String, dynamic>),
        )
        .toList();
    visualizationDefault = json['visualizationDefault'] as String?;
  }
  List<VisualizationsMeta>? visualizationsMeta;
  String? visualizationDefault;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['visualizationsMeta'] =
        visualizationsMeta?.map((e) => e.toJson()).toList();
    json['visualizationDefault'] = visualizationDefault;
    return json;
  }
}

class VisualizationsMeta {
  VisualizationsMeta({
    this.id,
    this.type,
    this.seriesMeta,
    this.seriesTitles,
    this.yAxisLabel,
    this.viewName,
    this.dbColumn,
    this.timeUnit,
    this.yearlyData,
    this.vizLabel,
  });

  VisualizationsMeta.fromJson(JSONObject json) {
    id = json['id'] as String?;
    type = json['type'] as String?;
    seriesMeta = (json['seriesMeta'] as List?)
        ?.map(
          (e) => SeriesMeta.fromJson(e as JSONObject),
        )
        .toList();
    seriesTitles = json['seriesTitles'] as JSONObject?;
    axisValues = json['axisValues'] as JSONObject?;
    yAxisLabel = json['yAxisLabel'] as String?;
    viewName = json['viewName'] as String?;
    dbColumn = json['dbColumn'] as String?;
    timeUnit = (json['timeUnit'] as List?)?.map((e) => e as String).toList();
    yearlyData = (json['yearlyData'] as JSONObject?) != null
        ? YearlyData.fromJson(json['yearlyData'] as JSONObject)
        : null;
    showQuarterlyIntervals = json['showQuarterlyIntervals'] as bool?;
    vizLabel = json['vizLabel'] as String?;
  }

  String? id;
  String? type;
  List<SeriesMeta>? seriesMeta;
  JSONObject? seriesTitles;
  JSONObject? axisValues;
  String? yAxisLabel;
  String? viewName;
  String? dbColumn;
  List<String>? timeUnit;
  YearlyData? yearlyData;
  dynamic showQuarterlyIntervals;
  String? vizLabel;

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    json['id'] = id;
    json['type'] = type;
    json['seriesMeta'] = seriesMeta?.map((e) => e.toJson()).toList();
    json['seriesTitles'] = seriesTitles;
    json['axisValues'] = axisValues;
    json['yAxisLabel'] = yAxisLabel;
    json['viewName'] = viewName;
    json['dbColumn'] = dbColumn;
    json['timeUnit'] = timeUnit;
    json['showQuarterlyIntervals'] = showQuarterlyIntervals;
    json['vizLabel'] = vizLabel;
    return json;
  }
}

class SeriesMeta {
  SeriesMeta({
    this.type,
    // this.xAccessor,
    // this.yAccessor,
    this.id,
    this.dbIndicatorId,
    this.label,
    this.color,
    this.data,
    // this.yMax,
    // this.yMin,
    // this.xMin,
    this.xMax,
    // this.tableDefinition,
  });

  SeriesMeta.fromJson(Map<String, dynamic> json) {
    type = json['type'] as String?;
    // xAccessor = (json['xAccessor'] as Map<String, dynamic>?) != null
    //     ? XAccessor.fromJson(json['xAccessor'] as Map<String, dynamic>)
    //     : null;
    // yAccessor = (json['yAccessor'] as Map<String, dynamic>?) != null
    //     ? YAccessor.fromJson(json['yAccessor'] as Map<String, dynamic>)
    //     : null;
    id = json['id'] as String?;
    dbIndicatorId = json['dbIndicatorId'] as String?;
    label = json['label'];
    color = json['color'] as String?;
    data = (json['data'] as List?)
        ?.map((dynamic e) => e as Map<String, dynamic>)
        .toList();
    // yMax = json['yMax'].toString();
    // yMin = json['yMin'].toString();
    // xMin = json['xMin'] as String?;
    xMax = json['xMax'] as String?;
    // tableDefinition = (json['tableDefinition'] as List?)
    //     ?.map(
    //       (dynamic e) => TableDefinition.fromJson(e as Map<String, dynamic>),
    //     )
    //     .toList();
  }
  String? type;
  // XAccessor? xAccessor;
  // YAccessor? yAccessor;
  String? id;
  String? dbIndicatorId;
  dynamic label;
  String? color;
  List<Map<String, dynamic>>? data;
  // String? yMax;
  // String? yMin;
  // String? xMin;
  String? xMax;
  // List<TableDefinition>? tableDefinition;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['type'] = type;
    // json['xAccessor'] = xAccessor?.toJson();
    // json['yAccessor'] = yAccessor?.toJson();
    json['id'] = id;
    json['dbIndicatorId'] = dbIndicatorId;
    json['label'] = label;
    json['color'] = color;
    json['data'] = data?.map((e) => e).toList();
    // json['yMax'] = yMax;
    // json['yMin'] = yMin;
    // json['xMin'] = xMin;
    json['xMax'] = xMax;
    // json['tableDefinition'] = tableDefinition?.map((e) => e.toJson()).toList();
    return json;
  }
}

// class XAccessor {
//   XAccessor({
//     this.type,
//     this.path,
//     this.specifier,
//   });
//
//   XAccessor.fromJson(Map<String, dynamic> json) {
//     type = json['type'] as String?;
//     path = json['path'] as String?;
//     specifier = json['specifier'] as String?;
//   }
//   String? type;
//   String? path;
//   String? specifier;
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> json = <String, dynamic>{};
//     json['type'] = type;
//     json['path'] = path;
//     json['specifier'] = specifier;
//     return json;
//   }
// }

// class YAccessor {
//   YAccessor({
//     this.type,
//     this.path,
//   });
//
//   YAccessor.fromJson(Map<String, dynamic> json) {
//     type = json['type'] as String?;
//     path = json['path'] as String?;
//   }
//   String? type;
//   String? path;
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> json = <String, dynamic>{};
//     json['type'] = type;
//     json['path'] = path;
//     return json;
//   }
// }

class MetaData {
  MetaData({
    this.label,
    this.value,
  });

  MetaData.fromJson(Map<String, dynamic> json) {
    label = json['label'] as String?;
    value = json['value'] as String?;
    if ((value ?? '').isEmpty) {
      value = null;
    }
  }
  String? label;
  String? value;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['label'] = label;
    json['value'] = value;
    return json;
  }
}

class TableFields {
  TableFields({
    this.label,
    this.path,
  });

  TableFields.fromJson(Map<String, dynamic> json) {
    label = json['label'] as String?;
    path = json['path'] as String?;
  }
  String? label;
  String? path;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['label'] = label;
    json['path'] = path;
    return json;
  }
}

class Visualizations {
  Visualizations({
    this.id,
    this.componentTitle,
    // this.componentSubtitle,
    // this.pageIcon,
    // this.pageLightIcon,
    // this.domains,
    // this.domain,
    // this.theme,
    // this.subtheme,
    // this.product,
    // this.type,
    // this.note,
    // this.narrative,
    // this.searchTags,
    // this.attachment,
    // this.indicator,
    // this.policyGuide,
    // this.enableCompare,
    // this.compareData,
    // this.insights,
    // this.enablePointToggle,
    // this.maxPointLimit,
    // this.minLimitYAxis,
    // this.enableVisualizationTypes,
    // this.enableUploadAndCompare,
    // this.showInsights,
    // this.contentClassification,
    // this.contentClassificationKey,
    // this.domainId,
    // this.themeId,
    // this.subthemeId,
    // this.productId,
    // this.tagName,
    // this.dataSource,
    // this.publicationDate,
    // this.updated,
    // this.language,
    // this.indicatorTools,
    // this.indicatorActions,
    // this.indicatorDrivers,
    // this.indicatorFilters,
    this.indicatorValues,
    this.filterPanel,
    // this.sortVisualizations,
    this.indicatorVisualizations,
    // this.benchmarks,
    // this.isUploadCompareData,
    this.tableFields,
    // this.updatedDateFromDB,
    // this.sortOrder,
  });

  Visualizations.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    componentTitle = json['component_title'] as String?;
    // componentSubtitle = json['component_subtitle'] as String?;
    // pageIcon = json['page_icon'] as String?;
    // pageLightIcon = json['page_light_icon'] as String?;
    // domains =
    //     (json['domains'] as List?)?.map((dynamic e) => e as String).toList();
    // domain = json['domain'] as String?;
    // theme = json['theme'] as String?;
    // subtheme = json['subtheme'] as String?;
    // product = json['product'] as String?;
    // type = json['type'] as String?;
    // note = json['note'] as String?;
    // narrative = json['narrative'] as String?;
    // searchTags = json['search_tags'] as List?;
    // attachment = json['attachment'] as String?;
    // indicator = json['Indicator'] as String?;
    // policyGuide = json['policy_guide'] as String?;
    // enableCompare = json['enableCompare'] as bool?;
    // compareData = json['compare_data'] as String?;
    // insights = json['insights'] as String?;
    // enablePointToggle = json['enablePointToggle'];
    // maxPointLimit = json['maxPointLimit'] as String?;
    // minLimitYAxis = json['minLimitYAxis'] as String?;
    // enableVisualizationTypes = json['enableVisualizationTypes'] as bool?;
    // enableUploadAndCompare = json['enableUploadAndCompare'] as bool?;
    // showInsights = json['showInsights'] as String?;
    // contentClassification = json['content_classification'] as String?;
    // contentClassificationKey = json['content_classification_key'] as String?;
    // domainId = json['domain_id'] == null?null:json['domain_id'].toString();
    // themeId = json['theme_id'];
    // subthemeId = json['subtheme_id'];
    // productId = json['product_id'];
    // tagName = json['tagName'] as String?;
    // dataSource = json['data_source'] as String?;
    // publicationDate = json['publication_date'] as String?;
    // updated = json['updated'] as String?;
    // language = json['language'] as String?;
    // indicatorTools = json['indicatorTools'] as List?;
    // indicatorActions = (json['indicatorActions'] as List?)
    //     ?.map(
    //       (dynamic e) => IndicatorActions.fromJson(e as Map<String, dynamic>),
    //     )
    //     .toList();
    // indicatorDrivers = json['indicatorDrivers'] as List?;
    // indicatorFilters = json['indicatorFilters'] as List?;
    indicatorValues = (json['indicatorValues'] as Map<String, dynamic>?) != null
        ? IndicatorValues.fromJson(
            json['indicatorValues'] as Map<String, dynamic>,
          )
        : null;
    filterPanel = (json['filterPanel']) != null
        ? FilterPanel.fromJson(json['filterPanel'] as Map<String, dynamic>)
        : null;
    // sortVisualizations = json['sortVisualizations'] as bool?;
    indicatorVisualizations =
        (json['indicatorVisualizations'] as Map<String, dynamic>?) != null
            ? IndicatorVisualizations.fromJson(
                json['indicatorVisualizations'] as Map<String, dynamic>,
              )
            : null;
    // benchmarks = json['benchmarks'] as List?;
    // isUploadCompareData = json['isUploadCompareData'] as bool?;
    tableFields = (json['tableFields'] as List?)
        ?.map((dynamic e) => TableFields.fromJson(e as Map<String, dynamic>))
        .toList();
    // updatedDateFromDB = json['updatedDateFromDB'] as bool?;
    // sortOrder = json['sortOrder'] as int?;
  }
  String? id;
  String? componentTitle;
  // String? componentSubtitle;
  // String? pageIcon;
  // String? pageLightIcon;
  // List<String>? domains;
  // String? domain;
  // String? theme;
  // String? subtheme;
  // String? product;
  // String? type;
  // String? note;
  // String? narrative;
  // List<dynamic>? searchTags;
  // String? attachment;
  // String? indicator;
  // String? policyGuide;
  // bool? enableCompare;
  // String? compareData;
  // String? insights;
  // dynamic enablePointToggle;
  // String? maxPointLimit;
  // String? minLimitYAxis;
  // bool? enableVisualizationTypes;
  // bool? enableUploadAndCompare;
  // String? showInsights;
  // String? contentClassification;
  // String? contentClassificationKey;
  // String? domainId;
  // dynamic themeId;
  // dynamic subthemeId;
  // dynamic productId;
  // String? tagName;
  // String? dataSource;
  // String? publicationDate;
  // String? updated;
  // String? language;
  // List<dynamic>? indicatorTools;
  // List<IndicatorActions>? indicatorActions;
  // List<dynamic>? indicatorDrivers;
  // List<dynamic>? indicatorFilters;
  IndicatorValues? indicatorValues;
  FilterPanel? filterPanel;
  // bool? sortVisualizations;
  IndicatorVisualizations? indicatorVisualizations;
  // List<dynamic>? benchmarks;
  // bool? isUploadCompareData;
  List<TableFields>? tableFields;
  // bool? updatedDateFromDB;
  // int? sortOrder;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['component_title'] = componentTitle;
    // json['component_subtitle'] = componentSubtitle;
    // json['page_icon'] = pageIcon;
    // json['page_light_icon'] = pageLightIcon;
    // json['domains'] = domains;
    // json['domain'] = domain;
    // json['theme'] = theme;
    // json['subtheme'] = subtheme;
    // json['product'] = product;
    // json['type'] = type;
    // json['note'] = note;
    // json['narrative'] = narrative;
    // json['search_tags'] = searchTags;
    // json['attachment'] = attachment;
    // json['Indicator'] = indicator;
    // json['policy_guide'] = policyGuide;
    // json['enableCompare'] = enableCompare;
    // json['compare_data'] = compareData;
    // json['insights'] = insights;
    // json['enablePointToggle'] = enablePointToggle;
    // json['maxPointLimit'] = maxPointLimit;
    // json['minLimitYAxis'] = minLimitYAxis;
    // json['enableVisualizationTypes'] = enableVisualizationTypes;
    // json['enableUploadAndCompare'] = enableUploadAndCompare;
    // json['showInsights'] = showInsights;
    // json['content_classification'] = contentClassification;
    // json['content_classification_key'] = contentClassificationKey;
    // json['domain_id'] = domainId;
    // json['theme_id'] = themeId;
    // json['subtheme_id'] = subthemeId;
    // json['product_id'] = productId;
    // json['tagName'] = tagName;
    // json['data_source'] = dataSource;
    // json['publication_date'] = publicationDate;
    // json['updated'] = updated;
    // json['language'] = language;
    // json['indicatorTools'] = indicatorTools;
    // json['indicatorActions'] =
    //     indicatorActions?.map((e) => e.toJson()).toList();
    // json['indicatorDrivers'] = indicatorDrivers;
    // json['indicatorFilters'] = indicatorFilters;
    json['indicatorValues'] = indicatorValues?.toJson();
    json['filterPanel'] = filterPanel?.toJson();
    // json['sortVisualizations'] = sortVisualizations;
    json['indicatorVisualizations'] = indicatorVisualizations?.toJson();
    // json['benchmarks'] = benchmarks;
    // json['isUploadCompareData'] = isUploadCompareData;
    json['tableFields'] = tableFields?.map((e) => e.toJson()).toList();
    // json['updatedDateFromDB'] = updatedDateFromDB;
    // json['sortOrder'] = sortOrder;
    return json;
  }
}

// class IndicatorActions {
//   IndicatorActions({
//     this.id,
//     this.type,
//     this.label,
//   });
//
//   IndicatorActions.fromJson(Map<String, dynamic> json) {
//     id = json['id'] as String?;
//     type = json['type'] as String?;
//     label = json['label'] as String?;
//   }
//   String? id;
//   String? type;
//   String? label;
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> json = <String, dynamic>{};
//     json['id'] = id;
//     json['type'] = type;
//     json['label'] = label;
//     return json;
//   }
// }

class FilterPanel {
  FilterPanel({
    this.id,
    // this.isEnabled,
    // this.label,
    // this.viewName,
    // this.dimension,
    this.isCFD,
    this.properties,
  });

  FilterPanel.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    // isEnabled = json['isEnabled'] as bool?;
    // label = json['label'] as String?;
    // viewName = json['viewName'] as String?;
    // dimension = (json['dimension'] as Map<String, dynamic>?) != null
    //     ? Dimension.fromJson(json['dimension'] as Map<String, dynamic>)
    //     : null;
    isCFD = json['isCFD'] as bool?;
    properties = (json['properties'] as List?)
        ?.map((dynamic e) => Properties.fromJson(e as Map<String, dynamic>))
        .toList();
    propertiesForComputation = (json['properties'] as List?)
        ?.map((dynamic e) => Properties.fromJson(e as Map<String, dynamic>))
        .toList();
  }

  String? id;
  // bool? isEnabled;
  // String? label;
  // String? viewName;
  // Dimension? dimension;
  /// Is Consumer Financial Distress Indicator in Insight-Discover.
  bool? isCFD;
  List<Properties>? properties;

  // TODO(Jerin): Only used in old detail page. Remove is not needed.
  List<Properties>? propertiesForComputation;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['id'] = id;
    json['isCFD'] = isCFD;
    // json['isEnabled'] = isEnabled;
    // json['label'] = label;
    // json['viewName'] = viewName;
    // json['dimension'] = dimension?.toJson();
    json['properties'] = properties?.map((e) => e.toJson()).toList();
    json['properties'] =
        propertiesForComputation?.map((e) => e.toJson()).toList();
    return json;
  }
}

class Properties {
  Properties({
    this.label,
    this.path,
    this.format,
    this.isMultiSeries,
    this.staticFilter,
    this.isRanked,
    this.dependency,
    this.multipleDependency,
    this.type,
    this.defaultVal,
    this.options,
    this.lang,
    this.isOpened = false,
    this.propertyDefault,
    this.optionList = const [],
  });

  Properties.fromJson(Map<String, dynamic> json) {
    label = json['label'] as String?;
    path = json['path'] as String?;
    format = json['format'] as String?;
    isMultiSeries = json['isMultiSeries'] as bool?;
    staticFilter = json['staticFilter'] as bool?;
    isRanked = json['isRanked'] as bool?;
    dependency = json['dependency'] != null ? Dependency.fromJson(json['dependency'] as JSONObject) : null;
    multipleDependency = json['multipleDependency'] != null
        ? List<JSONObject>.from(json['multipleDependency'] as List<dynamic>)
            .map(
              (e) => Dependency.fromJson(e),
            )
            .toList()
        : null;
    type = json['type'] as String?;
    defaultVal = json['default'] as String?;
    options = (json['options'] as List?)?.map((dynamic e) => e as String).toList();
    // TODO(Jerin): Once all filters are moved to new impl. Remove `optionsList`.
    if (options != null) {
      for (final String? element in options ?? []) {
        optionList.add(
          OptionList(
            title: element,
          ),
        );
      }
    }
    lang = json['lang'] as String?;
    isOpened = json['is_opened'] as bool?;
    propertyDefault = json['default'] as String?;
  }

  String? label;
  String? path;
  String? format;
  bool? isMultiSeries;
  bool? staticFilter;
  bool? isRanked;
  String? propertyDefault;
  String? type;
  String? lang;
  String? defaultVal;
  bool? isOpened = false;
  List<String>? options;
  List<OptionList> optionList = [];
  String? selectedOptionItem;

  Dependency? dependency;
  List<Dependency>? multipleDependency;

  Properties clone() => Properties(
        label: label,
        path: path,
        format: format,
        isMultiSeries: isMultiSeries,
        staticFilter: staticFilter,
        isRanked: isRanked,
        type: type,
        defaultVal: defaultVal,
        options: options,
        lang: lang,
        isOpened: isOpened,
        propertyDefault: propertyDefault,
        optionList: optionList.map((e) => e.clone()).toList(),
      );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['label'] = label;
    json['path'] = path;
    json['format'] = format;
    json['isMultiSeries'] = isMultiSeries;
    json['staticFilter'] = staticFilter;
    json['isRanked'] = isRanked;
    json['dependency'] = dependency?.toJson();
    json['multipleDependency'] = multipleDependency?.map((e) => e.toJson()).toList();
    json['type'] = type;
    json['default'] = defaultVal;
    json['options'] = options;
    json['lang'] = lang;
    json['is_opened'] = isOpened;
    json['default'] = propertyDefault;
    return json;
  }

  bool isAllDependencySatisfied(SelectedFilterMap filter) {
    final dependency = this.dependency;
    if (dependency != null) return dependency.isSatisfied(filter);

    final multipleDependency = this.multipleDependency;
    if (multipleDependency != null) {
      return multipleDependency.every(
        (dep) => dep.isSatisfied(filter),
      );
    }

    return true;
  }
}

class Dependency {
  Dependency({
    this.value,
    this.condition,
    this.filter,
  });

  Dependency.fromJson(JSONObject json)
      : value = json['value'] as String?,
        condition = json['condition'] as String?,
        filter = json['filter'] as String?;

  final String? value;
  final String? condition;
  final String? filter;

  bool isSatisfied(SelectedFilterMap filter) {
    if (filter.isEmpty) return false;

    return filter.entries.any((e) {
      final isSameFilter = e.key == this.filter;
      final condition = this.condition;
      if (!isSameFilter || condition == null) return false;

      return e.value.every(
        (value) => DependencyCompareUtil(value, this.value ?? '', condition).isValid,
      );
    });
  }

  JSONObject toJson() => {
    'value': value,
    'condition': condition,
    'filter': filter,
  };
}

class OptionList {
  OptionList({
    this.title,
    this.isSelected = false,
  });

  String? title;
  bool isSelected = false;

  OptionList clone() => OptionList(
        title: title,
        isSelected: isSelected,
      );
}

// class FilterBy {
//   FilterBy({
//     this.cOUNTRYREGION,
//     this.oWNERTYPE,
//     this.iNDUSTRY,
//   });
//
//   FilterBy.fromJson(Map<String, dynamic> json) {
//     cOUNTRYREGION = json['COUNTRY_REGION'] as String?;
//     oWNERTYPE = json['OWNER_TYPE'] as String?;
//     iNDUSTRY = json['INDUSTRY'] as String?;
//   }
//   String? cOUNTRYREGION;
//   String? oWNERTYPE;
//   String? iNDUSTRY;
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> json = <String, dynamic>{};
//     json['COUNTRY_REGION'] = cOUNTRYREGION;
//     json['OWNER_TYPE'] = oWNERTYPE;
//     json['INDUSTRY'] = iNDUSTRY;
//     return json;
//   }
// }

class YearlyData {
  YearlyData({
    this.viewName,
    this.dbColumn,
    this.dbIndicatorId,
  });

  YearlyData.fromJson(Map<String, dynamic> json) {
    viewName = json['viewName'] as String?;
    dbColumn = json['dbColumn'] as String?;
    dbIndicatorId = json['dbIndicatorId'] as String?;
  }
  String? viewName;
  String? dbColumn;
  String? dbIndicatorId;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['viewName'] = viewName;
    json['dbColumn'] = dbColumn;
    json['dbIndicatorId'] = dbIndicatorId;
    return json;
  }
}

// class TableDefinition {
//   TableDefinition({
//     this.label,
//     this.path,
//   });
//
//   TableDefinition.fromJson(Map<String, dynamic> json) {
//     label = json['label'] as String?;
//     path = json['path'] as String?;
//   }
//   String? label;
//   String? path;
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> json = <String, dynamic>{};
//     json['label'] = label;
//     json['path'] = path;
//     return json;
//   }
// }

// class Data {
//   String? iNDUSTRYAR;
//   String? cOUNTRYREGION;
//   String? oWNERTYPE;
//   String? iNDUSTRY;
//   String? cOUNTRYREGIONAR;
//   String? iNDICATORIDAR;
//   int? vALUE;
//   String? iNSERTUSERID;
//   String? oBSDT;
//   String? iNSERTDT;
//   String? iNDICATORID;
//   String? oWNERTYPEAR;
//   String? vALUETYPE;
//   String? yEAR;
//
//   Data({
//     this.iNDUSTRYAR,
//     this.cOUNTRYREGION,
//     this.oWNERTYPE,
//     this.iNDUSTRY,
//     this.cOUNTRYREGIONAR,
//     this.iNDICATORIDAR,
//     this.vALUE,
//     this.iNSERTUSERID,
//     this.oBSDT,
//     this.iNSERTDT,
//     this.iNDICATORID,
//     this.oWNERTYPEAR,
//     this.vALUETYPE,
//     this.yEAR,
//   });
//
//   Data.fromJson(Map<String, dynamic> json) {
//     iNDUSTRYAR = json['INDUSTRY_AR'] as String?;
//     cOUNTRYREGION = json['COUNTRY_REGION'] as String?;
//     oWNERTYPE = json['OWNER_TYPE'] as String?;
//     iNDUSTRY = json['INDUSTRY'] as String?;
//     cOUNTRYREGIONAR = json['COUNTRY_REGION_AR'] as String?;
//     iNDICATORIDAR = json['INDICATOR_ID_AR'] as String?;
//     vALUE = json['VALUE'] as int?;
//     iNSERTUSERID = json['INSERT_USER_ID'] as String?;
//     oBSDT = json['OBS_DT'] as String?;
//     iNSERTDT = json['INSERT_DT'] as String?;
//     iNDICATORID = json['INDICATOR_ID'] as String?;
//     oWNERTYPEAR = json['OWNER_TYPE_AR'] as String?;
//     vALUETYPE = json['VALUE_TYPE'] as String?;
//     yEAR = json['YEAR'] as String?;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> json = <String, dynamic>{};
//     json['INDUSTRY_AR'] = iNDUSTRYAR;
//     json['COUNTRY_REGION'] = cOUNTRYREGION;
//     json['OWNER_TYPE'] = oWNERTYPE;
//     json['INDUSTRY'] = iNDUSTRY;
//     json['COUNTRY_REGION_AR'] = cOUNTRYREGIONAR;
//     json['INDICATOR_ID_AR'] = iNDICATORIDAR;
//     json['VALUE'] = vALUE;
//     json['INSERT_USER_ID'] = iNSERTUSERID;
//     json['OBS_DT'] = oBSDT;
//     json['INSERT_DT'] = iNSERTDT;
//     json['INDICATOR_ID'] = iNDICATORID;
//     json['OWNER_TYPE_AR'] = oWNERTYPEAR;
//     json['VALUE_TYPE'] = vALUETYPE;
//     json['YEAR'] = yEAR;
//     return json;
//   }
// }

class Security {
  Security({
    this.name,
  });

  Security.fromJson(JSONObject json) {
    name = json['name'] as String?;
  }

  String? get id => name?.split('-').firstOrNull?.trim();

  String? get label => name?.split('-').lastOrNull?.trim();

  String? name;

  JSONObject toJson() => {
        'name': name,
      };
}
