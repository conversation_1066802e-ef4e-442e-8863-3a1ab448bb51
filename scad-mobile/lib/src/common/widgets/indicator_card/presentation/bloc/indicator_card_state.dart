part of 'indicator_card_bloc.dart';

abstract class IndicatorCardState extends Equatable {

  const IndicatorCardState({required this.id, required this.contentType});

  final String id;
  final String contentType;

  @override
  List<Object> get props => [];
}

class IndicatorDetailsInitState extends IndicatorCardState {
  const IndicatorDetailsInitState({required super.id, required super.contentType});
}

class IndicatorDetailsLoadingState extends IndicatorCardState {
  IndicatorDetailsLoadingState({required super.id, required super.contentType}) {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;

  @override
  List<Object> get props => [rnd];
}

class IndicatorDetailsErrorState extends IndicatorCardState {
  IndicatorDetailsErrorState({required this.error, required super.id, required super.contentType}) {
    rnd = Random().nextInt(10000);
  }

  final String error;
  late final int rnd;

  @override
  List<Object> get props => [rnd, error];
}

class IndicatorDetailsSuccessState extends IndicatorCardState {
  IndicatorDetailsSuccessState({
    required super.id,
    required super.contentType,
    required this.indicatorDetails,
    this.isFromDriverForSearch,
  }) {
    rnd = Random().nextInt(10000);
  }

  late final int rnd;
  final IndicatorDetailsResponse indicatorDetails;
  final bool? isFromDriverForSearch;

  @override
  List<Object> get props => [
        rnd,
        indicatorDetails,
        isFromDriverForSearch ?? false,
        contentType,
      ];
}

class IndicatorStatusErrorState extends IndicatorCardState {
  const IndicatorStatusErrorState({required this.error, required super.id, required super.contentType});

  final String error;

  @override
  List<Object> get props => [error];
}

class IndicatorStatusSuccessState extends IndicatorCardState {
  const IndicatorStatusSuccessState({
    required this.subscription,
    required this.myApps,
    required super.id, required super.contentType,
  });

  final NodeIdUuid subscription;
  final NodeIdUuid myApps;

  @override
  List<Object> get props => [subscription, myApps];
}










class GetIndicatorDetailsV2LoadingState extends IndicatorCardState {
  const GetIndicatorDetailsV2LoadingState({
    required super.id,
    required super.contentType,
  });

  @override
  List<Object> get props => [id, contentType];
}

class GetIndicatorDetailsV2ErrorState extends IndicatorCardState {
  const GetIndicatorDetailsV2ErrorState({
    required super.id,
    required super.contentType,
    required this.error,
  });

  final String error;

  @override
  List<Object> get props => [id, contentType, error];
}

class GetIndicatorDetailsV2SuccessState extends IndicatorCardState {
  const GetIndicatorDetailsV2SuccessState({
    required super.id,
    required super.contentType,
    required this.indicatorDetailsResponse,
  });

  final IndicatorDetailsResponse indicatorDetailsResponse;

  @override
  List<Object> get props => [
        id,
        contentType,
      ];
}
