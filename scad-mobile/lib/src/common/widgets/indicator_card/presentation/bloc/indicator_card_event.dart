part of 'indicator_card_bloc.dart';

abstract class IndicatorCardEvent extends Equatable {
  const IndicatorCardEvent();

  @override
  List<Object> get props => [];
}

class GetIndicatorDetailsEvent extends IndicatorCardEvent {
  GetIndicatorDetailsEvent({
    required this.contentType,
    required this.id,
    this.overviewContentType = '',
    this.isFromChangeDriverCallInitOnce,
    this.isFromDriverForSearch,
    this.payload = const {},
  }) {
    rnd = Random().nextInt(10000);
  }

  final String overviewContentType;
  final String contentType;
  final String id;
  final Map<String, String> payload;
  final bool? isFromChangeDriverCallInitOnce;
  final bool? isFromDriverForSearch;
  late final int rnd;

  @override
  List<Object> get props =>
      [
        overviewContentType,
        contentType,
        id,
        payload,
        rnd,
        isFromChangeDriverCallInitOnce ?? false,
        isFromDriverForSearch ?? false,
      ];
}

class GetIndicatorStatusEvent extends IndicatorCardEvent {
  const GetIndicatorStatusEvent({required this.id});

  final String id;

  @override
  List<Object> get props => [id];
}

class GetSpatialAnalyticsStatusEvent extends IndicatorCardEvent {
  const GetSpatialAnalyticsStatusEvent({required this.moduleKey, required this.id});

  final String id;
  final String moduleKey;

  @override
  List<Object> get props => [id,moduleKey];
}

class GetIndicatorDetailsV2Event extends IndicatorCardEvent {
  const GetIndicatorDetailsV2Event({required this.id, required this.contentType});

  final String id;
  final String contentType;

  @override
  List<Object> get props => [id, contentType];
}
