import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scad_mobile/route_manager/route_imports.dart';
import 'package:scad_mobile/src/common/models/response_models/repo_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_details_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/data/models/indicator_status_response.dart';
import 'package:scad_mobile/src/common/widgets/indicator_card/domain/repositories/indicator_card_repository_imports.dart';
import 'package:scad_mobile/src/common/widgets/lazy_indicator_list_view/presentation/bloc/lazy_indicator_list_view_bloc.dart';
import 'package:scad_mobile/src/config/dependancy_injection/injection_container.dart';
import 'package:scad_mobile/src/features/home/<USER>/bloc/home_bloc/home_bloc.dart';
import 'package:scad_mobile/translations/locale_keys.g.dart';

part 'indicator_card_event.dart';
part 'indicator_card_state.dart';

class IndicatorCardBloc extends Bloc<IndicatorCardEvent, IndicatorCardState> {
  IndicatorCardBloc() : super(const IndicatorDetailsInitState(id: '', contentType: '')) {
    on<GetIndicatorDetailsEvent>(getIndicatorDetailsEventHandler);
    on<GetIndicatorStatusEvent>(getIndicatorStatusEventHandler);
    on<GetSpatialAnalyticsStatusEvent>(_onGetSpatialAnalyticsStatusEvent);
   }

  final _eventLogs = <IndicatorCardEvent>[];

  void _emitIndicatorEventStatus({
    required String status,
    required String id,
    required String contentType,
  }) {
    final context = servicelocator<AppRouter>().navigatorKey.currentContext!;
    context.read<HomeBloc>().add(
          IndicatorHomeCurrentEvent(
            status: status,
            id: id,
            contentType: contentType,
          ),
        );
    context.read<LazyIndicatorListViewBloc>().add(
          LazyIndicatorItemEvent(
            status: status,
            id: id,
            contentType: contentType,
          ),
        );
  }

  FutureOr<void> getIndicatorDetailsEventHandler(
    GetIndicatorDetailsEvent event,
    Emitter<IndicatorCardState> emit,
  ) async {
    if (_eventLogs.contains(event)) return;
    _eventLogs.add(event);

    try {
      _emitIndicatorEventStatus(
        status: 'loading',
        id: event.id,
        contentType: event.contentType,
      );

      emit(IndicatorDetailsLoadingState(id: event.id, contentType: event.contentType));

      final RepoResponse<IndicatorDetailsResponse> indicatorDetailsResponse =
          await servicelocator<IndicatorCardRepository>().indicatorDetails(
        id: event.id,
        contentType: event.contentType,
        payload: event.payload,
      );

      if (indicatorDetailsResponse.isSuccess) {
        final IndicatorDetailsResponse indicatorDetails = indicatorDetailsResponse.response!;

        _emitIndicatorEventStatus(
          status: 'success',
          id: event.id,
          contentType: event.contentType,
        );

        emit(
          IndicatorDetailsSuccessState(
            id: event.id,
            indicatorDetails: indicatorDetails,
            isFromDriverForSearch: event.isFromDriverForSearch,
            contentType: event.contentType,
          ),
        );
      } else {
        if (indicatorDetailsResponse.statusCode == HttpStatus.notFound) {
          _emitIndicatorEventStatus(
            status: 'error',
            id: event.id,
            contentType: event.contentType,
          );
        } else {
          final context = servicelocator<AppRouter>().navigatorKey.currentContext!;
          if (context.mounted) {
            context.read<LazyIndicatorListViewBloc>().add(
                  LazyIndicatorItemEvent(
                    status: 'error',
                    id: event.id,
                    contentType: event.contentType,
                  ),
                );
          }
        }

        emit(
          IndicatorDetailsErrorState(
            id: event.id,
            contentType: event.contentType,
            error: indicatorDetailsResponse.errorMessage,
          ),
        );
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
        IndicatorDetailsErrorState(
          id: event.id,
          contentType: event.contentType,
          error: LocaleKeys.somethingWentWrong.tr(),
        ),
      );
    } finally {
      _eventLogs.remove(event);
    }
  }

  FutureOr<void> getIndicatorStatusEventHandler(
    GetIndicatorStatusEvent event,
    Emitter<IndicatorCardState> emit,
  ) async {
    try {
      // emit(IndicatorStatusLoadingState());
    // final String hiveKey =
    //       'indicatorDetailsStatusMyappsNotifiaction:${event.id}}';

      final RepoResponse<IndicatorStatusResponse> response =
          await servicelocator<IndicatorCardRepository>()
              .getIndicatorStatus(id: event.id);

      if (response.isSuccess) {
        // await HiveUtilsApiCache.set(hiveKey, response.response?.toJson());
        emit(
          IndicatorStatusSuccessState(
            id: '', contentType: '',
            subscription: response.response?.subscriptions ?? NodeIdUuid(),
            myApps: response.response?.myApps ?? NodeIdUuid(),
          ),
        );
      } else {
        emit(IndicatorStatusErrorState(
            id: '', contentType: '',
            error: response.errorMessage));
      }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
          IndicatorStatusErrorState(
              id: '', contentType: '',
              error: LocaleKeys.somethingWentWrong.tr()),);
    }
  }

  FutureOr<void> _onGetSpatialAnalyticsStatusEvent(
      GetSpatialAnalyticsStatusEvent event,
    Emitter<IndicatorCardState> emit,
  ) async {
    try {
    final List<RepoResponse<IndicatorStatusResponse>> responses = await Future.wait([
      servicelocator<IndicatorCardRepository>().getIndicatorStatus(id: event.id),
      servicelocator<IndicatorCardRepository>().getIndicatorStatus(id: '${event.id}-${event.moduleKey}'),
    ]);

    if (responses.every((element) => element.isSuccess)) {
      final RepoResponse<IndicatorStatusResponse> response1 = responses[0];
      final RepoResponse<IndicatorStatusResponse> response2 = responses[1];

      emit(
        IndicatorStatusSuccessState(
          id: '', contentType: '',
          subscription: response1.response?.subscriptions ?? NodeIdUuid(),
          myApps: response2.response?.myApps ?? NodeIdUuid(),
        ),
      );
    } else {
      emit(
        IndicatorStatusErrorState(
          id: '', contentType: '',
          error: responses
              .firstWhere((element) => !element.isSuccess)
              .errorMessage,
        ),
      );
    }
    } catch (e, s) {
      Completer<dynamic>().completeError(e, s);
      emit(
          IndicatorStatusErrorState(
              id: '', contentType: '',
              error: LocaleKeys.somethingWentWrong.tr()),);
    }
  }
}
