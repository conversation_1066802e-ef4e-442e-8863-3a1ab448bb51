// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package com.google.firebase.installations;

import com.google.firebase.installations.time.Clock;

public class FakeClock implements Clock {
  private long currentTimeMillis;

  /** Creates a FakeClock instance initialized to the given time. */
  public FakeClock(long currentTimeMillis) {
    this.currentTimeMillis = currentTimeMillis;
  }

  @Override
  public long currentTimeMillis() {
    return currentTimeMillis;
  }

  public void advanceTimeBySeconds(long deltaSeconds) {
    currentTimeMillis += (deltaSeconds * 1000L);
  }
}
