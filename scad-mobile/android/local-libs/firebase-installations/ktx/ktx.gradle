// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

plugins {
    id 'firebase-library'
    id("kotlin-android")
}

firebaseLibrary {
    libraryGroup = "installations"
    publishJavadoc = false
    releaseNotes { 
        enabled.set(false)
    }
}

android {
    namespace "com.google.firebase.installations.ktx"
    compileSdkVersion project.compileSdkVersion
    defaultConfig {
        minSdkVersion project.minSdkVersion
        multiDexEnabled true
        targetSdkVersion project.targetSdkVersion
        versionName version
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        test.java.srcDirs += 'src/test/kotlin'
        androidTest.java.srcDirs += 'src/androidTest/kotlin'
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    testOptions.unitTests.includeAndroidResources = true
}

dependencies {
    api(project(":firebase-installations"))
    api("com.google.firebase:firebase-common:21.0.0")
    api("com.google.firebase:firebase-common-ktx:21.0.0")
    api("com.google.firebase:firebase-installations-interop:17.1.1")

    implementation("com.google.firebase:firebase-components:18.0.0")

    testImplementation libs.androidx.test.core
    testImplementation libs.truth
    testImplementation 'junit:junit:4.13'
    testImplementation libs.robolectric
}
