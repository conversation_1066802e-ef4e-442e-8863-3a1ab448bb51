package ae.gov.scad.adstatapp

import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import android.view.WindowManager

class MainActivity : FlutterFragmentActivity() {

    private val viewType: String = "arc-gis-map"

    init {
        instance = this
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        ae.gov.scad.adstatapp.MainActivity.Companion.instance.window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
        flutterEngine.platformViewsController.registry.registerViewFactory(
            viewType, NativeViewFactory(flutterEngine)
        )
    }

    companion object {
        fun getInstance(): MainActivity {
            return instance
        }

        private lateinit var instance: MainActivity
    }

}
