[versions]
# javalite, protoc and protobufjavautil versions should be in sync while updating and
# it needs to match the protobuf version which grpc has transitive dependency on, which
# needs to match the version of grpc that grpc-kotlin has a transitive dependency on.
androidGradlePlugin = "8.3.2"
android-lint = "31.3.2"
autovalue = "1.10.1"
coroutines = "1.7.3"
dagger = "2.43.2"
featureDelivery = "2.1.0"
firebaseCommon = "21.0.0"
firebaseComponents = "18.0.0"
grpc = "1.62.2"
grpcKotlin = "1.4.1"
javalite = "3.25.5"
kotlin = "1.9.23"
mavenResolverApi = "1.9.22"
mavenResolverProvider = "3.9.9"
mockk = "1.13.11"
serialization-plugin = "1.8.22"
protoc = "3.25.5"
truth = "1.4.2"
robolectric = "4.12"
protobufjavautil = "3.25.5"
kotest = "5.9.0" # Do not use 5.9.1 because it reverts the fix for https://github.com/kotest/kotest/issues/3981
quickcheck = "0.6"
serialization = "1.5.1"
spotless = "7.0.0.BETA3"
androidx-test-core = "1.5.0"
androidx-test-junit = "1.1.5"
androidx-test-truth = "1.5.0"

[libraries]
android-gradlePlugin-gradle = { group = "com.android.tools.build", name = "gradle", version.ref = "androidGradlePlugin" }
android-gradlePlugin-gradle-api = { group = "com.android.tools.build", name = "gradle-api", version.ref = "androidGradlePlugin" }
android-gradlePlugin-builder-test-api = { group = "com.android.tools.build", name = "builder-test-api", version.ref = "androidGradlePlugin" }
android-lint = { module = "com.android.tools.lint:lint", version.ref = "android-lint" }
android-lint-api = { module = "com.android.tools.lint:lint-api", version.ref = "android-lint" }
android-lint-checks = { module = "com.android.tools.lint:lint-checks", version.ref = "android-lint" }
android-lint-tests = { module = "com.android.tools.lint:lint-tests", version.ref = "android-lint" }
android-lint-testutils = { module = "com.android.tools:testutils", version.ref = "android-lint" }
androidx-annotation = { module = "androidx.annotation:annotation", version = "1.5.0" }
androidx-core = { module = "androidx.core:core", version = "1.2.0" }
androidx-futures = { module = "androidx.concurrent:concurrent-futures", version = "1.1.0" }
auth0-jwt = { module = "com.auth0:java-jwt", version = "4.4.0" }
autovalue = { module = "com.google.auto.value:auto-value", version.ref = "autovalue" }
autovalue-annotations = { module = "com.google.auto.value:auto-value-annotations", version.ref = "autovalue" }
dagger-dagger = { module = "com.google.dagger:dagger", version.ref = "dagger" }
dagger-compiler = { module = "com.google.dagger:dagger-compiler", version.ref = "dagger" }
errorprone-annotations = { module = "com.google.errorprone:error_prone_annotations", version = "2.26.0" }
feature-delivery = { module = "com.google.android.play:feature-delivery", version.ref = "featureDelivery" }
findbugs-jsr305 = { module = "com.google.code.findbugs:jsr305", version = "3.0.2" }
firebase-common = { module = "com.google.firebase:firebase-common", version.ref = "firebaseCommon" }
firebase-components = { module = "com.google.firebase:firebase-components", version.ref = "firebaseComponents" }
grpc-android = { module = "io.grpc:grpc-android", version.ref = "grpc" }
grpc-kotlin-stub = { module = "io.grpc:grpc-kotlin-stub", version.ref = "grpcKotlin" }
grpc-okhttp = { module = "io.grpc:grpc-okhttp", version.ref = "grpc" }
grpc-protobuf-lite = { module = "io.grpc:grpc-protobuf-lite", version.ref = "grpc" }
grpc-testing = { module = "io.grpc:grpc-testing", version.ref = "grpc" }
grpc-protoc-gen-java = { module = "io.grpc:protoc-gen-grpc-java", version.ref = "grpc" }
grpc-protoc-gen-kotlin = { module = "io.grpc:protoc-gen-grpc-kotlin", version.ref = "grpcKotlin" }
grpc-stub = { module = "io.grpc:grpc-stub", version.ref = "grpc" }
javax-annotation-jsr250 = { module = "javax.annotation:jsr250-api", version = "1.0" }
javax-inject = { module = "javax.inject:javax.inject", version = "1" }
kotlin-bom = { module = "org.jetbrains.kotlin:kotlin-bom", version.ref = "kotlin" }
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib", version.ref = "kotlin" }
kotlin-stdlib-jdk8 = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlin-coroutines-tasks = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-play-services", version.ref = "coroutines" }
kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version = "0.6.1" }
kotlinx-serialization-core = { module = "org.jetbrains.kotlinx:kotlinx-serialization-core", version.ref = "serialization" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "serialization" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }
maven-resolver-api = { module = "org.apache.maven.resolver:maven-resolver-api", version.ref = "mavenResolverApi" }
maven-resolver-connector-basic = { module = "org.apache.maven.resolver:maven-resolver-connector-basic", version.ref = "mavenResolverApi" }
maven-resolver-impl = { module = "org.apache.maven.resolver:maven-resolver-impl", version.ref = "mavenResolverApi" }
maven-resolver-provider = { module = "org.apache.maven:maven-resolver-provider", version.ref = "mavenResolverProvider" }
maven-resolver-transport-file = { module = "org.apache.maven.resolver:maven-resolver-transport-file", version.ref = "mavenResolverApi" }
maven-resolver-transport-http = { module = "org.apache.maven.resolver:maven-resolver-transport-http", version.ref = "mavenResolverApi" }
maven-resolver-util = { module = "org.apache.maven.resolver:maven-resolver-util", version.ref = "mavenResolverApi" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version = "3.12.13" }
org-json = { module = "org.json:json", version = "20210307" }
playservices-base = { module = "com.google.android.gms:play-services-base", version = "18.1.0" }
playservices-basement = { module = "com.google.android.gms:play-services-basement", version = "18.3.0" }
playservices-tasks = { module = "com.google.android.gms:play-services-tasks", version = "18.1.0" }
protoc = { module = "com.google.protobuf:protoc", version.ref = "protoc" }
protobuf-java = { module = "com.google.protobuf:protobuf-java", version.ref = "javalite" }
protobuf-java-lite = { module = "com.google.protobuf:protobuf-javalite", version.ref = "javalite" }
protobuf-kotlin-lite = { module = "com.google.protobuf:protobuf-kotlin-lite", version.ref = "javalite" }

# Test libs
androidx-test-core = { module = "androidx.test:core", version.ref = "androidx-test-core" }
androidx-test-junit = { module = "androidx.test.ext:junit", version.ref = "androidx-test-junit" }
androidx-test-truth = { module = "androidx.test.ext:truth", version.ref = "androidx-test-truth" }
androidx-test-rules = { module = "androidx.test:rules", version.ref = "androidx-test-core" }
androidx-test-runner = { module = "androidx.test:runner", version = "1.5.2" }
junit = { module = "junit:junit", version = "4.13.2" }
kotlin-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "coroutines" }
mockito-core = { module = "org.mockito:mockito-core", version = "5.2.0" }
mockito-dexmaker = { module = "com.linkedin.dexmaker:dexmaker-mockito", version = "2.28.3" }
mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
mockk-android = { module = "io.mockk:mockk-android", version.ref = "mockk" }
robolectric = { module = "org.robolectric:robolectric", version.ref = "robolectric" }
truth = { module = "com.google.truth:truth", version.ref = "truth" }
truth-liteproto-extension = { module = "com.google.truth.extensions:truth-liteproto-extension", version.ref = "truth" }
protobuf-java-util = { module = "com.google.protobuf:protobuf-java-util", version.ref = "protobufjavautil" }
kotest-runner = { module = "io.kotest:kotest-runner-junit4-jvm", version.ref = "kotest" }
kotest-assertions = { module = "io.kotest:kotest-assertions-core-jvm", version.ref = "kotest" }
kotest-property = { module = "io.kotest:kotest-property-jvm", version.ref = "kotest" }
kotest-property-arbs = { module = "io.kotest.extensions:kotest-property-arbs", version = "2.1.2" }
quickcheck = { module = "net.java:quickcheck", version.ref = "quickcheck" }
spotless-plugin-gradle = { module = "com.diffplug.spotless:spotless-plugin-gradle", version.ref = "spotless" }
turbine = { module = "app.cash.turbine:turbine", version = "1.0.0" }

# Remove three-ten-abp once minSdkVersion is changed to 26 or later, and, instead use the
# correspondingly-named classes from the java.time package, which should be drop-in replacements.
# Do not use three-ten-abp in production code (it's only for tests) because it has performance
# issues.
testonly-three-ten-abp = { module = "com.jakewharton.threetenabp:threetenabp", version = "1.4.7" }

[bundles]
kotest = ["kotest-runner", "kotest-assertions", "kotest-property", "kotest-property-arbs"]
playservices = ["playservices-base", "playservices-basement", "playservices-tasks"]
maven-resolver = [
    "maven-resolver-api",
    "maven-resolver-connector-basic",
    "maven-resolver-impl",
    "maven-resolver-provider",
    "maven-resolver-transport-file",
    "maven-resolver-transport-http",
    "maven-resolver-util"
]

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlinx-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "serialization-plugin" }
spotless = { id = "com.diffplug.spotless", version.ref = "spotless" }
