module Fastlane
  module Actions
    class GetLatestChangeLogAction < Action
      def self.run(params)
        changelog_file = params[:changelog_file]
        latest_version = nil
        latest_changes = []

        File.open(changelog_file, 'r') do |file|
          file.each_line do |line|
            if line =~ /^## v\d+\.\d+\.\d+/
              break if latest_version
              latest_version = line.strip.gsub(/^##\s? /, '')
            elsif latest_version
              next if line.strip.empty?
              latest_changes << line.strip.gsub(/\*+$/, '')
            end
          end
        end

        changes = latest_changes.join("\n")

        UI.message "Latest Version: #{latest_version}"
        UI.message "Latest Changes:\n#{changes}"

        { version: latest_version, changes: changes }
      end

      #####################################################
      # @!group Documentation
      #####################################################

      def self.description
        "Extracts the latest change logs from the CHANGE_LOGS.md file"
      end

      def self.available_options
        [
          FastlaneCore::ConfigItem.new(
            key: :changelog_file,
            description: "Path to the CHANGELOG.md file",
            optional: false,
            type: String
          )
        ]
      end

      def self.authors
        ["Jerinji2016"]
      end

      def self.is_supported?(platform)
        true
      end
    end
  end
end