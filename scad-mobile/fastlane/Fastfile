PUBSPEC_PATH = "pubspec.yaml"

ANDROID_PACKAGE_NAME = "ae.gov.scad.adstatapp"
APK_PATH = "build/app/outputs/flutter-apk"
APK_FILE_NAME = "app-release.apk"
AAB_PATH = "build/app/outputs/bundle/release"
AAB_FILE_NAME = "app-release.apk"

SPLIT_APK_PATH = "build/app/outputs/flutter-apk"
ARMEABI_V7_FILE_NAME = "app-armeabi-v7a-release.apk"
ARM64_V8_FILE_NAME = "app-arm64-v8a-release.apk"
X86_64_FILE_NAME = "app-x86_64-release.apk"

IOS_PACKAGE_NAME = "ae.gov.scad.adstatapp"
XC_PATH = "build/ios/archive"
XC_OUT_NAME = "Runner.xcarchive"

OUTPUT_PATH = "output"
ITERATION_TMP_FILE = "iteration.yaml"

APP_PREFIX = "bayaan_private"

module SharedValues
  FL_PLATFORM = :FL_PLATFORM
  FL_ENV = :FL_ENV
  FL_ARTIFACTS = :FL_ARTIFACTS

  FL_ENV_FILE = :FL_ENV_FILE
  FL_NEW_VERSION = :FL_NEW_VERSION

  FL_SHOREBIRD = :FL_SHOREBIRD
  FL_ITERATION = :FL_ITERATION
end

def get_platform(options)
  default = options[:platform] || lane_context[SharedValues::FL_PLATFORM]
  platform = default || UI.select("[platform] Select build type: ", ["both", "android", "ios"])
  unless %w(both ios android).include?(platform)
      UI.user_error!("Invalid platform: #{platform}, Valid values are [both, android, ios]")
  end
  lane_context[SharedValues::FL_PLATFORM] = platform
  return platform
end


def is_shorebird_release(options)
  default = options[:shorebird] || lane_context[SharedValues::FL_SHOREBIRD]
  choice = default || UI.select("[shorebird] Release with shorebird? ", ["yes", "no"])
  unless %w(yes no y n).include?(choice)
      UI.user_error!("Invalid selection: #{choice}, Valid values are [yes, no, y, n]")
  end

  require_shorebird = choice == 'yes' or choice == 'y'
  lane_context[SharedValues::FL_SHOREBIRD] = require_shorebird
  return require_shorebird
end

def get_env(options)
  default = options[:env] || lane_context[SharedValues::FL_ENV]
  env = default || UI.select("[env] Select environment: ", ["prod", "preprod", "demo", "dev", "bnx_dev"])
  unless %w(prod preprod demo dev bnx_dev).include?(env) then
      UI.user_error!("Invalid environment: #{env}, Valid values are [prod, pre-prod, demo, dev, bnx_dev]")
  end
  lane_context[SharedValues::FL_ENV] = env
  return env
end

def get_android_artifacts(options)
  default = options[:artifacts] || lane_context[SharedValues::FL_ARTIFACTS]
  artifact = default || UI.select("[artifacts] Select artifacts: ", ["fat_apk", "split_apk", "aab"])
  unless %w(fat_apk split_apk aab).include?(artifact) then
      UI.user_error!("Invalid artifacts: #{artifact}, Valid values are [fat_apk, split_apk, aab]")
  end
  lane_context[SharedValues::FL_ARTIFACTS] = artifact
  return artifact
end

def set_env_file 
  env = lane_context[SharedValues::FL_ENV]
  UI.message "Building for Environment: #{env}"

  file = nil
  case env
  when "bnx_dev"
    file = 'env-beinex-dev.json'
  when "dev"
    file = 'env-dev.json'
  when "demo"
    file = 'env-demo.json'
  when "preprod"
    file = 'env-stage.json'
  when "prod"
    file = 'env-prod.json'
  else
    UI.user_error "Unknown environment"
  end

  lane_context[SharedValues::FL_ENV_FILE] = file
  UI.message "Env file set: #{lane_context[SharedValues::FL_ENV_FILE]}"
end

def set_version(options) 
  current_version = get_pubspec_version
  new_version = options[:version] || current_version
  lane_context[SharedValues::FL_NEW_VERSION] = new_version
  update_pubspec_version(new_version: new_version)
end

def get_date()
  now = DateTime.now
  return "#{Date::ABBR_MONTHNAMES[now.month].downcase}-#{now.day}"
end

def set_iteration(options)
  current = 0
  if(File.exist?(ITERATION_TMP_FILE))
    content = File.read(ITERATION_TMP_FILE)
    require 'yaml'
    data = YAML.safe_load(content)
    current = data&.fetch(get_date, 0)
  end

  iteration = options[:iteration] || current + 1

  if (iteration <= current)
    UI.error "Sorry, this iteration is already build. Try next number"
  end

  next_iteration = iteration || current + 1
  lane_context[SharedValues::FL_ITERATION] = next_iteration
end 

def prebuild_setup()
  sh 'flutter clean'
  sh 'flutter pub get'
  sh 'flutter pub run build_runner build --delete-conflicting-outputs'
  sh 'flutter pub run easy_localization:generate --source-dir assets/translations --output-dir lib/translations'
  sh 'flutter pub run easy_localization:generate --source-dir assets/translations --output-dir lib/translations --output-file locale_keys.g.dart --format keys'
end

def build_android_artifacts(options)
  artifacts = get_android_artifacts(options)

  Dir.chdir('../') do
    case artifacts
    when "split_apk"
      build_android_split_apk
    when "fat_apk"
      build_android_apk
    when "aab"
      build_android_aab
    else
      UI.user_error "Invalid artifacts selection"
    end
  end

  # save iteration for future
  iteration = lane_context[SharedValues::FL_ITERATION]
  content = "#{get_date}: #{iteration}"
  File.open(ITERATION_TMP_FILE, "w") { |f| f.write(content) }
end

def get_release_file_name(prefix, extension)
  env = lane_context[SharedValues::FL_ENV]
  version = lane_context[SharedValues::FL_NEW_VERSION]
  date = get_date
  iteration = lane_context[SharedValues::FL_ITERATION]
  return "#{prefix}_#{env}_#{version}_#{date}_#{iteration}.#{extension}"
end

def build_android_split_apk()
  env_file = lane_context[SharedValues::FL_ENV_FILE]
  sh "flutter build apk --release --split-per-abi --dart-define-from-file=.environment/#{env_file}"

  # rename and move file
  new_arm_64_v8_name = get_release_file_name("#{APP_PREFIX}_arm64_v8", 'apk')
  File.rename "#{SPLIT_APK_PATH}/#{ARM64_V8_FILE_NAME}", "#{OUTPUT_PATH}/#{new_arm_64_v8_name}"

  # temporarily not required, so commented
  # new_armeabi_v7_name = get_release_file_name("#{APP_PREFIX}_armeabi_v7", 'apk')
  # File.rename "#{SPLIT_APK_PATH}/#{ARMEABI_V7_FILE_NAME}", "#{OUTPUT_PATH}/#{new_armeabi_v7_name}"

  # new_x86_64_name = get_release_file_name("#{APP_PREFIX}_x86_64", 'apk')
  # File.rename "#{SPLIT_APK_PATH}/#{X86_64_FILE_NAME}", "#{OUTPUT_PATH}/#{new_x86_64_name}"
end

def build_android_apk()
  env_file = lane_context[SharedValues::FL_ENV_FILE]
  sh "flutter build apk --release --dart-define-from-file=.environment/#{env_file}"

  # rename and move file
  new_file_name = get_release_file_name("#{APP_PREFIX}", 'apk')
  File.rename "#{APK_PATH}/#{APK_FILE_NAME}", "#{OUTPUT_PATH}/#{new_file_name}"
end

def build_android_aab()
  env_file = lane_context[SharedValues::FL_ENV_FILE]
  sh "flutter build appbundle --release --dart-define-from-file=.environment/#{env_file}"

  # rename and move file
  new_file_name = get_release_file_name("#{APP_PREFIX}", 'aab')
  File.rename "#{AAB_PATH}/#{AAB_FILE_NAME}", "#{OUTPUT_PATH}/#{new_file_name}"
end

def build_ios_xc()
  cocoapods(
    clean_install: true,
    use_bundle_exec: false,
    podfile: 'ios/Podfile'
  )

  env_file = lane_context[SharedValues::FL_ENV_FILE]
  sh "flutter build xcarchive --release --dart-define-from-file=.environment/#{env_file}"

  # export file to outputs
  # File.rename "#{XC_PATH}/#{XC_OUT_NAME}", "#{OUTPUT_PATH}/#{XC_OUT_NAME}"

  UI.success "iOS archived at #{XC_PATH}"
end

def build_runner_delegate(options)
  ENV["FL_PUBSPEC_PATH"] = PUBSPEC_PATH

  platform = get_platform(options)
  env = get_env(options)
  set_env_file
  set_version(options)
  set_iteration(options)

  if %w(both android).include?(platform) then
    artifact = get_android_artifacts(options)
  end

  Dir.chdir('../') do
    prebuild_setup

    if (!Dir.exist? OUTPUT_PATH)
        # Create a directory for output files
        Dir.mkdir OUTPUT_PATH
    end
  end

  begin
    case platform
    when "both"
        UI.message "Building for Android 🤖 & iOS 🍎"
        build_android_artifacts(options)
        build_ios_xc
    when "android"
        UI.message "Building for Android 🤖"
        build_android_artifacts(options)
    when "ios"
        UI.message "Building for iOS 🍎"
        build_ios_xc
    else
        UI.error "Unimplemented #{platform}"
    end

    UI.success "Build complete"
  rescue => e
    UI.error "Build Failed: #{e.message}"
  end
end

desc 'Run build_runner and easy_localization code generation commands'
lane :code_gen do
  Dir.chdir('../') do
    prebuild_setup
  end
end

desc 'Build releases with shorebird integration'
lane :release do |options|
  options[:shorebird] = true
  build_runner_delegate(options)
end

desc 'Build releases traditionally with Flutter'
lane :build do |options|
  build_runner_delegate(options)
end

desc 'Post release cleanup
- Create git tag with version and changes in description
- Merge version branch into prod
- Create new release branch and check into branch
- Bump pubspec version
- Commit and push new branch changes'
lane :post_release_cleanup do |options|
  diff = `git diff`
  if diff.length > 0
    UI.user_error! "There are uncommitted changes. Please commit them before cleanup"
  end

  ENV["FL_PUBSPEC_PATH"] = PUBSPEC_PATH
  current_version = get_pubspec_version

  result = get_latest_change_log(changelog_file: 'CHANGE_LOGS.md')
  latest_changelog_version = result[:version]
  change_logs = result[:changes]

  version, build_number = current_version.split('+')
  if ("v#{version}" != latest_changelog_version)
    UI.user_error! "Version mismatch: Latest CHANGE_LOGS version #{latest_changelog_version} does not match with pubspec version #{version}"
  end

  # Create git tag with version and changes in description
  tag_name = "v#{version}"
  sh "git tag -a #{tag_name} -m '#{change_logs}'"
  sh "git push origin tag #{tag_name}"

  # merge version branch into prod
  current_branch = git_branch

  sh "git checkout prod"
  sh "git pull origin prod"
  sh "git merge #{current_branch}"

  # bump pubspec version
  new_version_string = change_version_string(
    version: current_version,
    bump_patch: true,
    bump_build_number: true
  )

  # create new branch and check into branch
  new_version = new_version_string.split('+').first
  new_branch = "release/v#{new_version}"
  sh "git checkout -b #{new_branch}"
  UI.success "Checked out to new branch: #{new_branch}"

  update_pubspec_version(new_version: new_version_string)
  UI.success "Version bumped to #{new_version_string}"

  # commit and push changes
  commit_message = "Bumped version to #{new_version_string}"
  Dir.chdir('../') do
    sh "git add #{PUBSPEC_PATH}"
    sh "git commit -m '#{commit_message}'"
    sh "git push origin #{new_branch}"
  end

  UI.success "Post Release Cleanup complete"
end
