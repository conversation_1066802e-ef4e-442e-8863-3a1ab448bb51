<svg width="39" height="39" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<rect width="39" height="39" />
<g clip-path="url(#clip0_13133_27192)">
<!--<rect x="-375" y="-377" width="428" height="1809" rx="20"/>-->
<!--<g filter="url(#filter0_b_13133_27192)">-->
<!--<rect width="38.9573" height="38.8938" rx="8" fill="black" fill-opacity="0.53"/>-->
<!--</g>-->
<rect x="9.42798" y="23.8125" width="4.75962" height="4.75962" rx="0.818182" fill="white"/>
<rect x="8" y="22.3846" width="7.61538" height="7.61538" rx="1.63636" stroke="white" stroke-width="0.818182"/>
<rect x="23.8125" y="9.42786" width="4.75962" height="4.75962" rx="0.818182" fill="white"/>
<rect x="22.3845" y="8" width="7.61538" height="7.61538" rx="1.63636" stroke="white" stroke-width="0.818182"/>
<path d="M15.1924 22.8076L22.8078 15.1923" stroke="white" stroke-dasharray="1.64 1.64"/>
</g>
<defs>
<filter id="filter0_b_13133_27192" x="-55" y="-55" width="148.957" height="148.894" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="27.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_13133_27192"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_13133_27192" result="shape"/>
</filter>
<clipPath id="clip0_13133_27192">
<rect x="-375" y="-377" width="428" height="1809" rx="20" fill="white"/>
</clipPath>
</defs>
</svg>
